name: Node.js SDK check

on:
  push:
    branches:
      - 'main'
  pull_request:
    paths:
      - 'packages/core/**'
      - 'packages/sdk/typescript/human-protocol-sdk/**'
  workflow_dispatch:

jobs:
  node-sdk-test:
    name: Node.js SDK Test
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - run: npm install --global yarn && yarn --ignore-scripts
        name: Install dependencies
      - run: yarn build
        name: Build core package
        working-directory: ./packages/core
      - run: yarn workspace @human-protocol/sdk test
        name: Run Node.js SDK test
