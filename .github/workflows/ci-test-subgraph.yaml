name: Subgraph check

on:
  push:
    branches:
      - 'main'
  pull_request:
    paths:
      - 'packages/core/**'
      - 'packages/sdk/typescript/subgraph/**'
  workflow_dispatch:

jobs:
  subgraph-test:
    name: Subgraph Test
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - run: npm install --global yarn && yarn
        name: Install dependencies
      - run: yarn workspace @human-protocol/subgraph test
        name: Run subgraph test
