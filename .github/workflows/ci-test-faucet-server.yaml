name: Faucet server check

on:
  push:
    branches:
      - 'main'
  pull_request:
    paths:
      - 'packages/core/**'
      - 'packages/sdk/typescript/human-protocol-sdk/**'
      - 'packages/apps/faucet-server/**'
  workflow_dispatch:

jobs:
  faucet-server-test:
    name: Faucet Server Test
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - run: npm install --global yarn && yarn
        name: Install dependencies
      - run: cp .env.example .env
        name: Create .env file
        working-directory: packages/apps/faucet-server
      - run: yarn workspace @human-protocol/faucet-server test
        name: Run faucet-server test
