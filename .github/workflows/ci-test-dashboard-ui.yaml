name: Dashboard UI check

on:
  push:
    branches:
      - 'main'
  pull_request:
    paths:
      - 'packages/core/**'
      - 'packages/sdk/typescript/human-protocol-sdk/**'
      - 'packages/sdk/typescript/subgraph/**'
      - 'packages/apps/dashboard/ui/**'
  workflow_dispatch:

jobs:
  dashboard-test:
    name: Dashboard UI Test
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - run: npm install --global yarn && yarn
        name: Install dependencies
      - run: yarn workspace @human-protocol/dashboard-ui test
        name: Run dashboard-ui test
