name: Protocol check

on:
  push:
    branches:
      - 'main'
  pull_request:
    paths:
      - 'packages/core/**'
  workflow_dispatch:

jobs:
  core-test:
    name: Core Test
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - run: npm install --global yarn && yarn --ignore-scripts
        name: Install dependencies
      - run: yarn workspace @human-protocol/core test
        name: Run protocol test
