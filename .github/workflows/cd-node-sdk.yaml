name: Node.js SDK NPM publish

on:
  release:
    types: [published]
  workflow_dispatch:
    inputs:
      release_version:
        description: 'Release version to use'
        required: true

jobs:
  node-sdk-publish:
    name: Node.js SDK NPM Publish
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - run: yarn --ignore-scripts
        name: Install dependencies
      - run: yarn build
        name: Build core package
        working-directory: ./packages/core
      - name: Change Node.js SDK version
        uses: jossef/action-set-json-field@v2
        if: ${{ github.event_name != 'workflow_dispatch' }}
        with:
          file: ./packages/sdk/typescript/human-protocol-sdk/package.json
          field: version
          value: ${{ github.event.release.tag_name }}
      - name: Change Node.js SDK version
        uses: jossef/action-set-json-field@v2
        if: ${{ github.event_name == 'workflow_dispatch' }}
        with:
          file: ./packages/sdk/typescript/human-protocol-sdk/package.json
          field: version
          value: ${{ github.event.inputs.release_version }}
      - run: yarn build
        name: Build SDK package
        working-directory: ./packages/sdk/typescript/human-protocol-sdk
      - uses: JS-DevTools/npm-publish@v3
        name: Publish
        with:
          package: ./packages/sdk/typescript/human-protocol-sdk/package.json
          access: public
          token: ${{ secrets.NPM_TOKEN }}
