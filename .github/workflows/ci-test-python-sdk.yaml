name: Python SDK check

on:
  push:
    branches:
      - 'main'
  pull_request:
    paths:
      - 'packages/core/**'
      - 'packages/sdk/python/human-protocol-sdk/**'
  workflow_dispatch:

jobs:
  python-test:
    name: Python Test
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - name: Install Node
        run: yarn --ignore-scripts
      - run: yarn build
        name: Build core package
        working-directory: ./packages/core
      - name: Set up Python 3.10
        uses: actions/setup-python@v5
        with:
          python-version: '3.10'
      - name: Install pipenv
        run: pip install pipenv
      - name: Python test
        working-directory: ./packages/sdk/python/human-protocol-sdk
        run: |
          pipenv install --dev
          make run-test
