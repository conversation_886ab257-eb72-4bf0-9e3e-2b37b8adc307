name: Subgraph deployment

on:
  workflow_dispatch:
    inputs:
      label:
        description: 'New version label'
        required: true

jobs:
  subgraph:
    name: Deploy Subgraph
    runs-on: ubuntu-latest
    strategy:
      matrix:
        network:
          - name: amoy
          - name: avalanche
          - name: bsc-testnet
          - name: bsc
          - name: celo-alfajores
          - name: celo
          - name: ethereum
          - name: fuji
          - name: moonbase-alpha
          - name: moonbeam
          - name: polygon
          - name: sepolia
          - name: xlayer-testnet
          - name: xlayer
      fail-fast: true
      max-parallel: 3
    steps:
      - uses: actions/checkout@v4
      - run: npm install --global yarn && yarn --ignore-scripts
        name: Install dependencies
      - run: yarn build
        name: Build core package
        working-directory: ./packages/core
      - run: yarn global add @graphprotocol/graph-cli
        name: Install Graph CLI
      - run: graph auth --studio ${API_KEY}
        name: Authenticate Graph CLI
        env:
          API_KEY: ${{ secrets.HP_GRAPH_API_KEY }}
      - run: yarn generate && yarn build
        name: Generate and build Subgraph
        working-directory: ./packages/sdk/typescript/subgraph
        env:
          NETWORK: ${{ matrix.network.name }}
      - run: graph deploy --studio ${NETWORK} -l ${{ github.event.inputs.label }}
        name: Deploy Subgraph
        working-directory: ./packages/sdk/typescript/subgraph
        env:
          NETWORK: ${{ matrix.network.name }}
