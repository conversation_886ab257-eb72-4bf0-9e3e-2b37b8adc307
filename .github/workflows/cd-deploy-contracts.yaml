name: Deploy contracts

on:
  workflow_dispatch:
    inputs:
      network:
        description: 'network'
        required: true
      escrowFactory:
        description: 'deploy escrow factory'
        required: true
      staking:
        description: 'deploy staking'
        required: true
      rewardPool:
        description: 'deploy reward pool'
        required: true

jobs:
  deploy-contracts:
    name: Deploy contracts
    runs-on: ubuntu-latest
    environment: deploy-contracts
    env:
      ETH_SEPOLIA_TESTNET_URL: ${{ secrets.ETH_SEPOLIA_TESTNET_URL }}
      ETH_POLYGON_URL: ${{ secrets.ETH_POLYGON_URL }}
      ETH_POLYGON_AMOY_URL: ${{ secrets.ETH_POLYGON_AMOY_URL }}
      ETH_BSC_URL: ${{ secrets.ETH_BSC_URL }}
      ETH_BSC_TESTNET_URL: ${{ secrets.ETH_BSC_TESTNET_URL }}
      ETH_MOONBEAM_URL: ${{ secrets.ETH_MOONBEAM_URL }}
      ETH_MOONBASE_ALPHA_URL: ${{ secrets.ETH_MOONBASE_ALPHA_URL }}
      ETH_MAINNET_URL: ${{ secrets.ETH_MAINNET_URL }}
      ETH_FUJI_URL: ${{ secrets.ETH_FUJI_URL }}
      ETH_AVALANCHE_URL: ${{ secrets.ETH_AVALANCHE_URL }}
      ETHERSCAN_API_KEY: ${{ secrets.ETHERSCAN_API_KEY }}
      POLYGONSCAN_API_KEY: ${{ secrets.POLYGONSCAN_API_KEY }}
      BSC_API_KEY: ${{ secrets.BSC_API_KEY }}
      MOONSCAN_API_KEY: ${{ secrets.MOONSCAN_API_KEY }}
      AVALANCHE_API_KEY: ${{ secrets.AVALANCHE_API_KEY }}
    steps:
      - uses: actions/checkout@v4
        with:
          token: ${{ secrets.GH_TOKEN_CD_CONTRACTS }}
      - name: Install dependencies
        run: npm install --global yarn && yarn --ignore-scripts
      - run: yarn build
        name: Build core package
        working-directory: ./packages/core
      - name: Networks list
        id: networks
        run: |
          case ${{ github.event.inputs.network }} in
            "sepolia")
              echo "escrow_factory=******************************************" >> $GITHUB_OUTPUT
              echo "staking=******************************************" >> $GITHUB_OUTPUT
              echo "reward_pool=******************************************" >> $GITHUB_OUTPUT
              echo "private_key=TESTNET_PRIVATE_KEY" >> $GITHUB_OUTPUT
            ;;
            "polygon")
              echo "escrow_factory=0xBDBfD2cC708199C5640C6ECdf3B0F4A4C67AdfcB" >> $GITHUB_OUTPUT
              echo "staking=0xcbAd56bE3f504E98bd70875823d3CC0242B7bB29" >> $GITHUB_OUTPUT
              echo "reward_pool=0x1371057BAec59944B924A7963F2EeCF43ff94CE4" >> $GITHUB_OUTPUT
              echo "private_key=MAINNET_PRIVATE_KEY" >> $GITHUB_OUTPUT
            ;;
            "polygonAmoy")
              echo "escrow_factory=******************************************" >> $GITHUB_OUTPUT
              echo "staking=0xCc0AF0635aa19fE799B6aFDBe28fcFAeA7f00a60" >> $GITHUB_OUTPUT
              echo "reward_pool=0xd866bCEFf6D0F77E1c3EAE28230AE6C79b03fDa7" >> $GITHUB_OUTPUT
              echo "private_key=TESTNET_PRIVATE_KEY" >> $GITHUB_OUTPUT
            ;;
            "bsc")
              echo "escrow_factory=0x92FD968AcBd521c232f5fB8c33b342923cC72714" >> $GITHUB_OUTPUT
              echo "staking=0xdFbB79dC35a3A53741be54a2C9b587d6BafAbd1C" >> $GITHUB_OUTPUT
              echo "reward_pool=0xf376443BCc6d4d4D63eeC086bc4A9E4a83878e0e" >> $GITHUB_OUTPUT
              echo "private_key=MAINNET_PRIVATE_KEY" >> $GITHUB_OUTPUT
            ;;
            "bscTestnet")
              echo "escrow_factory=0x2bfA592DBDaF434DDcbb893B1916120d181DAD18" >> $GITHUB_OUTPUT
              echo "staking=0x5517fE916Fe9F8dB15B0DDc76ebDf0BdDCd4ed18" >> $GITHUB_OUTPUT
              echo "reward_pool=0xB0A0500103eCEc431b73F6BAd923F0a2774E6e29" >> $GITHUB_OUTPUT
              echo "private_key=TESTNET_PRIVATE_KEY" >> $GITHUB_OUTPUT
            ;;
            "moonbeam")
              echo "escrow_factory=0xD9c75a1Aa4237BB72a41E5E26bd8384f10c1f55a" >> $GITHUB_OUTPUT
              echo "staking=0x05398211bA2046E296fBc9a9D3EB49e3F15C3123" >> $GITHUB_OUTPUT
              echo "reward_pool=0x4A5963Dd6792692e9147EdC7659936b96251917a" >> $GITHUB_OUTPUT
              echo "private_key=MAINNET_PRIVATE_KEY" >> $GITHUB_OUTPUT
            ;;
            "moonbaseAlpha")
              echo "escrow_factory=0x5e622FF522D81aa426f082bDD95210BC25fCA7Ed" >> $GITHUB_OUTPUT
              echo "staking=0xBFC7009F3371F93F3B54DdC8caCd02914a37495c" >> $GITHUB_OUTPUT
              echo "reward_pool=0xf46B45Df3d956369726d8Bd93Ba33963Ab692920" >> $GITHUB_OUTPUT
              echo "private_key=TESTNET_PRIVATE_KEY" >> $GITHUB_OUTPUT
            ;;
            "mainnet")
              echo "escrow_factory=0xD9c75a1Aa4237BB72a41E5E26bd8384f10c1f55a" >> $GITHUB_OUTPUT
              echo "staking=0x05398211bA2046E296fBc9a9D3EB49e3F15C3123" >> $GITHUB_OUTPUT
              echo "reward_pool=0x4A5963Dd6792692e9147EdC7659936b96251917a" >> $GITHUB_OUTPUT
              echo "private_key=MAINNET_PRIVATE_KEY" >> $GITHUB_OUTPUT
            ;;
            "avalanche")
              echo "escrow_factory=0xD9c75a1Aa4237BB72a41E5E26bd8384f10c1f55a" >> $GITHUB_OUTPUT
              echo "staking=0x05398211bA2046E296fBc9a9D3EB49e3F15C3123" >> $GITHUB_OUTPUT
              echo "reward_pool=0x4A5963Dd6792692e9147EdC7659936b96251917a" >> $GITHUB_OUTPUT
              echo "private_key=MAINNET_PRIVATE_KEY" >> $GITHUB_OUTPUT
            ;;
            "avalancheFujiTestnet")
              echo "escrow_factory=0x56C2ba540726ED4f46E7a134b6b9Ee9C867FcF92" >> $GITHUB_OUTPUT
              echo "staking=0x9890473B0b93E24d6D1a8Dfb739D577C6f25FFd3" >> $GITHUB_OUTPUT
              echo "reward_pool=0x5517fE916Fe9F8dB15B0DDc76ebDf0BdDCd4ed18" >> $GITHUB_OUTPUT
              echo "private_key=TESTNET_PRIVATE_KEY" >> $GITHUB_OUTPUT
            ;;
            *)
              echo "Invalid network"
              exit 1
              ;;
          esac
      - name: Upgrade Proxies
        if: github.event.inputs.escrowFactory == 'true' || github.event.inputs.staking == 'true' || github.event.inputs.rewardPool== 'true'
        run: yarn upgrade:proxy --network ${{ github.event.inputs.network }}
        working-directory: ./packages/core
        env:
          PRIVATE_KEY: ${{ secrets[steps.networks.outputs.private_key] }}
          DEPLOY_ESCROW_FACTORY: ${{ github.event.inputs.escrowFactory }}
          DEPLOY_STAKING: ${{ github.event.inputs.staking }}
          DEPLOY_REWARD_POOL: ${{ github.event.inputs.rewardPool }}
          ESCROW_FACTORY_ADDRESS: ${{ steps.networks.outputs.escrow_factory }}
          STAKING_ADDRESS: ${{ steps.networks.outputs.staking }}
          REWARD_POOL_ADDRESS: ${{ steps.networks.outputs.reward_pool }}

      - name: Verify Escrow Factory Proxy
        if: always() && github.event.inputs.escrowFactory == 'true'
        run: npx hardhat verify --network ${{ github.event.inputs.network }} ${{ steps.networks.outputs.escrow_factory }}
        working-directory: ./packages/core
      - name: Verify Staking Proxy
        if: always() && github.event.inputs.staking == 'true'
        run: npx hardhat verify --network ${{ github.event.inputs.network }} ${{ steps.networks.outputs.staking }}
        working-directory: ./packages/core
      - name: Verify Reward Pool Proxy
        if: always() && github.event.inputs.rewardPool== 'true'
        run: npx hardhat verify --network ${{ github.event.inputs.network }} ${{ steps.networks.outputs.reward_pool }}
        working-directory: ./packages/core
        #Commit changes to develop
      - name: Check for Changes
        if: always()
        id: check_changes
        run: |
          git fetch
          if [[ -n "$(git diff --name-only)" ]]; then
            echo "Changes detected."
            echo "::set-output name=changes::true"
          else
            echo "No changes detected."
            echo "::set-output name=changes::false"
          fi

      - name: stash
        if: always() && steps.check_changes.outputs.changes == 'true'
        run: |
          git status
          git stash --include-untracked
      - name: Checkout develop
        if: always() && steps.check_changes.outputs.changes == 'true'
        uses: actions/checkout@v4
        with:
          ref: develop
          token: ${{ secrets.GH_TOKEN_CD_CONTRACTS }}
      - name: pop
        if: always() && steps.check_changes.outputs.changes == 'true'
        run: |
          git stash pop
          git status
      - name: Commit changes
        if: always() && steps.check_changes.outputs.changes == 'true'
        uses: EndBug/add-and-commit@v9
        with:
          add: "['./packages/core/.openzeppelin']"
          message: 'Update upgrade file from CD'
          default_author: github_actions
          tag_push: '--force'
          github_token: ${{ secrets.GH_TOKEN_CD_CONTRACTS }}


