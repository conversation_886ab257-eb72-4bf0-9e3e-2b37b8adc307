name: Core NPM publish

on:
  release:
    types: [published]

jobs:
  core-publish:
    name: Core NPM Publish
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - run: yarn --ignore-scripts
        name: Install dependencies
      - run: yarn build
        name: Build core package
        working-directory: ./packages/core
      - name: Change core version
        uses: jossef/action-set-json-field@v2
        with:
          file: ./packages/core/package.json
          field: version
          value: ${{ github.event.release.tag_name }}
      - uses: JS-DevTools/npm-publish@v3
        with:
          package: ./packages/core/package.json
          access: public
          token: ${{ secrets.NPM_TOKEN }}
