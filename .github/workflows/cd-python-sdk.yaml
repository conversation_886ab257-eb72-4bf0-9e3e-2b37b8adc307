name: Python SDK publish

on:
  release:
    types: [published]
  workflow_dispatch:
    inputs:
      release_version:
        description: 'Release version to use'
        required: true

jobs:
  publish-python-sdk:
    name: Publish Python SDK
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - run: yarn --ignore-scripts
        name: Install dependencies
      - run: yarn build
        name: Build core package
        working-directory: ./packages/core
      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: "3.x"
      - name: Install dependencies
        working-directory: ./packages/sdk/python/human-protocol-sdk
        run: |
          python -m pip install --upgrade pip
          pip install setuptools==68.2.2 wheel twine
      - name: Set the release version
        uses: "<PERSON><PERSON>eeves/write-file-action@master"
        if: ${{ github.event_name != 'workflow_dispatch' }}
        with:
          path: ./packages/sdk/python/human-protocol-sdk/human_protocol_sdk/__init__.py
          write-mode: overwrite
          contents: |
            __version__ = "${{ github.event.release.tag_name }}"
      - name: Set the release version
        uses: "<PERSON><PERSON>eeves/write-file-action@master"
        if: ${{ github.event_name == 'workflow_dispatch' }}
        with:
          path: ./packages/sdk/python/human-protocol-sdk/human_protocol_sdk/__init__.py
          write-mode: overwrite
          contents: |
            __version__ = "${{ github.event.inputs.release_version }}"
      - name: Build and publish
        working-directory: ./packages/sdk/python/human-protocol-sdk
        env:
          TWINE_USERNAME: __token__
          TWINE_PASSWORD: ${{ secrets.PYPI_API_TOKEN }}
        run: |
          make publish-package
