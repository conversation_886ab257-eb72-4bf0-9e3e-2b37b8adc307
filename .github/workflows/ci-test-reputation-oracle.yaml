name: Reputation Oracle Check

on:
  push:
    branches:
      - 'main'
  pull_request:
    paths:
      - 'packages/core/**'
      - 'packages/sdk/typescript/human-protocol-sdk/**'
      - 'packages/apps/reputation-oracle/**'
  workflow_dispatch:

jobs:
  reputation-oracle-test:
    name: Reputation Oracle Test
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - run: npm install --global yarn && yarn
        name: Install dependencies
      - run: yarn workspace @human-protocol/reputation-oracle test
        name: Run reputation oracle test
