name: CVAT Recording Oracle Tests

on:
  push:
    paths:
      - 'packages/examples/cvat/recording-oracle/**'
      - 'packages/sdk/python/human-protocol-sdk/**'

jobs:
  cvat-exo-test:
    name: CVAT Test
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - name: CVAT Recording Oracle tests
        working-directory: ./packages/examples/cvat/recording-oracle
        run: docker compose -f docker-compose.test.yml up --attach test --exit-code-from test
