version: 2

updates:
  # Maintain dependencies for GitHub Actions
  - package-ecosystem: "github-actions"
    directory: "/"
    schedule:
      interval: "weekly"
    target-branch: "develop"

  # Maintain dependencies for npm
  - package-ecosystem: "npm"
    directory: "/"
    schedule:
      interval: "weekly"
    target-branch: "develop"

  # Maintain dependencies for pip
  - package-ecosystem: "pip"
    directory: "/"
    schedule:
      interval: "weekly"
    target-branch: "develop"

  # Maintain dependencies for rust
  - package-ecosystem: "cargo"
    directory: "/"
    schedule:
      interval: "weekly"
    target-branch: "develop"
