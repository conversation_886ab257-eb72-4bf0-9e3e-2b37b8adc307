"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
/* eslint-disable no-console */
const dotenv_1 = __importDefault(require("dotenv"));
dotenv_1.default.config({ path: `.env.${process.env.NODE_ENV}` });
const body_parser_1 = __importDefault(require("body-parser"));
const cors_1 = __importDefault(require("cors"));
const express_1 = __importDefault(require("express"));
const express_rate_limit_1 = __importDefault(require("express-rate-limit"));
const node_cache_1 = __importDefault(require("node-cache"));
const path_1 = __importDefault(require("path"));
const web3_1 = __importDefault(require("web3"));
const sdk_1 = require("@human-protocol/sdk");
const networks_1 = require("./constants/networks");
const web3_2 = require("./services/web3");
const slack_1 = require("./services/slack");
// init express
const app = (0, express_1.default)();
app.use((0, cors_1.default)());
app.use(body_parser_1.default.json());
app.use(body_parser_1.default.urlencoded({ extended: false }));
app.use(express_1.default.static(path_1.default.join(__dirname, '..', 'client', 'build')));
const port = process.env.APP_PORT;
// set up rate limiter: maximum of five requests per second
app.use((0, express_rate_limit_1.default)({
    windowMs: 1 * 1000,
    max: 5,
}));
// init cache
const blockList = new node_cache_1.default();
// init queue
const lastSend = [];
const getNetworkData = (chainId) => {
    return {
        ...sdk_1.NETWORKS[chainId],
        ...networks_1.FAUCET_NETWORKS[chainId],
    };
};
app.get('/stats', async (_request, response) => {
    const chainId = Number(_request.query.chainId);
    // Check for valid network
    const network = getNetworkData(chainId);
    if (!network)
        return response.status(200).json({
            status: false,
            message: 'Invalid Chain Id',
        });
    if (!network.rpcUrl?.length) {
        return response.status(200).json({
            status: false,
            message: 'Faucet is disabled',
        });
    }
    const web3 = (0, web3_2.getWeb3)(network.rpcUrl);
    response.send({
        account: web3.eth.defaultAccount,
        balance: await (0, web3_2.getFaucetBalance)(web3, network.hmtAddress),
        dailyLimit: process.env.DAILY_LIMIT,
    });
});
app.get('/queue', async (_request, response) => {
    response.send({ lastSend: lastSend });
});
app.post('/faucet', async (request, response) => {
    const chainId = request.body.chainId;
    const toAddress = request.body.address.replace(' ', '');
    // Check for valid network
    const network = getNetworkData(chainId);
    if (!network)
        return response.status(200).json({
            status: false,
            message: 'Invalid Chain Id',
        });
    if (!network.rpcUrl?.length) {
        return response.status(200).json({
            status: false,
            message: 'Faucet is disabled',
        });
    }
    // check for valid Eth address
    if (!web3_1.default.utils.isAddress(toAddress))
        return response.status(200).json({
            status: false,
            message: 'Your account address is invalid. Please check your account address (it should start with 0x).',
        });
    // extract ip
    let ipAddress = request.ip || request.socket.remoteAddress;
    if (!ipAddress)
        return response.status(200).json({
            status: false,
            message: 'Testnet ETH request fail. Please try again!',
        });
    ipAddress = ipAddress.replace(/\./g, '_');
    // check ip address availability
    if (blockList.has(ipAddress)) {
        const waitTime = Number(blockList.getTtl(ipAddress)) - Date.now();
        return response.status(200).json({
            status: false,
            message: `Your ip address has already requested testnet ETH today. The remaining time for next request is ${msToTime(waitTime)}`,
        });
    }
    // check tx address availability
    if (blockList.get(toAddress)) {
        const waitTime = Number(blockList.getTtl(toAddress)) - Date.now();
        return response.status(200).json({
            status: false,
            message: `Your wallet address has already requested testnet ETH today. The remaining time for next request is ${msToTime(waitTime)}.`,
        });
    }
    const web3 = (0, web3_2.getWeb3)(network.rpcUrl);
    // Check min HMT balance
    if ((await (0, web3_2.getHmtBalance)(web3, network.hmtAddress)) <
        BigInt(process.env.FAUCET_MIN_BALANCE)) {
        const message = `Low faucet balance detection in network ${network.title} with token address ${network.hmtAddress} and wallet address ${web3.eth.defaultAccount}`;
        (0, slack_1.sendSlackNotification)(message);
    }
    // Check min native balance
    if ((await web3.eth.getBalance(web3.eth.defaultAccount)) <
        BigInt(process.env.NATIVE_MIN_BALANCE)) {
        const message = `Low native balance detection in network ${network.title} with wallet address ${web3.eth.defaultAccount}`;
        (0, slack_1.sendSlackNotification)(message);
    }
    if (!(await (0, web3_2.checkFaucetBalance)(web3, network.hmtAddress))) {
        const message = `Faucet out of balance on ${network.title}`;
        (0, slack_1.sendSlackNotification)(message);
        return response.status(200).json({
            status: false,
            message: 'Faucet out of balance.',
        });
    }
    const txHash = await (0, web3_2.sendFunds)(web3, network.hmtAddress, toAddress);
    if (txHash) {
        lastSend.push({
            time: Date.now(),
            address: toAddress,
            txHash: txHash,
        });
        while (lastSend.length > 5)
            lastSend.shift();
    }
    if (Number(process.env.IP_WAITING_TIME) > 0)
        blockList.set(ipAddress, true, Number(process.env.IP_WAITING_TIME));
    if (Number(process.env.ADDRESS_WAITING_TIME) > 0)
        blockList.set(toAddress, true, Number(process.env.ADDRESS_WAITING_TIME));
    return response.status(200).json({
        status: true,
        message: `Requested successfully`,
        txHash: txHash,
    });
});
app.get('*', async (_request, response) => {
    response.sendFile(path_1.default.join(__dirname, '..', 'client', 'build', 'index.html'));
});
app.listen(port, () => {
    console.log(`Started on PORT ${port}`);
});
const msToTime = (duration) => {
    const seconds = Math.floor((duration / 1000) % 60), minutes = Math.floor((duration / (1000 * 60)) % 60), hours = Math.floor((duration / (1000 * 60 * 60)) % 24);
    const formattedTime = [
        hours.toString().padStart(2, '0'),
        minutes.toString().padStart(2, '0'),
        seconds.toString().padStart(2, '0'),
    ].join(':');
    return formattedTime;
};
