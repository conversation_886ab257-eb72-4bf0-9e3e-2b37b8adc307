"use strict";
/* eslint-disable @typescript-eslint/no-explicit-any */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
/**
 * TODO: Remove eslint rule for explict any after the PR is merged
 * Known issue for web3 v4 unknown ABI
 * https://github.com/web3/web3.js/pull/6636
 */
const dotenv_1 = __importDefault(require("dotenv"));
const HMToken_json_1 = __importDefault(require("@human-protocol/core/artifacts/contracts/HMToken.sol/HMToken.json"));
const globals_1 = require("@jest/globals");
const web3_1 = require("./web3");
const web3_2 = __importDefault(require("web3"));
dotenv_1.default.config({ path: `.env.example` });
let token;
const web3 = new web3_2.default('http://127.0.0.1:8549');
const owner = web3.eth.accounts.privateKeyToAccount(`0x${process.env.PRIVATE_KEY}`);
web3.eth.defaultAccount = owner.address;
const externalUser = '******************************************';
(0, globals_1.describe)('Faucet', () => {
    beforeEach(async () => {
        const tokenContract = new web3.eth.Contract(HMToken_json_1.default.abi);
        token = await tokenContract.deploy({
            data: HMToken_json_1.default.bytecode,
            arguments: [
                web3.utils.toWei('100000', 'ether'),
                'Human Token',
                18,
                'HMT',
            ],
        }).send({
            from: owner.address,
        });
    });
    (0, globals_1.it)('Check faucet balance before send', async () => {
        const result = await (0, web3_1.getFaucetBalance)(web3, token.options.address);
        (0, globals_1.expect)(result).toBe(web3.utils.toWei('100000', 'ether'));
    });
    (0, globals_1.it)('Check HMT balance', async () => {
        const result = await (0, web3_1.getHmtBalance)(web3, token.options.address);
        (0, globals_1.expect)(result).toBe(100000000000000000000000000000000000000000n);
    });
    (0, globals_1.it)('Send balance', async () => {
        const oldFaucetBalance = await (0, web3_1.getFaucetBalance)(web3, token.options.address);
        const oldUserBalance = await token.methods.balanceOf(externalUser).call();
        await (0, web3_1.sendFunds)(web3, token.options.address, externalUser);
        const newFaucetBalance = await (0, web3_1.getFaucetBalance)(web3, token.options.address);
        const newUserBalance = await token.methods.balanceOf(externalUser).call();
        (0, globals_1.expect)(Number(newFaucetBalance)).toBe(Number(oldFaucetBalance) - 10);
        (0, globals_1.expect)(oldUserBalance).toBe(web3.utils.toBigInt(newUserBalance) -
            web3.utils.toBigInt(web3.utils.toWei('10', 'ether')));
    });
    (0, globals_1.it)('Check balance', async () => {
        (0, globals_1.expect)(await (0, web3_1.checkFaucetBalance)(web3, token.options.address)).toBeTruthy();
        await token.methods.transfer(externalUser, await token.methods.balanceOf(owner.address).call()).send({ from: owner.address });
        (0, globals_1.expect)(await (0, web3_1.checkFaucetBalance)(web3, token.options.address)).toBeFalsy();
    });
    (0, globals_1.it)('Check min balance threshold for ERC20 token', async () => {
        const oldFaucetBalance = await (0, web3_1.getFaucetBalance)(web3, token.options.address);
        const oldUserBalance = await token.methods.balanceOf(externalUser).call();
        await (0, web3_1.sendFunds)(web3, token.options.address, externalUser);
        const newFaucetBalance = await (0, web3_1.getFaucetBalance)(web3, token.options.address);
        const newUserBalance = await token.methods.balanceOf(externalUser).call();
        (0, globals_1.expect)(Number(newFaucetBalance)).toBe(Number(oldFaucetBalance) - 10);
        (0, globals_1.expect)(oldUserBalance).toBe(web3.utils.toBigInt(newUserBalance) -
            web3.utils.toBigInt(web3.utils.toWei('10', 'ether')));
    });
});
