"use strict";
/* eslint-disable @typescript-eslint/no-explicit-any */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.checkFaucetBalance = exports.sendFunds = exports.getNativeBalance = exports.getHmtBalance = exports.getFaucetBalance = exports.getWeb3 = void 0;
/**
 * TODO: Remove eslint rule for explict any after the PR is merged
 * Known issue for web3 v4 unknown ABI
 * https://github.com/web3/web3.js/pull/6636
 */
const HMToken_json_1 = __importDefault(require("@human-protocol/core/abis/HMToken.json"));
const web3_1 = __importDefault(require("web3"));
const getWeb3 = (rpcUrl) => {
    const web3 = new web3_1.default(rpcUrl);
    const faucetAccount = web3.eth.accounts.privateKeyToAccount(`0x${process.env.PRIVATE_KEY}`);
    web3.eth.accounts.wallet.add(faucetAccount);
    web3.eth.defaultAccount = faucetAccount.address;
    return web3;
};
exports.getWeb3 = getWeb3;
const getFaucetBalance = async (web3, hmtAddress) => {
    const balance = web3.utils.fromWei(await (0, exports.getHmtBalance)(web3, hmtAddress), 'ether');
    return balance;
};
exports.getFaucetBalance = getFaucetBalance;
const getHmtBalance = async (web3, hmtAddress) => {
    const HMT = new web3.eth.Contract(HMToken_json_1.default, hmtAddress);
    return HMT.methods.balanceOf(web3.eth.defaultAccount).call();
};
exports.getHmtBalance = getHmtBalance;
const getNativeBalance = async (web3) => {
    return await web3.eth.getBalance(web3.eth.defaultAccount);
};
exports.getNativeBalance = getNativeBalance;
const sendFunds = async (web3, hmtAddress, toAddress) => {
    const HMT = new web3.eth.Contract(HMToken_json_1.default, hmtAddress);
    let txHash = '';
    try {
        const gasNeeded = await HMT.methods.transfer(toAddress, web3_1.default.utils.toWei(process.env.DAILY_LIMIT, 'ether')).estimateGas({ from: web3.eth.defaultAccount });
        const gasPrice = await web3.eth.getGasPrice();
        const receipt = await HMT.methods.transfer(toAddress, web3_1.default.utils.toWei(process.env.DAILY_LIMIT, 'ether')).send({
            from: web3.eth.defaultAccount,
            gas: gasNeeded.toString(),
            gasPrice: gasPrice.toString(),
        });
        txHash = receipt.transactionHash;
    }
    catch (err) {
        // eslint-disable-next-line no-console
        console.log(err);
        return undefined;
    }
    return txHash;
};
exports.sendFunds = sendFunds;
const checkFaucetBalance = async (web3, hmtAddress) => {
    if ((await (0, exports.getFaucetBalance)(web3, hmtAddress)) < process.env.DAILY_LIMIT)
        return false;
    const HMT = new web3.eth.Contract(HMToken_json_1.default, hmtAddress);
    const gasNeeded = await HMT.methods.transfer(web3.eth.defaultAccount, web3_1.default.utils.toWei(process.env.DAILY_LIMIT, 'ether')).estimateGas({ from: web3.eth.defaultAccount });
    const gasPrice = await web3.eth.getGasPrice();
    const balance = await web3.eth.getBalance(web3.eth.defaultAccount);
    if (balance < web3.utils.toBigInt(gasNeeded) * gasPrice)
        return false;
    return true;
};
exports.checkFaucetBalance = checkFaucetBalance;
