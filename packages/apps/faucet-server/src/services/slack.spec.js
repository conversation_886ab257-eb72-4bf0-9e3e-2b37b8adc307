"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.MOCK_SLACK_WEBHOOK_URL = void 0;
const axios_1 = __importDefault(require("axios"));
const slack_1 = require("./slack");
exports.MOCK_SLACK_WEBHOOK_URL = 'https://slack.com/webhook';
jest.mock('axios');
describe('sendSlackNotification', () => {
    beforeEach(async () => {
        jest.clearAllMocks();
    });
    it('should send Slack notification with the provided message', async () => {
        const mockResponse = { data: 'mockResponseData' };
        axios_1.default.post.mockResolvedValue(mockResponse);
        process.env.SLACK_WEBHOOK_URL = exports.MOCK_SLACK_WEBHOOK_URL;
        const message = 'Test message';
        await (0, slack_1.sendSlackNotification)(message);
        expect(axios_1.default.post).toHaveBeenCalledWith(exports.MOCK_SLACK_WEBHOOK_URL, {
            text: message,
        });
    });
    it('should handle error when sending Slack notification', async () => {
        const mockError = new Error('Mock error');
        axios_1.default.post.mockRejectedValue(mockError);
        process.env.SLACK_WEBHOOK_URL = exports.MOCK_SLACK_WEBHOOK_URL;
        const message = 'Test message';
        await (0, slack_1.sendSlackNotification)(message);
        expect(axios_1.default.post).toHaveBeenCalledWith(exports.MOCK_SLACK_WEBHOOK_URL, {
            text: message,
        });
    });
    it('should handle missing slackWebhookUrl', async () => {
        delete process.env.SLACK_WEBHOOK_URL;
        const message = 'Test message';
        await (0, slack_1.sendSlackNotification)(message);
        expect(axios_1.default.post).toHaveBeenCalledTimes(0);
    });
});
