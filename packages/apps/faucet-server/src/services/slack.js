"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.sendSlackNotification = void 0;
const axios_1 = __importDefault(require("axios"));
function sendSlackNotification(message) {
    const slackWebhookUrl = process.env.SLACK_WEBHOOK_URL;
    if (!slackWebhookUrl) {
        console.log('Slack webhook URL is not provided');
        return;
    }
    const payload = {
        text: message,
    };
    axios_1.default
        .post(slackWebhookUrl, payload)
        .then(() => {
        console.log('Slack notification sent:', payload);
    })
        .catch((error) => {
        console.log('Error sending Slack notification:', error);
    });
}
exports.sendSlackNotification = sendSlackNotification;
