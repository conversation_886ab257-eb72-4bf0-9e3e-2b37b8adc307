"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.FAUCET_NETWORKS = void 0;
const sdk_1 = require("@human-protocol/sdk");
exports.FAUCET_NETWORKS = {
    [sdk_1.ChainId.SEPOLIA]: {
        rpcUrl: process.env.RPC_URL_SEPOLIA || '',
    },
    [sdk_1.ChainId.BSC_TESTNET]: {
        rpcUrl: process.env.RPC_URL_BSC_TESTNET || '',
    },
    [sdk_1.ChainId.POLYGON_AMOY]: {
        rpcUrl: process.env.RPC_URL_POLYGON_AMOY || '',
    },
    [sdk_1.ChainId.MOONBASE_ALPHA]: {
        rpcUrl: process.env.RPC_URL_MOONBASE_ALPHA || '',
    },
    [sdk_1.ChainId.AVALANCHE_TESTNET]: {
        rpcUrl: process.env.RPC_URL_AVALANCHE_TESTNET || '',
    },
    [sdk_1.ChainId.CELO_ALFAJORES]: {
        rpcUrl: process.env.RPC_URL_CELO_ALFAJORES || '',
    },
    [sdk_1.ChainId.XLAYER_TESTNET]: {
        rpcUrl: process.env.RPC_URL_XLAYER_TESTNET || '',
    },
    [sdk_1.ChainId.LOCALHOST]: {
        rpcUrl: process.env.RPC_PORT
            ? `http://127.0.0.1:${process.env.RPC_PORT}`
            : '',
    },
};
