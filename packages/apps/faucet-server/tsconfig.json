{"compilerOptions": {"target": "es2020", "module": "commonjs", "moduleResolution": "node", "pretty": true, "noEmitOnError": true, "strict": true, "resolveJsonModule": true, "removeComments": true, "noUnusedLocals": true, "noFallthroughCasesInSwitch": true, "useDefineForClassFields": true, "forceConsistentCasingInFileNames": true, "skipLibCheck": true, "esModuleInterop": true, "outDir": "build", "lib": ["es2020"], "useUnknownInCatchVariables": false, "strictNullChecks": false, "allowJs": true}}