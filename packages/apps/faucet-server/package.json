{"name": "@human-protocol/faucet-server", "version": "1.0.0", "description": "Human Protocol faucet for private Ethereum testnet.", "main": "src/index.ts", "scripts": {"build": "tsc", "lint": "eslint src", "test": "concurrently -k -s first --hide 0 \"hardhat node --port 8549\" \"jest\"", "start": "ts-node src/index.ts", "vercel-build": "yarn workspace @human-protocol/sdk build"}, "author": "", "license": "ISC", "dependencies": {"@human-protocol/sdk": "*", "axios": "^1.3.4", "body-parser": "^1.20.0", "cors": "^2.8.5", "express": "^4.21.0", "express-rate-limit": "^7.3.0", "node-cache": "^5.1.2", "web3": "^4.12.1"}, "devDependencies": {"@types/cors": "^2.8.17", "@types/express": "^4.17.14"}}