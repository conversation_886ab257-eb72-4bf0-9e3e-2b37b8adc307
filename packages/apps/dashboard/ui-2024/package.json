{"private": "true", "name": "@human-protocol/dashboard-ui-2024", "version": "1.0.0", "description": "Human Protocol Dashboard", "license": "MIT", "type": "module", "scripts": {"start": "vite", "test": "vitest -u", "build": "tsc && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"@emotion/react": "^11.11.4", "@emotion/styled": "^11.11.5", "@mui/icons-material": "^6.1.1", "@mui/material": "^5.15.18", "@mui/styled-engine-sc": "6.0.0-alpha.18", "@mui/x-date-pickers": "^7.5.0", "@tanstack/react-query": "^5.48.0", "@types/react-router-dom": "^5.3.3", "@types/recharts": "^1.8.29", "axios": "^1.7.2", "clsx": "^2.1.1", "dayjs": "^1.11.11", "node-sass": "^9.0.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-number-format": "^5.4.0", "react-router-dom": "^6.23.1", "recharts": "^2.13.0-alpha.4", "simplebar-react": "^3.2.5", "styled-components": "^6.1.11", "swiper": "^11.1.3", "use-debounce": "^10.0.2", "zod": "^3.23.8", "zustand": "^4.5.4"}, "devDependencies": {"@types/react": "^18.2.66", "@types/react-dom": "^18.2.22", "@typescript-eslint/eslint-plugin": "^7.2.0", "@typescript-eslint/parser": "^7.2.0", "@vitejs/plugin-react": "^4.2.1", "eslint": "^8.57.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.11", "prettier": "3.2.5", "sass": "^1.78.0", "stylelint-prettier": "^5.0.0", "typescript": "^5.2.2", "vite": "^5.4.7", "vite-plugin-svgr": "^4.2.0"}}