<!doctype html>
<html lang="en">
	<head>
		<meta charset="UTF-8" />

		<link
			rel="icon"
			href="data:image/png;base64,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"
		/>
		<link rel="preconnect" href="https://fonts.googleapis.com" />
		<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
		<link
			href="https://fonts.googleapis.com/css2?family=Inter:ital,opsz,wght@0,14..32,100..900;1,14..32,100..900&family=Roboto:ital,wght@0,100;0,300;0,400;0,500;0,700;0,900;1,100;1,300;1,400;1,500;1,700;1,900&display=swap"
			rel="stylesheet"
		/>
		<meta name="viewport" content="width=device-width, initial-scale=1.0" />
		<title>HUMAN Dashboard</title>
	</head>
	<body>
		<div id="root"></div>
		<script type="module" src="/src/main.tsx"></script>
	</body>
</html>
