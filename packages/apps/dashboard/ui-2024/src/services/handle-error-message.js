"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.handleErrorMessage = void 0;
const axios_1 = require("axios");
const zod_1 = require("zod");
function handleErrorMessage(unknownError) {
    if (unknownError instanceof axios_1.AxiosError) {
        return unknownError.message;
    }
    if (unknownError instanceof zod_1.ZodError) {
        return 'Unexpected data error';
    }
    if (unknownError instanceof Error) {
        return unknownError.message;
    }
    return 'Something went wrong';
}
exports.handleErrorMessage = handleErrorMessage;
