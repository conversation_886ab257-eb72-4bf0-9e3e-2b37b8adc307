"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.httpService = void 0;
const axios_1 = __importDefault(require("axios"));
const env_1 = require("../helpers/env");
exports.httpService = axios_1.default.create({
    baseURL: env_1.env.VITE_API_URL,
});
