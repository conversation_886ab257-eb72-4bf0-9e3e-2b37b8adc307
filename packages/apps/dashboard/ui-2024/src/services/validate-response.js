"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.validateResponse = void 0;
const zod_1 = require("zod");
const validateResponse = (object, zodObject) => {
    try {
        const data = zodObject.parse(object);
        return data;
    }
    catch (error) {
        console.error('Unexpected response');
        if (error instanceof zod_1.ZodError) {
            error.issues.forEach((issue) => {
                console.log(issue);
            });
        }
        console.error(error);
        throw error;
    }
};
exports.validateResponse = validateResponse;
