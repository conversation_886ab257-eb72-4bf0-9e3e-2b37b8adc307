"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.useLeaderboardAllDetails = void 0;
const react_query_1 = require("@tanstack/react-query");
const http_service_1 = require("../http-service");
const api_paths_1 = require("../api-paths");
const validate_response_1 = require("@services/validate-response");
const use_leaderboard_search_1 = require("@utils/hooks/use-leaderboard-search");
const use_leaderboard_details_1 = require("@services/api/use-leaderboard-details");
function useLeaderboardAllDetails() {
    const { filterParams: { chainId }, } = (0, use_leaderboard_search_1.useLeaderboardSearch)();
    return (0, react_query_1.useQuery)({
        queryFn: async () => {
            const { data } = await http_service_1.httpService.get(api_paths_1.apiPaths.leaderboardDetailsAll.path, {
                params: { chainId: chainId === -1 ? undefined : chainId },
            });
            const validResponse = (0, validate_response_1.validateResponse)(data, use_leaderboard_details_1.leaderBoardSuccessResponseSchema);
            return validResponse;
        },
        queryKey: ['useLeaderboardAllDetails', chainId],
    });
}
exports.useLeaderboardAllDetails = useLeaderboardAllDetails;
