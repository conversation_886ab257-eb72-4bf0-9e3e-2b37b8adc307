"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.useLeaderboardDetails = exports.leaderBoardSuccessResponseSchema = exports.reputationSchema = void 0;
const react_query_1 = require("@tanstack/react-query");
const zod_1 = require("zod");
const http_service_1 = require("../http-service");
const api_paths_1 = require("../api-paths");
const validate_response_1 = require("@services/validate-response");
const use_leaderboard_search_1 = require("@utils/hooks/use-leaderboard-search");
exports.reputationSchema = zod_1.z.unknown().transform((value) => {
    try {
        const knownReputation = zod_1.z
            .union([zod_1.z.literal('Low'), zod_1.z.literal('Medium'), zod_1.z.literal('High')])
            .parse(value);
        return knownReputation;
    }
    catch (error) {
        return 'Unknown';
    }
});
const leaderBoardEntity = zod_1.z.object({
    address: zod_1.z.string(),
    role: zod_1.z.string(),
    amountStaked: zod_1.z.string().transform((value, ctx) => {
        const valueAsNumber = Number(value);
        if (Number.isNaN(valueAsNumber)) {
            ctx.addIssue({
                path: ['amountStaked'],
                code: zod_1.z.ZodIssueCode.custom,
            });
        }
        return valueAsNumber / 10 ** 18;
    }),
    reputation: exports.reputationSchema,
    fee: zod_1.z.number(),
    jobTypes: zod_1.z.array(zod_1.z.string()),
    url: zod_1.z.string(),
    chainId: zod_1.z.number(),
});
exports.leaderBoardSuccessResponseSchema = zod_1.z.array(leaderBoardEntity);
function useLeaderboardDetails() {
    const { filterParams: { chainId }, } = (0, use_leaderboard_search_1.useLeaderboardSearch)();
    return (0, react_query_1.useQuery)({
        queryFn: async () => {
            const { data } = await http_service_1.httpService.get(api_paths_1.apiPaths.leaderboardDetails.path, {
                params: { chainId: chainId === -1 ? undefined : chainId },
            });
            const validResponse = (0, validate_response_1.validateResponse)(data, exports.leaderBoardSuccessResponseSchema);
            return validResponse;
        },
        queryKey: ['useLeaderboardDetails', chainId],
    });
}
exports.useLeaderboardDetails = useLeaderboardDetails;
