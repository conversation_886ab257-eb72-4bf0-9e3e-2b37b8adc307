"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.useGraphPageChartData = void 0;
const react_query_1 = require("@tanstack/react-query");
const zod_1 = require("zod");
const http_service_1 = require("../http-service");
const api_paths_1 = require("../api-paths");
const validate_response_1 = require("@services/validate-response");
const use_graph_page_chart_params_1 = require("@utils/hooks/use-graph-page-chart-params");
const use_debounce_1 = require("use-debounce");
const react_1 = require("react");
const dayjs_1 = __importDefault(require("dayjs"));
const hmtDailyStatSchemaResponseSchema = zod_1.z.object({
    from: zod_1.z.string().optional(),
    to: zod_1.z.string().optional(),
    results: zod_1.z.array(zod_1.z.object({
        totalTransactionAmount: zod_1.z.string().transform((value, ctx) => {
            const valueAsNumber = Number(value);
            if (Number.isNaN(valueAsNumber)) {
                ctx.addIssue({
                    path: ['totalTransactionAmount'],
                    code: zod_1.z.ZodIssueCode.custom,
                });
            }
            return valueAsNumber / 10 ** 18;
        }),
        totalTransactionCount: zod_1.z.number(),
        dailyUniqueSenders: zod_1.z.number(),
        dailyUniqueReceivers: zod_1.z.number(),
        date: zod_1.z.string(),
    })),
});
const hcaptchaDailyStatsResponseSchema = zod_1.z.object({
    from: zod_1.z.string().optional(),
    to: zod_1.z.string().optional(),
    results: zod_1.z.array(zod_1.z.object({
        solved: zod_1.z.number(),
        date: zod_1.z.string(),
    })),
});
const mergeResponses = (hcaptchaStatsResults, hmtStatsResults) => {
    const allDates = Array.from(new Set([
        ...hcaptchaStatsResults.map(({ date }) => date),
        ...hmtStatsResults.map(({ date }) => date),
    ])).sort((a, b) => ((0, dayjs_1.default)(a).isBefore((0, dayjs_1.default)(b)) ? -1 : 1));
    const hcaptchaStatsResultsMap = new Map();
    const hmtStatsResultsMap = new Map();
    hcaptchaStatsResults.forEach((entry) => {
        hcaptchaStatsResultsMap.set(entry.date, entry);
    });
    hmtStatsResults.forEach((entry) => {
        hmtStatsResultsMap.set(entry.date, entry);
    });
    return allDates.map((date) => {
        const hmtStatsEntry = hmtStatsResultsMap.get(date) || {
            dailyUniqueReceivers: 0,
            dailyUniqueSenders: 0,
            date: date,
            totalTransactionAmount: 0,
            totalTransactionCount: 0,
        };
        const hcaptchaStatsEntry = hcaptchaStatsResultsMap.get(date) || {
            date: date,
            solved: 0,
        };
        return { ...hmtStatsEntry, ...hcaptchaStatsEntry };
    });
};
const DEBOUNCE_MS = 300;
function useGraphPageChartData() {
    const { dateRangeParams, selectedTimePeriod, effectiveFromAllTimeDate, setEffectiveFromAllTimeDate, setFromDate, } = (0, use_graph_page_chart_params_1.useGraphPageChartParams)();
    const queryParams = (0, react_1.useMemo)(() => ({
        from: dateRangeParams.from.format('YYYY-MM-DD'),
        to: dateRangeParams.to.format('YYYY-MM-DD'),
    }), [dateRangeParams.from, dateRangeParams.to]);
    const [debouncedQueryParams] = (0, use_debounce_1.useDebounce)(queryParams, DEBOUNCE_MS);
    return (0, react_query_1.useQuery)({
        queryFn: async () => {
            const { data: hmtDailyStats } = await http_service_1.httpService.get(api_paths_1.apiPaths.hmtDailyStats.path, {
                params: debouncedQueryParams,
            });
            const { data: hcaptchDailyStats } = await http_service_1.httpService.get(api_paths_1.apiPaths.hcaptchaStatsDaily.path, {
                params: debouncedQueryParams,
            });
            const validHmtDailyStats = (0, validate_response_1.validateResponse)(hmtDailyStats, hmtDailyStatSchemaResponseSchema);
            const validHcaptchaGeneralStats = (0, validate_response_1.validateResponse)(hcaptchDailyStats, hcaptchaDailyStatsResponseSchema);
            const mergedResponses = mergeResponses(validHcaptchaGeneralStats.results, validHmtDailyStats.results);
            const latestDate = mergedResponses[0]?.date
                ? (0, dayjs_1.default)(new Date(mergedResponses[0]?.date))
                : null;
            const fromDateInLatestDateFormat = (0, dayjs_1.default)(new Date(dateRangeParams.from.format('YYYY-MM-DD')));
            if ((selectedTimePeriod === 'ALL' &&
                !effectiveFromAllTimeDate &&
                latestDate) ||
                (!effectiveFromAllTimeDate &&
                    latestDate?.isAfter(fromDateInLatestDateFormat))) {
                setEffectiveFromAllTimeDate(latestDate);
                setFromDate(latestDate);
            }
            return mergedResponses;
        },
        staleTime: DEBOUNCE_MS,
        queryKey: ['useGraphPageChartData', debouncedQueryParams],
        placeholderData: react_query_1.keepPreviousData,
    });
}
exports.useGraphPageChartData = useGraphPageChartData;
