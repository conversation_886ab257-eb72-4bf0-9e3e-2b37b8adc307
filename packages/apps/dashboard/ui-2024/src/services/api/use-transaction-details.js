"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.useTransactionDetails = void 0;
const react_query_1 = require("@tanstack/react-query");
const zod_1 = require("zod");
const http_service_1 = require("../http-service");
const api_paths_1 = require("../api-paths");
const use_wallet_search_1 = require("@utils/hooks/use-wallet-search");
const use_transactions_details_dto_1 = require("@utils/hooks/use-transactions-details-dto");
const validate_response_1 = require("@services/validate-response");
const transactionDetailsSuccessResponseSchema = zod_1.z.object({
    block: zod_1.z.number(),
    from: zod_1.z.string(),
    to: zod_1.z.string(),
    value: zod_1.z.string(),
    method: zod_1.z.string(),
    txHash: zod_1.z.string(),
});
const paginatedTransactionDetailsSuccessResponseSchema = zod_1.z.object({
    address: zod_1.z.string(),
    chainId: zod_1.z.number(),
    first: zod_1.z.number(),
    skip: zod_1.z.number(),
    results: zod_1.z.array(transactionDetailsSuccessResponseSchema),
});
function useTransactionDetails() {
    const { filterParams } = (0, use_wallet_search_1.useWalletSearch)();
    const { params } = (0, use_transactions_details_dto_1.useTransactionDetailsDto)();
    const dto = {
        chainId: filterParams.chainId,
        skip: params.skip,
        first: params.first,
    };
    return (0, react_query_1.useQuery)({
        queryFn: async () => {
            const { data } = await http_service_1.httpService.get(`${api_paths_1.apiPaths.transactionDetails.path}/${filterParams.address}`, {
                params: dto,
            });
            const validResponse = (0, validate_response_1.validateResponse)(data, paginatedTransactionDetailsSuccessResponseSchema);
            return validResponse;
        },
        queryKey: ['useTransactionDetails', filterParams.address, dto],
    });
}
exports.useTransactionDetails = useTransactionDetails;
