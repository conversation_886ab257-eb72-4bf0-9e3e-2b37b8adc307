"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.useAddressDetails = exports.Roles = void 0;
const react_query_1 = require("@tanstack/react-query");
const zod_1 = require("zod");
const http_service_1 = require("../http-service");
const api_paths_1 = require("../api-paths");
const use_wallet_search_1 = require("@utils/hooks/use-wallet-search");
const validate_response_1 = require("../../services/validate-response");
const use_leaderboard_details_1 = require("@services/api/use-leaderboard-details");
const transformOptionalTokenAmount = (value, ctx) => {
    if (value === undefined || value === null)
        return value;
    const valueAsNumber = Number(value);
    if (Number.isNaN(valueAsNumber)) {
        ctx.addIssue({
            path: ['amountStaked'],
            code: zod_1.z.ZodIssueCode.custom,
        });
    }
    return valueAsNumber / 10 ** 18;
};
const walletSchema = zod_1.z.object({
    chainId: zod_1.z.number(),
    address: zod_1.z.string(),
    balance: zod_1.z.string().transform(transformOptionalTokenAmount),
});
const escrowSchema = zod_1.z.object({
    chainId: zod_1.z.number().optional().nullable(),
    address: zod_1.z.string().optional().nullable(),
    balance: zod_1.z
        .string()
        .optional()
        .nullable()
        .transform(transformOptionalTokenAmount),
    token: zod_1.z.string().optional().nullable(),
    factoryAddress: zod_1.z.string().optional().nullable(),
    totalFundedAmount: zod_1.z
        .string()
        .optional()
        .nullable()
        .transform(transformOptionalTokenAmount),
    amountPaid: zod_1.z
        .string()
        .optional()
        .nullable()
        .transform(transformOptionalTokenAmount),
    status: zod_1.z.string().optional().nullable(),
    manifest: zod_1.z.string().optional().nullable(),
    launcher: zod_1.z.string().optional().nullable(),
    exchangeOracle: zod_1.z.string().optional().nullable(),
    recordingOracle: zod_1.z.string().optional().nullable(),
    reputationOracle: zod_1.z.string().optional().nullable(),
    finalResultsUrl: zod_1.z.string().nullable(),
});
var Roles;
(function (Roles) {
    Roles["jobLauncher"] = "Job Launcher";
    Roles["exchangeOracle"] = "Exchange Oracle";
    Roles["humanApp"] = "Human App";
    Roles["recordingOracle"] = "Recording Oracle";
    Roles["reputationOracle"] = "Reputation Oracle";
})(Roles = exports.Roles || (exports.Roles = {}));
const leaderSchema = zod_1.z.object({
    chainId: zod_1.z.number(),
    address: zod_1.z.string(),
    balance: zod_1.z.string().transform(transformOptionalTokenAmount),
    role: zod_1.z.nativeEnum(Roles).nullable(),
    amountStaked: zod_1.z.string().optional().transform(transformOptionalTokenAmount),
    amountAllocated: zod_1.z
        .string()
        .optional()
        .transform(transformOptionalTokenAmount),
    amountLocked: zod_1.z.string().optional().transform(transformOptionalTokenAmount),
    lockedUntilTimestamp: zod_1.z.string().optional(),
    reputation: use_leaderboard_details_1.reputationSchema,
    fee: zod_1.z.number(),
    jobTypes: zod_1.z.array(zod_1.z.string()).optional().nullable(),
    url: zod_1.z.string().optional().nullable(),
    reward: zod_1.z.string().optional(),
    amountJobsProcessed: zod_1.z.string(),
});
const addressDetailsResponseSchema = zod_1.z.object({
    wallet: zod_1.z.optional(walletSchema),
    escrow: zod_1.z.optional(escrowSchema),
    leader: zod_1.z.optional(leaderSchema),
});
function useAddressDetails() {
    const { filterParams } = (0, use_wallet_search_1.useWalletSearch)();
    return (0, react_query_1.useQuery)({
        queryFn: async () => {
            const address = filterParams.address || '0x0';
            const { data } = await http_service_1.httpService.get(`${api_paths_1.apiPaths.addressDetails.path}/${address}`, { params: { chainId: filterParams.chainId || -1 } });
            const validResponse = (0, validate_response_1.validateResponse)(data, addressDetailsResponseSchema);
            return validResponse;
        },
        queryKey: ['useAddressDetails', filterParams.address, filterParams.chainId],
    });
}
exports.useAddressDetails = useAddressDetails;
