"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.useHMTPrice = void 0;
const react_query_1 = require("@tanstack/react-query");
const zod_1 = require("zod");
const http_service_1 = require("../http-service");
const api_paths_1 = require("../api-paths");
const validate_response_1 = require("@services/validate-response");
const successHMTPriceResponseSchema = zod_1.z.object({
    hmtPrice: zod_1.z.number(),
});
function useHMTPrice() {
    return (0, react_query_1.useQuery)({
        queryFn: async () => {
            const { data } = await http_service_1.httpService.get(api_paths_1.apiPaths.statsHmtPrice.path);
            const validResponse = (0, validate_response_1.validateResponse)(data, successHMTPriceResponseSchema);
            return validResponse;
        },
        queryKey: ['useHMTPrice'],
    });
}
exports.useHMTPrice = useHMTPrice;
