"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.useHcaptchaGeneralStats = void 0;
const react_query_1 = require("@tanstack/react-query");
const zod_1 = require("zod");
const http_service_1 = require("../http-service");
const api_paths_1 = require("../api-paths");
const validate_response_1 = require("@services/validate-response");
const successHcaptchaGeneralStatsResponseSchema = zod_1.z.object({
    solved: zod_1.z.number(),
});
function useHcaptchaGeneralStats() {
    return (0, react_query_1.useQuery)({
        queryFn: async () => {
            const { data } = await http_service_1.httpService.get(api_paths_1.apiPaths.hcaptchaGeneralStats.path);
            const validResponse = (0, validate_response_1.validateResponse)(data, successHcaptchaGeneralStatsResponseSchema);
            return validResponse;
        },
        queryKey: ['useHcaptchaGeneralStats'],
    });
}
exports.useHcaptchaGeneralStats = useHcaptchaGeneralStats;
