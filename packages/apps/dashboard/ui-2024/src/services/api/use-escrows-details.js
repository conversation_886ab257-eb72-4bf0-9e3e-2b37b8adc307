"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.useEscrowDetails = void 0;
const react_query_1 = require("@tanstack/react-query");
const zod_1 = require("zod");
const http_service_1 = require("../http-service");
const api_paths_1 = require("../api-paths");
const use_wallet_search_1 = require("@utils/hooks/use-wallet-search");
const validate_response_1 = require("@services/validate-response");
const use_escrows_details_dto_1 = require("@utils/hooks/use-escrows-details-dto");
const escrowDetailsSuccessResponseSchema = zod_1.z.object({
    chainId: zod_1.z.number(),
    address: zod_1.z.string(),
    status: zod_1.z.string(),
});
const paginatedEscrowsDetailsSuccessResponseSchema = zod_1.z.object({
    address: zod_1.z.string(),
    chainId: zod_1.z.number(),
    first: zod_1.z.number(),
    skip: zod_1.z.number(),
    results: zod_1.z.array(escrowDetailsSuccessResponseSchema),
});
function useEscrowDetails({ role, }) {
    const { filterParams } = (0, use_wallet_search_1.useWalletSearch)();
    const { params } = (0, use_escrows_details_dto_1.useEscrowDetailsDto)();
    const dto = {
        chainId: filterParams.chainId,
        skip: params.skip,
        first: params.first,
        role,
    };
    return (0, react_query_1.useQuery)({
        queryFn: async () => {
            const { data } = await http_service_1.httpService.get(`${api_paths_1.apiPaths.escrowDetails.path}/${filterParams.address}`, {
                params: dto,
            });
            const validResponse = (0, validate_response_1.validateResponse)(data, paginatedEscrowsDetailsSuccessResponseSchema);
            return validResponse;
        },
        queryKey: ['useEscrowDetails', filterParams.address, dto],
    });
}
exports.useEscrowDetails = useEscrowDetails;
