"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const react_1 = __importDefault(require("react"));
const react_router_dom_1 = require("react-router-dom");
const Home_1 = __importDefault(require("@pages/Home"));
const Graph_1 = __importDefault(require("@pages/Graph"));
const SearchResults_1 = __importDefault(require("@pages/SearchResults"));
const Leaderboard_1 = require("@pages/Leaderboard");
const App = () => {
    return (<react_router_dom_1.BrowserRouter>
			<react_router_dom_1.Routes>
				<react_router_dom_1.Route path="/" element={<Home_1.default />}/>
				<react_router_dom_1.Route path="/leaderboard" element={<Leaderboard_1.LeaderBoard />}/>
				<react_router_dom_1.Route path="/graph" element={<Graph_1.default />}/>
				<react_router_dom_1.Route path="/search/:chainId/:address" element={<SearchResults_1.default />}/>
				<react_router_dom_1.Route path="*" element={<div>Not find</div>}/>
			</react_router_dom_1.Routes>
		</react_router_dom_1.BrowserRouter>);
};
exports.default = App;
