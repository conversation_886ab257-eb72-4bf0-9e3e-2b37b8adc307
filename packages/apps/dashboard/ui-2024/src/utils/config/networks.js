"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.networks = exports.getNetwork = void 0;
const chains = __importStar(require("viem/chains"));
const chainIdsList = [1, 56, 137, 1284, 43114, 42220, 196];
const viemChains = Object.values(chains);
const getNetwork = (chainId) => viemChains.find((network) => {
    if ('id' in network && network.id === chainId) {
        return network;
    }
});
exports.getNetwork = getNetwork;
exports.networks = chainIdsList
    .map((id) => (0, exports.getNetwork)(id))
    .filter((chain) => !!chain);
