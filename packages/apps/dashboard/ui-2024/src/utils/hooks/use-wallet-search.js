"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.useWalletSearch = void 0;
const zustand_1 = require("zustand");
exports.useWalletSearch = (0, zustand_1.create)((set) => ({
    filterParams: {
        address: '',
        chainId: 137,
    },
    setAddress: (address) => {
        set((state) => ({
            filterParams: {
                ...state.filterParams,
                address,
            },
        }));
    },
    setChainId: (chainId) => {
        set((state) => ({
            filterParams: {
                ...state.filterParams,
                chainId,
            },
        }));
    },
}));
