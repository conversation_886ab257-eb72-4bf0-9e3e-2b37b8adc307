"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.useTransactionDetailsDto = void 0;
const zustand_1 = require("zustand");
const INITIAL_PAGE_SIZE = 10;
exports.useTransactionDetailsDto = (0, zustand_1.create)((set) => ({
    params: {
        first: INITIAL_PAGE_SIZE,
        skip: 0,
    },
    pagination: {
        page: 0,
        pageSize: INITIAL_PAGE_SIZE,
        lastPage: false,
    },
    setNextPage() {
        set((state) => {
            const nextPage = state.pagination.page + 1;
            const newSkip = nextPage * state.params.first;
            return {
                ...state,
                params: {
                    ...state.params,
                    skip: newSkip,
                },
                pagination: {
                    ...state.pagination,
                    page: nextPage,
                },
            };
        });
    },
    setPrevPage() {
        set((state) => {
            const prevPage = state.pagination.page === 0 ? 0 : state.pagination.page - 1;
            const offSetPages = prevPage === 0 ? 0 : state.pagination.page - 1;
            const newSkip = state.params.first * offSetPages;
            return {
                ...state,
                params: {
                    ...state.params,
                    skip: newSkip,
                },
                pagination: {
                    ...state.pagination,
                    page: prevPage,
                },
            };
        });
    },
    setPageSize(pageSize) {
        set((state) => {
            return {
                ...state,
                pagination: {
                    lastPage: false,
                    page: 0,
                    pageSize: pageSize,
                },
                params: {
                    ...state.params,
                    first: pageSize,
                    skip: 0,
                },
            };
        });
    },
    setLastPageIndex(lastPageIndex) {
        set((state) => {
            return {
                ...state,
                pagination: {
                    ...state.pagination,
                    lastPageIndex,
                },
            };
        });
    },
}));
