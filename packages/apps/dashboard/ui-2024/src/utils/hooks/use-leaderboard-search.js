"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.useLeaderboardSearch = exports.leaderboardSearchSelectConfig = void 0;
const networks_1 = require("@utils/config/networks");
const zustand_1 = require("zustand");
exports.leaderboardSearchSelectConfig = [{ name: 'All Networks', allNetworksId: -1 }, ...networks_1.networks];
exports.useLeaderboardSearch = (0, zustand_1.create)((set) => ({
    filterParams: {
        chainId: -1,
    },
    setChainId: (chainId) => {
        set((state) => ({
            filterParams: {
                ...state.filterParams,
                chainId,
            },
        }));
    },
}));
