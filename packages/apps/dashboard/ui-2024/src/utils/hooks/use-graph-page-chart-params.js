"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.useGraphPageChartParams = exports.TIME_PERIOD_OPTIONS = exports.initialAllTime = void 0;
const dayjs_1 = __importDefault(require("dayjs"));
const zustand_1 = require("zustand");
const oneWeekAgo = (0, dayjs_1.default)().subtract(1, 'week');
const oneMonthAgo = (0, dayjs_1.default)().subtract(1, 'month');
const sixMonthsAgo = (0, dayjs_1.default)().subtract(6, 'months');
const oneYearAgo = (0, dayjs_1.default)().subtract(1, 'year');
exports.initialAllTime = (0, dayjs_1.default)().subtract(10, 'years');
exports.TIME_PERIOD_OPTIONS = [
    {
        value: oneWeekAgo,
        name: '1W',
    },
    {
        value: oneMonthAgo,
        name: '1M',
    },
    {
        value: sixMonthsAgo,
        name: '6M',
    },
    {
        value: oneYearAgo,
        name: '1Y',
    },
    {
        value: exports.initialAllTime,
        name: 'ALL',
    },
];
exports.useGraphPageChartParams = (0, zustand_1.create)((set) => ({
    dateRangeParams: {
        from: oneWeekAgo,
        to: (0, dayjs_1.default)(),
    },
    effectiveFromAllTimeDate: undefined,
    selectedTimePeriod: '1W',
    setFromDate: (fromDate) => {
        if (!fromDate) {
            return;
        }
        set((state) => {
            return {
                ...state,
                dateRangeParams: {
                    ...state.dateRangeParams,
                    from: fromDate,
                },
            };
        });
    },
    setToDate: (toDate) => {
        if (!toDate) {
            return null;
        }
        set((state) => ({
            ...state,
            dateRangeParams: {
                ...state.dateRangeParams,
                to: toDate,
            },
        }));
    },
    setTimePeriod: (timePeriod) => {
        set((state) => {
            const newFromDate = state.effectiveFromAllTimeDate && timePeriod.name === 'ALL'
                ? state.effectiveFromAllTimeDate
                : timePeriod.value;
            return {
                ...state,
                selectedTimePeriod: timePeriod.name,
                dateRangeParams: {
                    ...state.dateRangeParams,
                    from: newFromDate,
                },
            };
        });
    },
    clearTimePeriod: () => {
        set((state) => ({ ...state, selectedTimePeriod: null }));
    },
    setEffectiveFromAllTimeDate: (date) => {
        set((state) => ({
            ...state,
            effectiveFromAllTimeDate: date,
        }));
    },
}));
