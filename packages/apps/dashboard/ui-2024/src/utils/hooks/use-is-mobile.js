"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.useBreakPoints = void 0;
const useMediaQuery_1 = __importDefault(require("@mui/material/useMediaQuery"));
const breakpoints = {
    mobile: `(max-width: 1100px)`,
};
function useBreakPoints() {
    const matchesMobile = (0, useMediaQuery_1.default)(breakpoints.mobile);
    return {
        mobile: {
            isMobile: matchesMobile,
            mediaQuery: `@media ${breakpoints.mobile}`,
        },
    };
}
exports.useBreakPoints = useBreakPoints;
