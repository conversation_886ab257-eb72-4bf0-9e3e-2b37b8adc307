"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Leaderboard = void 0;
const use_leaderboard_details_1 = require("@services/api/use-leaderboard-details");
const Leaderboard_1 = require("../../features/Leaderboard");
const Leaderboard = () => {
    const { data, status, error } = (0, use_leaderboard_details_1.useLeaderboardDetails)();
    const isMoreThatFiveEntries = data?.length && data.length > 5;
    return (<Leaderboard_1.Leaderboard data={isMoreThatFiveEntries ? data.slice(0, 5) : data} status={status} error={error} viewAllBanner/>);
};
exports.Leaderboard = Leaderboard;
