"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TotalTransactions = void 0;
const FormatNumber_1 = require("@components/Home/FormatNumber");
const Typography_1 = __importDefault(require("@mui/material/Typography"));
const use_general_stats_1 = require("@services/api/use-general-stats");
function TotalTransactions() {
    const { data, status } = (0, use_general_stats_1.useGeneralStats)();
    return (<div>
			<Typography_1.default variant="body1" component="p">
				Total Transactions
			</Typography_1.default>
			<div className="count">
				{status === 'success' && (<FormatNumber_1.FormatNumber value={data.totalTransactions}/>)}
				{status === 'pending' && '...'}
				{status === 'error' && 'No data'}
			</div>
		</div>);
}
exports.TotalTransactions = TotalTransactions;
