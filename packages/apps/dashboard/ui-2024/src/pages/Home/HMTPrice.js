"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.HMTPrice = void 0;
const use_hmt_price_1 = require("../../services/api/use-hmt-price");
const Typography_1 = __importDefault(require("@mui/material/Typography"));
function HMTPrice() {
    const { data, status } = (0, use_hmt_price_1.useHMTPrice)();
    return (<div>
			<Typography_1.default variant="body1" component="p">
				HMT Price
			</Typography_1.default>
			<div className="count">
				{status === 'success' && `$${data.hmtPrice}`}
				{status === 'pending' && '...'}
				{status === 'error' && 'No data'}
			</div>
		</div>);
}
exports.HMTPrice = HMTPrice;
