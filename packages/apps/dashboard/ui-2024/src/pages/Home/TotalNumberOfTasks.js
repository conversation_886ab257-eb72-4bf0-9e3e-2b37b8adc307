"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TotalNumberOfTasks = void 0;
const use_hcaptcha_general_stats_1 = require("@services/api/use-hcaptcha-general-stats");
const Typography_1 = __importDefault(require("@mui/material/Typography"));
const FormatNumber_1 = require("@components/Home/FormatNumber");
function TotalNumberOfTasks() {
    const { data, status } = (0, use_hcaptcha_general_stats_1.useHcaptchaGeneralStats)();
    return (<div>
			<Typography_1.default variant="body1" component="p">
				Total Number of Tasks
			</Typography_1.default>
			<div className="count">
				{status === 'success' && <FormatNumber_1.FormatNumber value={data.solved}/>}
				{status === 'pending' && '...'}
				{status === 'error' && 'No data'}
			</div>
		</div>);
}
exports.TotalNumberOfTasks = TotalNumberOfTasks;
