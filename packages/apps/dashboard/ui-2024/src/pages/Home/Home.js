"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const PageWrapper_1 = __importDefault(require("@components/PageWrapper"));
const Search_1 = __importDefault(require("@components/Search"));
const ShadowIcon_1 = __importDefault(require("@components/ShadowIcon"));
const Typography_1 = __importDefault(require("@mui/material/Typography"));
const Tooltip_1 = __importDefault(require("@mui/material/Tooltip"));
const Button_1 = __importDefault(require("@mui/material/Button"));
const HelpOutline_1 = __importDefault(require("@mui/icons-material/HelpOutline"));
const Divider_1 = __importDefault(require("@mui/material/Divider"));
const react_router_dom_1 = require("react-router-dom");
const Leaderboard_1 = require("./Leaderboard");
const GraphSwiper_1 = __importDefault(require("@components/Home/GraphSwiper"));
const HMTPrice_1 = require("@pages/Home/HMTPrice");
const TotalNumberOfTasks_1 = require("@pages/Home/TotalNumberOfTasks");
const Holders_1 = require("@pages/Home/Holders");
const TotalTransactions_1 = require("@pages/Home/TotalTransactions");
const LeaderboardIcon_1 = require("@components/Icons/LeaderboardIcon");
const use_is_mobile_1 = require("@utils/hooks/use-is-mobile");
const color_palette_1 = require("@assets/styles/color-palette");
const Home = () => {
    const { mobile: { isMobile }, } = (0, use_is_mobile_1.useBreakPoints)();
    return (<PageWrapper_1.default violetHeader>
			<div className="home-page-header">
				<Typography_1.default fontWeight={isMobile ? undefined : 600} variant={isMobile ? 'H6-Mobile' : 'h3'}>
					All HUMAN activity. In one place.
				</Typography_1.default>
				<Search_1.default className="home-page-search"/>
			</div>
			<div className="home-page-boxes">
				<div className="home-page-box">
					<div className="box-title">Token</div>
					<div className="box-content">
						<div className="box-icon">
							<Tooltip_1.default title="Token Current Price" arrow>
								<HelpOutline_1.default style={{
            color: color_palette_1.colorPalette.sky.main,
        }}/>
							</Tooltip_1.default>
						</div>
						<HMTPrice_1.HMTPrice />
					</div>
					<Divider_1.default sx={{
            marginY: 3,
        }}/>
					<div className="box-content">
						<div className="box-icon">
							<Tooltip_1.default title="Number of users holding HMT" arrow>
								<HelpOutline_1.default style={{
            color: color_palette_1.colorPalette.sky.main,
        }}/>
							</Tooltip_1.default>
						</div>
						<Holders_1.Holders />
					</div>
				</div>
				<div className="home-page-box">
					<div className="box-title">
						Data Overview
						<Button_1.default sx={{ padding: '4px 10px' }} variant="outlined" color="secondary" component={react_router_dom_1.Link} to="/graph">
							View Charts
						</Button_1.default>
					</div>
					<div className="box-content">
						<div className="box-icon">
							<Tooltip_1.default title="Total number of transactions" arrow>
								<HelpOutline_1.default style={{
            color: color_palette_1.colorPalette.sky.main,
        }}/>
							</Tooltip_1.default>
						</div>
						<TotalTransactions_1.TotalTransactions />
					</div>
					<Divider_1.default sx={{
            marginY: 3,
        }}/>
					<div className="box-content">
						<div className="box-icon">
							<Tooltip_1.default title="Number of tasks that have been launched" arrow>
								<HelpOutline_1.default style={{
            color: color_palette_1.colorPalette.sky.main,
        }}/>
							</Tooltip_1.default>
						</div>
						<TotalNumberOfTasks_1.TotalNumberOfTasks />
					</div>
				</div>
				<div className="home-page-box">
					<GraphSwiper_1.default />
				</div>
			</div>
			<ShadowIcon_1.default className="home-page-leaderboard" title="Leaderboard" img={<LeaderboardIcon_1.LeaderboardIcon />}/>
			<Leaderboard_1.Leaderboard />
		</PageWrapper_1.default>);
};
exports.default = Home;
