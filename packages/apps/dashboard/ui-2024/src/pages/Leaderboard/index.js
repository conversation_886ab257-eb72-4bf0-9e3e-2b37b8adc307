"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.LeaderBoard = void 0;
const Breadcrumbs_1 = __importDefault(require("@components/Breadcrumbs"));
const PageWrapper_1 = __importDefault(require("@components/PageWrapper"));
const ShadowIcon_1 = __importDefault(require("@components/ShadowIcon"));
const index_1 = require("../../features/Leaderboard/index");
const use_leaderboard_all_details_1 = require("@services/api/use-leaderboard-all-details");
const LeaderboardIcon_1 = require("@components/Icons/LeaderboardIcon");
const LeaderBoard = () => {
    const { data, status, error } = (0, use_leaderboard_all_details_1.useLeaderboardAllDetails)();
    const isMoreThatFiveEntries = data?.length && data.length > 5;
    return (<PageWrapper_1.default displaySearchBar className="standard-background">
			<Breadcrumbs_1.default title="Leaderboard"/>
			<ShadowIcon_1.default className="home-page-leaderboard" title="Leaderboard" img={<LeaderboardIcon_1.LeaderboardIcon />}/>
			<index_1.Leaderboard data={isMoreThatFiveEntries ? data.slice(0, 5) : data} status={status} error={error}/>
		</PageWrapper_1.default>);
};
exports.LeaderBoard = LeaderBoard;
