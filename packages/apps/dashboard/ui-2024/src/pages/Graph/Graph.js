"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const Charts_1 = require("@components/Charts");
const Tabs_1 = __importDefault(require("@mui/material/Tabs"));
const TabPanel_1 = __importDefault(require("@mui/lab/TabPanel"));
const Tab_1 = __importDefault(require("@mui/material/Tab"));
const react_1 = require("react");
const TabContext_1 = __importDefault(require("@mui/lab/TabContext"));
const Typography_1 = __importDefault(require("@mui/material/Typography"));
const PageWrapper_1 = __importDefault(require("@components/PageWrapper"));
const Breadcrumbs_1 = __importDefault(require("@components/Breadcrumbs"));
const Graph = () => {
    const [graphType, setGraphType] = (0, react_1.useState)('bucketed');
    const handleGraphTypeChange = (_, newValue) => {
        setGraphType(newValue);
    };
    return (<PageWrapper_1.default displaySearchBar className="standard-background">
			<Breadcrumbs_1.default title="Charts"/>
			<TabContext_1.default value={graphType}>
				<Tabs_1.default textColor="primary" sx={{ marginBottom: 2 }} value={graphType} onChange={handleGraphTypeChange} aria-label="chart-tabs">
					<Tab_1.default sx={{
            width: { xs: '50%', sm: 'auto' },
        }} label={<Typography_1.default variant="Components/Button Large">
								Bucketed
							</Typography_1.default>} value="bucketed"/>
				</Tabs_1.default>
				<TabPanel_1.default sx={{
            p: 0,
        }} value="bucketed">
					<Charts_1.AreaChart />
				</TabPanel_1.default>
			</TabContext_1.default>
		</PageWrapper_1.default>);
};
exports.default = Graph;
