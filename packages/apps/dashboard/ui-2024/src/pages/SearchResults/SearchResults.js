"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const PageWrapper_1 = __importDefault(require("@components/PageWrapper"));
const Stack_1 = __importDefault(require("@mui/material/Stack"));
const ShadowIcon_1 = __importDefault(require("@components/ShadowIcon"));
const Clipboard_1 = __importDefault(require("@components/Clipboard"));
const react_1 = require("react");
const react_router_dom_1 = require("react-router-dom");
const EscrowAddress_1 = __importDefault(require("@pages/SearchResults/EscrowAddress"));
const WalletAddress_1 = __importDefault(require("@pages/SearchResults/WalletAddress"));
const NothingFound_1 = __importDefault(require("@components/NothingFound"));
const Breadcrumbs_1 = __importDefault(require("@components/Breadcrumbs"));
const Search_1 = __importDefault(require("@components/Search"));
const use_wallet_search_1 = require("@utils/hooks/use-wallet-search");
const Loader_1 = __importDefault(require("@components/Loader"));
const networks_1 = require("@utils/config/networks");
const use_address_details_1 = require("@services/api/use-address-details");
const handle_error_message_1 = require("@services/handle-error-message");
const RoleDetails_1 = __importDefault(require("@pages/SearchResults/RoleDetails/RoleDetails"));
const axios_1 = require("axios");
const WalletIcon_1 = require("@components/Icons/WalletIcon");
const EscrowAddressIcon_1 = require("@components/Icons/EscrowAddressIcon");
const renderCurrentResultType = (addressDetails, tokenId) => {
    const type = Object.keys(addressDetails)[0];
    const renderType = {
        leader: {
            title: 'Wallet Address',
            icon: <WalletIcon_1.WalletIcon />,
        },
        escrow: {
            title: 'Escrow Address',
            icon: <EscrowAddressIcon_1.EscrowAddressIcon />,
        },
        wallet: {
            title: 'Wallet Address',
            icon: <WalletIcon_1.WalletIcon />,
        },
    };
    if (type == null) {
        return null;
    }
    return (<>
			<ShadowIcon_1.default img={renderType[type].icon} title={renderType[type].title}/>
			<Clipboard_1.default value={tokenId ?? ''}/>
		</>);
};
const ResultError = ({ error }) => {
    if (error instanceof axios_1.AxiosError && error.response?.status === 400) {
        return (<Stack_1.default sx={{ paddingTop: '2rem' }}>
				<NothingFound_1.default />
			</Stack_1.default>);
    }
    return <Stack_1.default sx={{ paddingTop: '2rem' }}>{(0, handle_error_message_1.handleErrorMessage)(error)}</Stack_1.default>;
};
const Results = () => {
    const { data, status, error } = (0, use_address_details_1.useAddressDetails)();
    const { filterParams } = (0, use_wallet_search_1.useWalletSearch)();
    if (status === 'pending' && !data) {
        return <Loader_1.default height="30vh"/>;
    }
    if (status === 'error') {
        return <ResultError error={error}/>;
    }
    const selectedWalletData = data.wallet ||
        (data.leader && data.leader.role === null ? data.leader : undefined);
    return (<>
			<Stack_1.default sx={{ marginBottom: 2, marginTop: { xs: 0, md: 4 } }} direction={{ xs: 'column', md: 'row' }} gap={3} alignItems={{ xs: 'stretch', md: 'center' }}>
				{renderCurrentResultType(data, filterParams.address)}
			</Stack_1.default>

			{data.leader && data.leader.role ? (<RoleDetails_1.default data={data.leader}/>) : null}
			{selectedWalletData ? <WalletAddress_1.default data={selectedWalletData}/> : null}
			{data.escrow ? <EscrowAddress_1.default data={data.escrow}/> : null}
		</>);
};
const SearchResults = () => {
    const location = (0, react_router_dom_1.useLocation)();
    const { chainId: urlChainId, address: urlAddress } = (0, react_router_dom_1.useParams)();
    const { setAddress, setChainId, filterParams: { chainId, address }, } = (0, use_wallet_search_1.useWalletSearch)();
    const [paramsStatus, setParamsStatus] = (0, react_1.useState)('loading');
    (0, react_1.useEffect)(() => {
        setParamsStatus('loading');
    }, [location]);
    (0, react_1.useEffect)(() => {
        if (paramsStatus === 'success')
            return;
        if (urlAddress) {
            setAddress(urlAddress);
        }
        else {
            setParamsStatus('error');
            return;
        }
        const chainIdFromUrl = Number(urlChainId);
        if (!Number.isNaN(chainIdFromUrl) &&
            chainIdFromUrl &&
            (0, networks_1.getNetwork)(chainIdFromUrl)) {
            setChainId(chainIdFromUrl);
        }
        else {
            setParamsStatus('error');
        }
    }, [
        address,
        chainId,
        paramsStatus,
        setAddress,
        setChainId,
        urlAddress,
        urlChainId,
    ]);
    (0, react_1.useEffect)(() => {
        if (address && chainId && paramsStatus !== 'success') {
            setParamsStatus('success');
        }
    }, [address, chainId, paramsStatus]);
    return (<PageWrapper_1.default displaySearchBar className="standard-background">
			<Breadcrumbs_1.default title="Search Results"/>
			<Search_1.default className="search-results-bar"/>
			{paramsStatus === 'loading' && <Loader_1.default />}
			{paramsStatus === 'error' && (<Stack_1.default sx={{ paddingTop: '2rem' }}>Something went wrong</Stack_1.default>)}
			{paramsStatus === 'success' && <Results />}
		</PageWrapper_1.default>);
};
exports.default = SearchResults;
