"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const Card_1 = __importDefault(require("@mui/material/Card"));
const Typography_1 = __importDefault(require("@mui/material/Typography"));
const Box_1 = __importDefault(require("@mui/material/Box"));
const color_palette_1 = require("@assets/styles/color-palette");
const Stack_1 = __importDefault(require("@mui/material/Stack"));
const Tooltip_1 = __importDefault(require("@mui/material/Tooltip"));
const IconButton_1 = __importDefault(require("@mui/material/IconButton"));
const reputation_oracle_svg_1 = __importDefault(require("@assets/icons/reputation-oracle.svg"));
const exchange_oracle_svg_1 = __importDefault(require("@assets/icons/exchange-oracle.svg"));
const human_app_svg_1 = __importDefault(require("@assets/icons/human-app.svg"));
const job_launcher_svg_1 = __importDefault(require("@assets/icons/job-launcher.svg"));
const recording_oracle_svg_1 = __importDefault(require("@assets/icons/recording-oracle.svg"));
const HelpOutline_1 = __importDefault(require("@mui/icons-material/HelpOutline"));
const use_address_details_1 = require("@services/api/use-address-details");
const networks_1 = require("@utils/config/networks");
const use_wallet_search_1 = require("@utils/hooks/use-wallet-search");
const RoleDetailsEscrowsTable_1 = require("@pages/SearchResults/RoleDetails/RoleDetailsEscrows/RoleDetailsEscrowsTable");
const env_1 = require("@helpers/env");
const FormatNumber_1 = require("@components/Home/FormatNumber");
const RoleInformation = ({ title, points }) => {
    return (<Box_1.default>
			<Typography_1.default variant="body2">{title}</Typography_1.default>
			<ul style={{
            margin: 0,
            marginTop: 6,
            paddingLeft: 25,
        }}>
				{points.map((elem, idx) => (<li key={idx}>
						<Typography_1.default variant="body2">{elem}</Typography_1.default>
					</li>))}
			</ul>
		</Box_1.default>);
};
const RenderRoleDetailsInfo = ({ role, }) => {
    if (!role) {
        return null;
    }
    const roleDetailsInfo = {
        [use_address_details_1.Roles.reputationOracle]: {
            title: 'Reputation Oracle',
            points: [
                'The Reputation Oracle is the trust engine of HUMAN Protocol.',
                'It cross-checks validated solutions from the Recording Oracle, adjusts reputation scores, and manages payments.',
                "It's the final seal of quality and trust within the ecosystem.",
            ],
        },
        [use_address_details_1.Roles.recordingOracle]: {
            title: 'Recording Oracle',
            points: [
                'The Recording Oracle is where task solutions get the green light.',
                'It is storing, and recording task solutions on the blockchain.',
                "From quality checks to reputation adjustments, it's the assurance you need for dependable results.",
            ],
        },
        [use_address_details_1.Roles.exchangeOracle]: {
            title: 'Exchange Oracle',
            points: [
                "The Exchange Oracle is the HUMAN Protocol's powerhouse, directing tasks to skilled workers and ensuring smooth communication.",
                'It validates solutions, provides job updates, and handles cancellations, streamlining the job lifecycle.',
            ],
        },
        [use_address_details_1.Roles.jobLauncher]: {
            title: 'Job Launcher',
            points: [
                'The Job Launcher is a tool that allows anybody to create and launch jobs, to be distributed as tasks through the HUMAN App.',
            ],
        },
    };
    const details = roleDetailsInfo[role];
    if (!details) {
        return null;
    }
    return <RoleInformation points={details.points} title={details.title}/>;
};
const renderReputationTitle = (reputation) => {
    const reputationAttributes = {
        High: {
            title: 'High',
            colors: {
                title: color_palette_1.colorPalette.success.main,
                border: color_palette_1.colorPalette.success.light,
            },
        },
        Medium: {
            title: 'Medium',
            colors: {
                title: color_palette_1.colorPalette.warning.main,
                border: color_palette_1.colorPalette.warning.light,
            },
        },
        Low: {
            title: 'Low',
            colors: {
                title: color_palette_1.colorPalette.orange.main,
                border: color_palette_1.colorPalette.orange.light,
            },
        },
        Unknown: {
            title: 'Coming soon',
            colors: {
                title: color_palette_1.colorPalette.ocean.main,
                border: color_palette_1.colorPalette.ocean.light,
            },
        },
    };
    const colors = reputationAttributes[reputation].colors;
    return (<Box_1.default sx={{
            paddingX: 2,
            paddingY: 1,
            borderRadius: 4,
            border: `1px solid ${colors.border}`,
        }}>
			<Typography_1.default color={colors.title}>
				{reputationAttributes[reputation].title}
			</Typography_1.default>
		</Box_1.default>);
};
const renderRoleIcon = (role) => {
    if (!role)
        return null;
    const roleIcons = {
        [use_address_details_1.Roles.reputationOracle]: <reputation_oracle_svg_1.default />,
        [use_address_details_1.Roles.exchangeOracle]: <exchange_oracle_svg_1.default />,
        [use_address_details_1.Roles.humanApp]: <human_app_svg_1.default />,
        [use_address_details_1.Roles.jobLauncher]: <job_launcher_svg_1.default />,
        [use_address_details_1.Roles.recordingOracle]: <recording_oracle_svg_1.default />,
    };
    return roleIcons[role];
};
const RoleDetails = ({ data: { role, chainId, reputation, amountJobsProcessed, amountStaked, amountAllocated, amountLocked, }, }) => {
    const { filterParams } = (0, use_wallet_search_1.useWalletSearch)();
    return (<>
			<Card_1.default sx={{
            paddingX: { xs: 2, md: 8 },
            paddingY: { xs: 4, md: 6 },
            marginBottom: 4,
            borderRadius: '16px',
            boxShadow: 'none',
        }}>
				<Box_1.default sx={{
            marginBottom: { xs: 4, md: 8 },
        }}>
					<Typography_1.default sx={{
            marginBottom: 1.5,
        }} variant="h5">
						Overview
					</Typography_1.default>
					{env_1.env.VITE_HUMANPROTOCOL_CORE_ARCHITECTURE ? (<Box_1.default sx={{
                borderRadius: 16,
                backgroundColor: color_palette_1.colorPalette.overlay.light,
                display: 'inline-block',
                paddingY: 1,
                paddingX: 1.5,
                textDecoration: 'none',
            }} component="a" href={env_1.env.VITE_HUMANPROTOCOL_CORE_ARCHITECTURE} target="_blank">
							<Typography_1.default color={color_palette_1.colorPalette.ocean.main}>
								HUMAN Protocol core architecture
							</Typography_1.default>
						</Box_1.default>) : null}
				</Box_1.default>
				<Stack_1.default gap={4}>
					<Stack_1.default gap={{ xs: 1, md: 0 }} alignItems="baseline" direction={{ sm: 'column', md: 'row' }}>
						<Stack_1.default sx={{
            width: 300,
        }} direction="row" alignItems="center">
							<Typography_1.default variant="subtitle2">Role</Typography_1.default>
						</Stack_1.default>
						<Stack_1.default gap={2} direction="column">
							{renderRoleIcon(role)}
							<RenderRoleDetailsInfo role={role}/>
						</Stack_1.default>
					</Stack_1.default>
					<Stack_1.default gap={{ xs: 1, md: 0 }} direction={{ sm: 'column', md: 'row' }}>
						<Typography_1.default sx={{
            width: 300,
        }} variant="subtitle2">
							Network
						</Typography_1.default>
						<Typography_1.default variant="body2">
							{(0, networks_1.getNetwork)(chainId)?.name || ''}
						</Typography_1.default>
					</Stack_1.default>
					<Stack_1.default alignItems={{ xs: 'start', md: 'center' }} gap={{ xs: 1, md: 0 }} direction={{ sm: 'column', md: 'row' }}>
						<Stack_1.default sx={{
            width: 300,
        }} direction="row" alignItems="center">
							<Tooltip_1.default title="Reputation of the role as per their activities">
								<IconButton_1.default sx={{
            padding: 0,
            paddingRight: 1,
            color: color_palette_1.colorPalette.fog.main,
        }}>
									<HelpOutline_1.default fontSize="small"/>
								</IconButton_1.default>
							</Tooltip_1.default>
							<Typography_1.default variant="subtitle2">Reputation Score</Typography_1.default>
						</Stack_1.default>
						{renderReputationTitle(reputation)}
					</Stack_1.default>
					<Stack_1.default gap={{ xs: 1, md: 0 }} direction={{ sm: 'column', md: 'row' }}>
						<Typography_1.default sx={{
            width: 300,
        }} variant="subtitle2">
							Jobs Launched
						</Typography_1.default>
						<Typography_1.default>{amountJobsProcessed}</Typography_1.default>
					</Stack_1.default>
				</Stack_1.default>
			</Card_1.default>

			<Card_1.default sx={{
            paddingX: { xs: 2, md: 8 },
            paddingY: { xs: 4, md: 6 },
            marginBottom: 4,
            borderRadius: '16px',
            boxShadow: 'none',
        }}>
				<Box_1.default sx={{
            marginBottom: { xs: 4, md: 8 },
        }}>
					<Typography_1.default sx={{
            marginBottom: 1.5,
        }} variant="h5">
						Stake Info
					</Typography_1.default>
				</Box_1.default>
				<Stack_1.default gap={4}>
					{amountStaked !== undefined && amountStaked !== null ? (<Stack_1.default gap={{ xs: 1, md: 0 }} direction={{ sm: 'column', md: 'row' }}>
							<Typography_1.default sx={{
                width: 300,
            }} variant="subtitle2">
								Tokens Staked
							</Typography_1.default>
							<Stack_1.default sx={{ whiteSpace: 'nowrap', flexDirection: 'row' }}>
								<Typography_1.default variant="body2">
									<FormatNumber_1.FormatNumberWithDecimals value={amountStaked}/>
								</Typography_1.default>
								<Typography_1.default sx={{
                marginLeft: 0.5,
            }} variant="body2" color={color_palette_1.colorPalette.fog.main} component="span">
									HMT
								</Typography_1.default>
							</Stack_1.default>
						</Stack_1.default>) : null}
					{amountAllocated !== undefined && amountAllocated !== null ? (<Stack_1.default gap={{ xs: 1, md: 0 }} direction={{ sm: 'column', md: 'row' }}>
							<Typography_1.default sx={{
                width: 300,
            }} variant="subtitle2">
								Tokens Allocated
							</Typography_1.default>
							<Stack_1.default sx={{ whiteSpace: 'nowrap', flexDirection: 'row' }}>
								<Typography_1.default variant="body2">
									<FormatNumber_1.FormatNumberWithDecimals value={amountAllocated}/>
								</Typography_1.default>
								<Typography_1.default sx={{
                marginLeft: 0.5,
            }} variant="body2" color={color_palette_1.colorPalette.fog.main} component="span">
									HMT
								</Typography_1.default>
							</Stack_1.default>
						</Stack_1.default>) : null}
					{amountLocked !== undefined && amountLocked !== null ? (<Stack_1.default gap={{ xs: 1, md: 0 }} direction={{ sm: 'column', md: 'row' }}>
							<Typography_1.default sx={{
                width: 300,
            }} variant="subtitle2">
								Tokens Locked
							</Typography_1.default>
							<Stack_1.default sx={{ whiteSpace: 'nowrap', flexDirection: 'row' }}>
								<Typography_1.default variant="body2">
									<FormatNumber_1.FormatNumberWithDecimals value={amountLocked}/>
								</Typography_1.default>
								<Typography_1.default sx={{
                marginLeft: 0.5,
            }} variant="body2" color={color_palette_1.colorPalette.fog.main} component="span">
									HMT
								</Typography_1.default>
							</Stack_1.default>
						</Stack_1.default>) : null}
				</Stack_1.default>
			</Card_1.default>

			{filterParams.address && filterParams.chainId ? (<RoleDetailsEscrowsTable_1.RoleDetailsEscrowsTable role={role}/>) : null}
		</>);
};
exports.default = RoleDetails;
