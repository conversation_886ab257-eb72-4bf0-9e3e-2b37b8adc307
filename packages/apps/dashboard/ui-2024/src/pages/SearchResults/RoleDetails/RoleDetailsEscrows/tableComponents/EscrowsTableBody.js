"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.EscrowsTableBody = void 0;
const TableCell_1 = __importDefault(require("@mui/material/TableCell"));
const TableBody_1 = __importDefault(require("@mui/material/TableBody"));
const react_1 = require("react");
const handle_error_message_1 = require("@services/handle-error-message");
const CircularProgress_1 = __importDefault(require("@mui/material/CircularProgress"));
const EscrowsTableBodyContainer_1 = require("@pages/SearchResults/RoleDetails/RoleDetailsEscrows/tableComponents/EscrowsTableBodyContainer");
const use_escrows_details_1 = require("@services/api/use-escrows-details");
const use_escrows_details_dto_1 = require("@utils/hooks/use-escrows-details-dto");
const use_wallet_search_1 = require("@utils/hooks/use-wallet-search");
const react_router_dom_1 = require("react-router-dom");
const material_1 = require("@mui/material");
const EscrowsTableBody = ({ role, }) => {
    const navigate = (0, react_router_dom_1.useNavigate)();
    const { filterParams } = (0, use_wallet_search_1.useWalletSearch)();
    const { data, isPending, isError, error } = (0, use_escrows_details_1.useEscrowDetails)({ role });
    const { setLastPageIndex, setPrevPage, pagination: { page }, } = (0, use_escrows_details_dto_1.useEscrowDetailsDto)();
    (0, react_1.useEffect)(() => {
        if (data?.results.length === 0) {
            setLastPageIndex(page);
            setPrevPage();
        }
    }, [data?.results, page, setLastPageIndex, setPrevPage]);
    (0, react_1.useEffect)(() => {
        setLastPageIndex(undefined);
    }, [filterParams.address, filterParams.chainId, setLastPageIndex]);
    if (isPending) {
        return (<EscrowsTableBodyContainer_1.EscrowsTableBodyContainer>
				<CircularProgress_1.default />
			</EscrowsTableBodyContainer_1.EscrowsTableBodyContainer>);
    }
    if (isError) {
        return (<EscrowsTableBodyContainer_1.EscrowsTableBodyContainer>
				<div>{(0, handle_error_message_1.handleErrorMessage)(error)}</div>
			</EscrowsTableBodyContainer_1.EscrowsTableBodyContainer>);
    }
    if (!data.results.length) {
        return (<EscrowsTableBodyContainer_1.EscrowsTableBodyContainer>
				<div>No escrows launched yet</div>
			</EscrowsTableBodyContainer_1.EscrowsTableBodyContainer>);
    }
    return (<TableBody_1.default>
			{data.results.map((elem, idx) => (<material_1.TableRow key={idx}>
					<TableCell_1.default sx={{
                padding: '0 0 24px 0',
            }}>
						<material_1.Stack sx={{
                ':hover': {
                    cursor: 'pointer',
                },
            }} onClick={(e) => {
                e.stopPropagation();
                e.preventDefault();
                navigate(`/search/${filterParams.chainId}/${elem.address}`);
            }}>
							<a target="_blank" href={`/search/${filterParams.chainId}/${elem.address}`} style={{
                textDecoration: 'unset',
            }}>
								{elem.address}
							</a>
						</material_1.Stack>
					</TableCell_1.default>
				</material_1.TableRow>))}
		</TableBody_1.default>);
};
exports.EscrowsTableBody = EscrowsTableBody;
