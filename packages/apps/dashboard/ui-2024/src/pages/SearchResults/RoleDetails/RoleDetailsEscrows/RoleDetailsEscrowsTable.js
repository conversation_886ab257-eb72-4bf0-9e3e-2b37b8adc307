"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.RoleDetailsEscrowsTable = void 0;
const Card_1 = __importDefault(require("@mui/material/Card"));
const Typography_1 = __importDefault(require("@mui/material/Typography"));
const Box_1 = __importDefault(require("@mui/material/Box"));
const TableContainer_1 = __importDefault(require("@mui/material/TableContainer"));
const Table_1 = __importDefault(require("@mui/material/Table"));
const EscrowsTableBody_1 = require("@pages/SearchResults/RoleDetails/RoleDetailsEscrows/tableComponents/EscrowsTableBody");
const TablePagination_1 = __importDefault(require("@mui/material/TablePagination"));
const use_escrows_details_dto_1 = require("@utils/hooks/use-escrows-details-dto");
const TableHead_1 = __importDefault(require("@mui/material/TableHead"));
const TableRow_1 = __importDefault(require("@mui/material/TableRow"));
const material_1 = require("@mui/material");
const RoleDetailsEscrowsTable = ({ role, }) => {
    const { pagination: { page, pageSize, lastPageIndex }, setPageSize, setNextPage, setPrevPage, } = (0, use_escrows_details_dto_1.useEscrowDetailsDto)();
    return (<Card_1.default sx={{
            paddingX: { xs: 2, md: 8 },
            paddingY: { xs: 4, md: 6 },
            marginBottom: 4,
            borderRadius: '16px',
            boxShadow: 'none',
        }}>
			<Box_1.default>
				<Typography_1.default sx={{
            marginBottom: 3,
        }} variant="h5">
					Escrows
				</Typography_1.default>
				<TableContainer_1.default>
					<Table_1.default sx={{
            minWidth: 800,
            '& .MuiTableCell-root': {
                borderBottom: 'none',
            },
        }} aria-label="simple-table">
						<TableHead_1.default>
							<TableRow_1.default></TableRow_1.default>
						</TableHead_1.default>
						<EscrowsTableBody_1.EscrowsTableBody role={role}/>
					</Table_1.default>
				</TableContainer_1.default>
				<material_1.Stack sx={{
            width: '100%',
            display: 'flex',
        }}>
					<TablePagination_1.default 
    // count is unknown but required as props
    count={9999} 
    // onPageChange is required as props
    onPageChange={() => { }} page={page} rowsPerPage={pageSize} onRowsPerPageChange={(event) => {
            setPageSize(Number(event.target.value));
        }} rowsPerPageOptions={[5, 10]} labelDisplayedRows={({ from, to }) => {
            return `${from}–${to}`;
        }} component="div" slotProps={{
            actions: {
                nextButton: {
                    onClick: () => {
                        setNextPage();
                    },
                    disabled: lastPageIndex !== undefined &&
                        (page === lastPageIndex || lastPageIndex - 1 === page),
                },
                previousButton: {
                    onClick: () => {
                        setPrevPage();
                    },
                },
            },
        }}/>
				</material_1.Stack>
			</Box_1.default>
		</Card_1.default>);
};
exports.RoleDetailsEscrowsTable = RoleDetailsEscrowsTable;
