"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.HMTBalance = void 0;
const color_palette_1 = require("@assets/styles/color-palette");
const Stack_1 = __importDefault(require("@mui/material/Stack"));
const Typography_1 = __importDefault(require("@mui/material/Typography"));
const use_hmt_price_1 = require("@services/api/use-hmt-price");
const HMTBalance = ({ HMTBalance }) => {
    const { data, isError, isPending } = (0, use_hmt_price_1.useHMTPrice)();
    if (isError) {
        return 'N/A';
    }
    if (isPending) {
        return '...';
    }
    const HMTBalanceInDollars = HMTBalance * data.hmtPrice;
    return (<Stack_1.default sx={{ whiteSpace: 'nowrap', flexDirection: 'row' }}>
			<Typography_1.default variant="body2">{HMTBalance}</Typography_1.default>
			<Typography_1.default sx={{
            marginLeft: 0.5,
        }} color={color_palette_1.colorPalette.fog.main} component="span" variant="body2">
				{`HMT($${HMTBalanceInDollars.toFixed(2)})`}
			</Typography_1.default>
		</Stack_1.default>);
};
exports.HMTBalance = HMTBalance;
