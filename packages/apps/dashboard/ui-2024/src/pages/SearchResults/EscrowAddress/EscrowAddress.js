"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const Typography_1 = __importDefault(require("@mui/material/Typography"));
const Card_1 = __importDefault(require("@mui/material/Card"));
const Box_1 = __importDefault(require("@mui/material/Box"));
const Stack_1 = __importDefault(require("@mui/material/Stack"));
const color_palette_1 = require("@assets/styles/color-palette");
const SearchResults_1 = __importDefault(require("@components/SearchResults"));
const HMTBalance_1 = require("@pages/SearchResults/EscrowAddress/HMTBalance");
const EscrowAddress = ({ data: { token, balance, factoryAddress, totalFundedAmount, amountPaid, status, launcher, exchangeOracle, recordingOracle, reputationOracle, }, }) => {
    return (<Card_1.default sx={{
            paddingX: { xs: 2, md: 8 },
            paddingY: { xs: 4, md: 6 },
            borderRadius: '16px',
            boxShadow: 'none',
        }}>
			<Stack_1.default gap={4}>
				<SearchResults_1.default title="Token">
					<Typography_1.default variant="body2">{token}</Typography_1.default>
				</SearchResults_1.default>
				{balance !== undefined && balance !== null ? (<SearchResults_1.default tooltip={{ description: 'Amount of HMT in Escrow' }} title="Balance">
						<HMTBalance_1.HMTBalance HMTBalance={balance}/>
					</SearchResults_1.default>) : null}
				<SearchResults_1.default title="Factory Address" tooltip={{ description: 'Address of EscrowFactory contract' }}>
					<Typography_1.default variant="body2">{factoryAddress}</Typography_1.default>
				</SearchResults_1.default>
				<SearchResults_1.default title="Total Funded Amount">
					<Stack_1.default sx={{ whiteSpace: 'nowrap', flexDirection: 'row' }}>
						<Typography_1.default variant="body2">{totalFundedAmount}</Typography_1.default>
						<Typography_1.default sx={{
            marginLeft: 0.5,
        }} color={color_palette_1.colorPalette.fog.main} component="span" variant="body2">
							HMT
						</Typography_1.default>
					</Stack_1.default>
				</SearchResults_1.default>
				<SearchResults_1.default title="Paid Amount">
					<Stack_1.default sx={{ whiteSpace: 'nowrap', flexDirection: 'row' }}>
						<Typography_1.default variant="body2">{amountPaid}</Typography_1.default>
						<Typography_1.default sx={{
            marginLeft: 0.5,
        }} color={color_palette_1.colorPalette.fog.main} component="span" variant="body2">
							HMT
						</Typography_1.default>
					</Stack_1.default>
				</SearchResults_1.default>

				<SearchResults_1.default title="Status">
					<Box_1.default sx={{
            padding: '3px 8px',
            borderRadius: '16px',
            border: `1px solid ${color_palette_1.colorPalette.secondary.light}`,
        }}>
						<Typography_1.default variant="Components/Chip" color={color_palette_1.colorPalette.secondary.main}>
							{status}
						</Typography_1.default>
					</Box_1.default>
				</SearchResults_1.default>

				<SearchResults_1.default title="Job Launcher" tooltip={{
            description: 'Address of the Job Launcher that created the escrow',
        }}>
					<Typography_1.default variant="body2" sx={{ wordBreak: 'break-word' }}>
						{launcher}
					</Typography_1.default>
				</SearchResults_1.default>

				<SearchResults_1.default title="Exchange Oracle" tooltip={{
            description: "The Exchange Oracle is HUMAN Protocol's powerhouse, routing tasks to skilled workers ensuring smooth communication.",
        }}>
					<Typography_1.default variant="body2">{exchangeOracle}</Typography_1.default>
				</SearchResults_1.default>

				<SearchResults_1.default title="Recording Oracle" tooltip={{
            description: 'The Recording Oracle is where task solutions get the green light. It is storing, and recording task solutions on the blockchain.\n' +
                '\n' +
                "From quality checks to reputation adjustments, it's the assurance you need for dependable results.",
        }}>
					<Typography_1.default variant="body2">{recordingOracle}</Typography_1.default>
				</SearchResults_1.default>

				<SearchResults_1.default title="Reputation Oracle" tooltip={{
            description: 'The Reputation Oracle is the trust engine of the HUMAN Protocol. It cross-checks validated solutions from the Recording Oracle, adjusts reputation scores, and manages payments.\n' +
                "It's the final seal of quality and trust within the ecosystem.",
        }}>
					<Typography_1.default variant="body2">{reputationOracle}</Typography_1.default>
				</SearchResults_1.default>
			</Stack_1.default>
		</Card_1.default>);
};
exports.default = EscrowAddress;
