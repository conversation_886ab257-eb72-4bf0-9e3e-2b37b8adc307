"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.WalletAddressTransactionsTable = void 0;
const Typography_1 = __importDefault(require("@mui/material/Typography"));
const Card_1 = __importDefault(require("@mui/material/Card"));
const TableContainer_1 = __importDefault(require("@mui/material/TableContainer"));
const Table_1 = __importDefault(require("@mui/material/Table"));
const simplebar_react_1 = __importDefault(require("simplebar-react"));
const TablePagination_1 = __importDefault(require("@mui/material/TablePagination"));
const TransactionsTableHead_1 = require("@pages/SearchResults/WalletAddress/WalletAddressTransactions/tableComponents/TransactionsTableHead");
const TransactionsTableBody_1 = require("@pages/SearchResults/WalletAddress/WalletAddressTransactions/tableComponents/TransactionsTableBody");
const use_transactions_details_dto_1 = require("@utils/hooks/use-transactions-details-dto");
const material_1 = require("@mui/material");
const WalletAddressTransactionsTable = () => {
    const { pagination: { page, pageSize, lastPageIndex }, setPageSize, setNextPage, setPrevPage, } = (0, use_transactions_details_dto_1.useTransactionDetailsDto)();
    return (<Card_1.default sx={{
            paddingX: { xs: 2, md: 8 },
            paddingY: { xs: 4, md: 6 },
            borderRadius: '16px',
            boxShadow: 'none',
        }}>
			<Typography_1.default sx={{ marginBottom: 2 }} variant="h5" component="p">
				Transactions
			</Typography_1.default>
			<TableContainer_1.default>
				<simplebar_react_1.default>
					<Table_1.default sx={{
            minWidth: 800,
            '& .MuiTableCell-root': {
                borderBottom: 'none',
            },
        }} aria-label="simple-table">
						<TransactionsTableHead_1.TransactionsTableHead />
						<TransactionsTableBody_1.TransactionsTableBody />
						<material_1.TableFooter>
							<tr>
								<TablePagination_1.default 
    // count is unknown but required as props
    count={9999} 
    // onPageChange is required as props
    onPageChange={() => { }} page={page} component="td" rowsPerPage={pageSize} onRowsPerPageChange={(event) => {
            setPageSize(Number(event.target.value));
        }} rowsPerPageOptions={[5, 10]} labelDisplayedRows={({ from, to }) => {
            return `${from}–${to}`;
        }} slotProps={{
            actions: {
                nextButton: {
                    onClick: () => {
                        setNextPage();
                    },
                    disabled: lastPageIndex !== undefined &&
                        (page === lastPageIndex ||
                            lastPageIndex - 1 === page),
                },
                previousButton: {
                    onClick: () => {
                        setPrevPage();
                    },
                },
            },
        }}/>
							</tr>
						</material_1.TableFooter>
					</Table_1.default>
				</simplebar_react_1.default>
			</TableContainer_1.default>
		</Card_1.default>);
};
exports.WalletAddressTransactionsTable = WalletAddressTransactionsTable;
