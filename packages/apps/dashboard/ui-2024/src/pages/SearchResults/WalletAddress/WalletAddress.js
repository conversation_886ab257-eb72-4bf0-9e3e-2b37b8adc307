"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const Typography_1 = __importDefault(require("@mui/material/Typography"));
const Card_1 = __importDefault(require("@mui/material/Card"));
const Stack_1 = __importDefault(require("@mui/material/Stack"));
const SearchResults_1 = __importDefault(require("@components/SearchResults"));
const color_palette_1 = require("@assets/styles/color-palette");
const use_hmt_price_1 = require("@services/api/use-hmt-price");
const WalletAddressTransactionsTable_1 = require("@pages/SearchResults/WalletAddress/WalletAddressTransactions/WalletAddressTransactionsTable");
const use_wallet_search_1 = require("@utils/hooks/use-wallet-search");
const react_number_format_1 = require("react-number-format");
const use_is_mobile_1 = require("@utils/hooks/use-is-mobile");
const HmtPrice = () => {
    const { data: hmtPrice, isError: isHmtPriceError, isPending: isHmtPricePending, } = (0, use_hmt_price_1.useHMTPrice)();
    if (isHmtPriceError) {
        return <SearchResults_1.default title="HMT Price">N/A</SearchResults_1.default>;
    }
    if (isHmtPricePending) {
        return <SearchResults_1.default title="HMT Price">...</SearchResults_1.default>;
    }
    return (<SearchResults_1.default title="HMT Price">
			<Stack_1.default sx={{ whiteSpace: 'nowrap', flexDirection: 'row' }}>
				<Typography_1.default variant="body2">
					<>{hmtPrice.hmtPrice}</>
				</Typography_1.default>
				<Typography_1.default sx={{
            marginLeft: 0.5,
        }} color={color_palette_1.colorPalette.fog.main} component="span" variant="body2">
					$
				</Typography_1.default>
			</Stack_1.default>
		</SearchResults_1.default>);
};
const WalletAddress = ({ data: { balance }, }) => {
    const { filterParams } = (0, use_wallet_search_1.useWalletSearch)();
    const { mobile } = (0, use_is_mobile_1.useBreakPoints)();
    return (<>
			<Card_1.default sx={{
            paddingX: { xs: 2, md: 8 },
            paddingY: { xs: 4, md: 6 },
            marginBottom: 4,
            borderRadius: '16px',
            boxShadow: 'none',
        }}>
				<Stack_1.default gap={4}>
					<SearchResults_1.default title="Balance">
						<Stack_1.default sx={{ whiteSpace: 'nowrap', flexDirection: 'row' }}>
							<Typography_1.default variant="body2">
								<react_number_format_1.NumericFormat displayType="text" value={Number(balance) < 1 ? Number(balance) * 1e18 : balance} thousandSeparator="," decimalScale={mobile.isMobile ? 4 : undefined}/>
							</Typography_1.default>
							<Typography_1.default sx={{
            marginLeft: 0.5,
        }} color={color_palette_1.colorPalette.fog.main} component="span" variant="body2">
								HMT
							</Typography_1.default>
						</Stack_1.default>
					</SearchResults_1.default>
					<HmtPrice />
				</Stack_1.default>
			</Card_1.default>

			{filterParams.address && filterParams.chainId ? (<WalletAddressTransactionsTable_1.WalletAddressTransactionsTable />) : null}
		</>);
};
exports.default = WalletAddress;
