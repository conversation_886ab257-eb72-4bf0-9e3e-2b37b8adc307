"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TransactionsTableBodyContainer = void 0;
const TableBody_1 = __importDefault(require("@mui/material/TableBody"));
const material_1 = require("@mui/material");
const TransactionsTableBodyContainer = ({ children, }) => {
    return (<TableBody_1.default sx={{ position: 'relative', height: ' 40vh' }}>
			<material_1.Grid component="tr" container sx={{
            justifyContent: 'center',
            alignItems: 'center',
            position: 'absolute',
            top: 0,
            left: 0,
            width: '100%',
            height: '100%',
        }}>
				<td>{children}</td>
			</material_1.Grid>
		</TableBody_1.default>);
};
exports.TransactionsTableBodyContainer = TransactionsTableBodyContainer;
