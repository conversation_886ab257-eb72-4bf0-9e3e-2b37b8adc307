"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TransactionsTableBody = void 0;
const TableRow_1 = __importDefault(require("@mui/material/TableRow"));
const TableCell_1 = __importDefault(require("@mui/material/TableCell"));
const TableBody_1 = __importDefault(require("@mui/material/TableBody"));
const AbbreviateClipboard_1 = __importDefault(require("@components/SearchResults/AbbreviateClipboard"));
const use_transaction_details_1 = require("@services/api/use-transaction-details");
const react_1 = require("react");
const use_transactions_details_dto_1 = require("@utils/hooks/use-transactions-details-dto");
const TransactionTableCellMethod_1 = require("@pages/SearchResults/WalletAddress/WalletAddressTransactions/cells/TransactionTableCellMethod");
const TransactionTableCellValue_1 = require("@pages/SearchResults/WalletAddress/WalletAddressTransactions/cells/TransactionTableCellValue");
const TransactionsTableBodyContainer_1 = require("@pages/SearchResults/WalletAddress/WalletAddressTransactions/tableComponents/TransactionsTableBodyContainer");
const handle_error_message_1 = require("@services/handle-error-message");
const CircularProgress_1 = __importDefault(require("@mui/material/CircularProgress"));
const use_wallet_search_1 = require("@utils/hooks/use-wallet-search");
const TransactionsTableBody = () => {
    const { data, isPending, isError, error } = (0, use_transaction_details_1.useTransactionDetails)();
    const { filterParams } = (0, use_wallet_search_1.useWalletSearch)();
    const { setLastPageIndex, setPrevPage, pagination: { page }, } = (0, use_transactions_details_dto_1.useTransactionDetailsDto)();
    (0, react_1.useEffect)(() => {
        if (data?.results.length === 0) {
            setLastPageIndex(page);
            setPrevPage();
        }
    }, [data?.results, page, setLastPageIndex, setPrevPage]);
    (0, react_1.useEffect)(() => {
        setLastPageIndex(undefined);
    }, [filterParams.address, filterParams.chainId, setLastPageIndex]);
    if (isPending) {
        return (<TransactionsTableBodyContainer_1.TransactionsTableBodyContainer>
				<CircularProgress_1.default />
			</TransactionsTableBodyContainer_1.TransactionsTableBodyContainer>);
    }
    if (isError) {
        return (<TransactionsTableBodyContainer_1.TransactionsTableBodyContainer>
				<div>{(0, handle_error_message_1.handleErrorMessage)(error)}</div>
			</TransactionsTableBodyContainer_1.TransactionsTableBodyContainer>);
    }
    if (!data.results.length) {
        return (<TransactionsTableBodyContainer_1.TransactionsTableBodyContainer>
				<div>No data</div>
			</TransactionsTableBodyContainer_1.TransactionsTableBodyContainer>);
    }
    return (<TableBody_1.default>
			{data.results.map((elem, idx) => (<TableRow_1.default key={idx}>
					<TableCell_1.default sx={{
                p: 0,
            }}>
						<AbbreviateClipboard_1.default value={elem.txHash}/>
					</TableCell_1.default>
					<TableCell_1.default>
						<TransactionTableCellMethod_1.TransactionTableCellMethod method={elem.method}/>
					</TableCell_1.default>
					<TableCell_1.default>{elem.block}</TableCell_1.default>
					<TableCell_1.default>
						<TransactionTableCellValue_1.TransactionTableCellValue value={elem.value}/>
					</TableCell_1.default>
					<TableCell_1.default>
						<AbbreviateClipboard_1.default value={elem.to}/>
					</TableCell_1.default>
				</TableRow_1.default>))}
		</TableBody_1.default>);
};
exports.TransactionsTableBody = TransactionsTableBody;
