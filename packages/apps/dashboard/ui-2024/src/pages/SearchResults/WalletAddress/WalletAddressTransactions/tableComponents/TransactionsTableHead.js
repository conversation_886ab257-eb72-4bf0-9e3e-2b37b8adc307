"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TransactionsTableHead = void 0;
const Typography_1 = __importDefault(require("@mui/material/Typography"));
const Stack_1 = __importDefault(require("@mui/material/Stack"));
const color_palette_1 = require("@assets/styles/color-palette");
const TableHead_1 = __importDefault(require("@mui/material/TableHead"));
const TableRow_1 = __importDefault(require("@mui/material/TableRow"));
const TableCell_1 = __importDefault(require("@mui/material/TableCell"));
const Tooltip_1 = __importDefault(require("@mui/material/Tooltip"));
const IconButton_1 = __importDefault(require("@mui/material/IconButton"));
const HelpOutline_1 = __importDefault(require("@mui/icons-material/HelpOutline"));
const TransactionsTableHead = () => {
    return (<TableHead_1.default sx={{
            borderBottom: `1px solid ${color_palette_1.colorPalette.fog.main}`,
        }}>
			<TableRow_1.default>
				<TableCell_1.default sx={{
            p: 0,
        }}>
					<Stack_1.default direction="row" alignItems="center">
						<Tooltip_1.default title="Transaction identifier">
							<IconButton_1.default sx={{ padding: 0, paddingRight: 1 }}>
								<HelpOutline_1.default fontSize="small"/>
							</IconButton_1.default>
						</Tooltip_1.default>
						<Typography_1.default variant="Components/Table Header">
							Transaction Hash
						</Typography_1.default>
					</Stack_1.default>
				</TableCell_1.default>
				<TableCell_1.default>
					<Stack_1.default direction="row" alignItems="center">
						<Tooltip_1.default title="Function executed in the transaction">
							<IconButton_1.default sx={{ padding: 0, paddingRight: 1 }}>
								<HelpOutline_1.default fontSize="small"/>
							</IconButton_1.default>
						</Tooltip_1.default>
						<Typography_1.default variant="Components/Table Header">Method</Typography_1.default>
					</Stack_1.default>
				</TableCell_1.default>
				<TableCell_1.default>
					<Stack_1.default direction="row" alignItems="center">
						<Tooltip_1.default title="Identifier of the block that contains the transaction">
							<IconButton_1.default sx={{ padding: 0, paddingRight: 1 }}>
								<HelpOutline_1.default fontSize="small"/>
							</IconButton_1.default>
						</Tooltip_1.default>
						<Typography_1.default variant="Components/Table Header">Block</Typography_1.default>
					</Stack_1.default>
				</TableCell_1.default>
				<TableCell_1.default>
					<Stack_1.default direction="row" alignItems="center">
						<Tooltip_1.default title="Amount of HMT transferred in the transaction">
							<IconButton_1.default sx={{ padding: 0, paddingRight: 1 }}>
								<HelpOutline_1.default fontSize="small"/>
							</IconButton_1.default>
						</Tooltip_1.default>
						<Typography_1.default variant="Components/Table Header">Value</Typography_1.default>
					</Stack_1.default>
				</TableCell_1.default>
				<TableCell_1.default>
					<Typography_1.default variant="Components/Table Header">
						Escrow Address
					</Typography_1.default>
				</TableCell_1.default>
			</TableRow_1.default>
		</TableHead_1.default>);
};
exports.TransactionsTableHead = TransactionsTableHead;
