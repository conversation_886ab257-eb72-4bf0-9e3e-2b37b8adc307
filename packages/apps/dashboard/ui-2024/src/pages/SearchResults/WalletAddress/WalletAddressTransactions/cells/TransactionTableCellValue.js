"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TransactionTableCellValue = void 0;
const formatHMTDecimals_1 = require("@helpers/formatHMTDecimals");
const Typography_1 = __importDefault(require("@mui/material/Typography"));
const use_hmt_price_1 = require("@services/api/use-hmt-price");
const TransactionTableCellValue = ({ value }) => {
    const { isError, isPending } = (0, use_hmt_price_1.useHMTPrice)();
    if (isError) {
        return 'N/A';
    }
    if (isPending) {
        return '...';
    }
    return (<Typography_1.default>
			{(0, formatHMTDecimals_1.formatHMTDecimals)(value)}
			<Typography_1.default component="span">HMT</Typography_1.default>
		</Typography_1.default>);
};
exports.TransactionTableCellValue = TransactionTableCellValue;
