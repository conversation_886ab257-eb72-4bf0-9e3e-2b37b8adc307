"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TransactionTableCellMethod = void 0;
const color_palette_1 = require("@assets/styles/color-palette");
const Box_1 = __importDefault(require("@mui/material/Box/Box"));
const Typography_1 = __importDefault(require("@mui/material/Typography"));
const material_1 = require("@mui/material");
const TransactionTableCellMethod = ({ method }) => {
    const methodAttributes = {
        transfer: {
            color: {
                text: color_palette_1.colorPalette.success.main,
                border: color_palette_1.colorPalette.success.light,
            },
        },
        complete: {
            color: {
                text: color_palette_1.colorPalette.success.main,
                border: color_palette_1.colorPalette.success.light,
            },
        },
        payout: {
            color: {
                text: color_palette_1.colorPalette.secondary.main,
                border: color_palette_1.colorPalette.secondary.light,
            },
        },
    };
    const currentStatusColors = methodAttributes[method]?.color || color_palette_1.colorPalette.success.main;
    return (<Box_1.default sx={{
            display: 'inline-flex',
            paddingX: 2,
            paddingY: 1,
            borderRadius: 4,
            border: `1px solid ${currentStatusColors.border}`,
        }}>
			<Typography_1.default>{(0, material_1.capitalize)(method)}</Typography_1.default>
		</Box_1.default>);
};
exports.TransactionTableCellMethod = TransactionTableCellMethod;
