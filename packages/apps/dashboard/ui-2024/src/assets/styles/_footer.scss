footer{
  min-height: 124px;
  margin-top: auto;
  @media(max-width: 1100px) {
    padding: 0 24px;
    flex-direction: column-reverse;
    justify-content: center;
    align-items: center;
    background-color: $maWhite;
  }

  .footer-wrapper {
    display: flex;
    padding: 32px 18px;
    flex-direction: row;
    justify-content: space-between;
    align-items: stretch;

    @media (max-width: 1100px) {
      flex-direction: column-reverse;
    }
  }

  .footer-link {
    gap: 24px;
    margin-bottom: 24px;
    align-items: flex-start;
  }

  .footer-icon {
    justify-content: flex-start;
    gap: 30px;
    margin-bottom: 0;
    align-items: flex-end;

    @media (max-width: 600px) {
      justify-content: space-between;
      align-items: flex-start;
      margin-bottom: 32px;
    }

    @media (max-width: 1100px) {
      margin-bottom: 32px;
    }
  }

  .footer-link, .footer-icon{
    display: flex;
    flex-wrap: wrap;
    span {
      cursor: pointer;
      font-size: 12px;
    }
    svg {
      font-size: 32px;
      cursor: pointer;
    }
  }

  @media (max-width: 1100px) {
    .footer-link-wrapper{
      .footer-link{
        flex-direction: column;
      }
    }
  }

  .footer-link, .footer-icon{
    justify-content: flex-start;
    align-items: flex-start;
  }
}
