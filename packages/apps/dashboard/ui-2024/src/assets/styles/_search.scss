.search, .search-white{
  position: relative;
  width: 920px;
  max-width: 100%;

  #search-bar{
    width: 100%;
    border-radius: 8px;
    line-height: 24px;

    &:focus, &:focus-visible {
      border-color: $secondary;
      outline: none;
    }
    &:-webkit-autofill {
      -webkit-text-fill-color: $primary;
      box-shadow: 0 0 0px 1000px $white inset;
    }
  }

  .search-close{
    cursor: pointer;
  }

  .search-button{
    background-color: $secondary;
    border-radius: 8px;
    svg{
      font-size: 32px;
    }
    &:hover{
      background-color: $primary;
    }
  }
}

.search-results-bar {
  display: block;

  @media (max-width: 1100px) {
    display: none;
  }
}

.search-white{
  width: 500px;
  max-width: 25%;

  .search-button{
    padding: 4px;
    background: #1406B214;
    svg{
      font-size: 24px;
    }
    &:hover{
      background-color: $secondary;
      svg{
        color: $white;
      }
    }
  }

  #search-bar{
    padding: 8px 0;

  }
  @media (max-width: 1150px) {
    max-width: 20%;
  }

  @media (max-width: 1100px) {
    max-width: 100%;

    #search-bar{
      padding: 12px 0;
    }
  }
}

@media (max-width: 1100px) {

  .search-white{
    padding: 8px 0;
  }
}
