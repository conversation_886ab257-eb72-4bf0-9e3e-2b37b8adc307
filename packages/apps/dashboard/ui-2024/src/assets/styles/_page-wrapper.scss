.page-wrapper{
  margin: 0 56px;
  min-height: calc(100vh - 206px);
  border-bottom-left-radius: 20px;
  border-bottom-right-radius: 20px;
  display: flex;
  flex-direction: column;
  padding: 14px 0;
  height: 100vh;


  @media (max-width: 1100px) {
  border-radius: 0;
    height: auto;
  }

  .container{
    margin: auto;
    padding: 30px 80px 100px;
    @media (max-width: 1100px) {
      padding: 30px 16px;
    }
  }

  @media (max-width: 1100px) {
    min-height: calc(100vh - 386px);
    margin: 0 24px;
    &.search-white-header{
      min-height: calc(100vh - 438px);
    }
  }

  @media (max-width: 600px) {
    margin: 0;
  }
}

.violet-header{
  z-index: 10;
  border-top-right-radius: 20px;
  border-top-left-radius: 20px;
  background-size: 100% 100%;
  background: linear-gradient(to bottom, $primary 252px, $maWhite 1px);

  @media (max-width: 600px) {
    border-top-left-radius: 0;
    border-top-right-radius: 0;
  }
}

.standard-background {
  background-color: $maWhite;
}
