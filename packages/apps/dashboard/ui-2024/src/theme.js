"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const styles_1 = require("@mui/material/styles");
const color_palette_1 = require("@assets/styles/color-palette");
const theme = (0, styles_1.createTheme)({
    palette: {
        primary: {
            main: color_palette_1.colorPalette.primary.main,
            light: color_palette_1.colorPalette.primary.light,
        },
        info: {
            main: color_palette_1.colorPalette.info.main,
            light: color_palette_1.colorPalette.info.light,
            dark: color_palette_1.colorPalette.info.dark,
        },
        secondary: {
            main: color_palette_1.colorPalette.secondary.main,
            light: color_palette_1.colorPalette.secondary.light,
        },
        text: {
            primary: color_palette_1.colorPalette.primary.main,
            secondary: color_palette_1.colorPalette.fog.main,
        },
        sky: {
            main: color_palette_1.colorPalette.sky.main,
            light: color_palette_1.colorPalette.sky.light,
            dark: color_palette_1.colorPalette.sky.dark,
            contrastText: color_palette_1.colorPalette.sky.contrastText,
        },
        white: {
            main: '#fff',
            light: '#fff',
            dark: '#fff',
            contrastText: '#fff',
        },
        textSecondary: color_palette_1.colorPalette.textSecondary,
    },
    typography: {
        fontFamily: 'Inter, Arial, sans-serif',
        h1: {
            fontSize: 32,
        },
        h2: {
            fontSize: 34,
            fontWeight: 600,
        },
        h3: {
            fontSize: 24,
            fontWeight: 500,
            '@media (max-width:600px)': {
                fontSize: 20,
            },
        },
        h4: {
            fontSize: 20,
            fontWeight: 500,
        },
        h5: {
            fontSize: 18,
            fontWeight: 600,
        },
        h6: {
            fontSize: 20,
            fontWeight: 500,
        },
        'H6-Mobile': {
            fontSize: '20px',
            fontWeight: 500,
            lineHeight: '32px',
            letterSpacing: '0.15px',
            textAlign: 'left',
        },
        body1: {
            fontSize: 16,
            fontWeight: 400,
        },
        body2: {
            fontSize: 14,
            fontWeight: 500,
        },
        body3: {
            fontSize: '12px',
            fontWeight: 400,
            lineHeight: '19.92px',
            letterSpacing: '0.4px',
            textAlign: 'left',
        },
        'Components/Button Small': {
            fontSize: '13px',
            fontWeight: 600,
            lineHeight: '22px',
            letterSpacing: '0.1px',
            textAlign: 'left',
        },
        'Components/Button Large': {
            fontSize: '15px',
            fontWeight: 600,
            lineHeight: '26px',
            letterSpacing: '0.1px',
            textAlign: 'left',
        },
        'Components/Chip': {
            fontSize: '13px',
            fontWeight: 400,
            lineHeight: '18px',
            letterSpacing: '0.16px',
            textAlign: 'left',
        },
        'Components/Table Header': {
            fontFamily: 'Roboto',
            fontSize: '14px',
            fontWeight: 500,
            lineHeight: '24px',
            letterSpacing: '0.17px',
            textAlign: 'left',
        },
        subtitle1: {
            fontSize: 12,
        },
        subtitle2: {
            fontSize: 14,
            fontWeight: 600,
            lineHeight: '21.9px',
        },
        caption: {
            fontSize: 10,
        },
    },
    components: {
        MuiButton: {
            styleOverrides: {
                root: {
                    fontWeight: 600,
                    textTransform: 'none',
                },
            },
        },
        MuiToolbar: {
            styleOverrides: {
                root: {
                    '@media (min-width:1001px)': {
                        paddingX: 56,
                    },
                },
            },
        },
        MuiTooltip: {
            styleOverrides: {
                tooltip: {
                    backgroundColor: '#320a8d',
                    color: '#fff',
                },
                arrow: {
                    color: '#320a8d',
                },
            },
        },
        MuiIconButton: {
            styleOverrides: {
                sizeMedium: {
                    color: color_palette_1.colorPalette.primary.main,
                },
            },
        },
        MuiSelect: {
            styleOverrides: {
                root: {
                    borderRadius: 4,
                    borderWidth: 2,
                    color: '#320a8d',
                    '& .MuiOutlinedInput-notchedOutline': {
                        borderColor: '#320a8d',
                        borderWidth: 2,
                    },
                    '&:hover .MuiOutlinedInput-notchedOutline': {
                        borderColor: '#320a8d',
                    },
                    '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                        borderColor: '#320a8d',
                    },
                    '& .MuiSvgIcon-root': {
                        color: '#320a8d',
                    },
                },
            },
        },
        MuiTypography: {
            styleOverrides: {
                root: {
                    wordBreak: 'break-word',
                },
            },
        },
        MuiOutlinedInput: {
            styleOverrides: {
                root: {
                    backgroundColor: color_palette_1.colorPalette.white,
                },
            },
        },
    },
});
exports.default = theme;
