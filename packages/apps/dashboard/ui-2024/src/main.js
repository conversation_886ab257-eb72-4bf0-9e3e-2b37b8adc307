"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const react_1 = __importDefault(require("react"));
const client_1 = __importDefault(require("react-dom/client"));
const styles_1 = require("@mui/material/styles");
const CssBaseline_1 = __importDefault(require("@mui/material/CssBaseline"));
const theme_1 = __importDefault(require("./theme"));
const App_1 = __importDefault(require("./App"));
require("@assets/styles/main.scss");
require("simplebar-react/dist/simplebar.min.css");
const react_query_1 = require("@tanstack/react-query");
const queryClient = new react_query_1.QueryClient({
    defaultOptions: {
        mutations: { retry: 0 },
        queries: { retry: 0 },
    },
});
client_1.default.createRoot(document.getElementById('root')).render(<styles_1.ThemeProvider theme={theme_1.default}>
		<CssBaseline_1.default />
		<react_1.default.StrictMode>
			<react_query_1.QueryClientProvider client={queryClient}>
				<App_1.default />
			</react_query_1.QueryClientProvider>
		</react_1.default.StrictMode>
	</styles_1.ThemeProvider>);
