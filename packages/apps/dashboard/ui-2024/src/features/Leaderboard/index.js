"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Leaderboard = void 0;
const color_palette_1 = require("@assets/styles/color-palette");
const Paper_1 = __importDefault(require("@mui/material/Paper"));
const TableContainer_1 = __importDefault(require("@mui/material/TableContainer"));
const react_router_dom_1 = require("react-router-dom");
const simplebar_react_1 = __importDefault(require("simplebar-react"));
const SelectNetwork_1 = require("./components/SelectNetwork");
const Table_1 = require("./components/Table");
const material_1 = require("@mui/material");
const Leaderboard = ({ data, status, error, viewAllBanner, }) => {
    const navigate = (0, react_router_dom_1.useNavigate)();
    return (<TableContainer_1.default component={Paper_1.default} sx={{
            padding: '32px',
            marginTop: '30px',
            borderRadius: '16px',
            boxShadow: 'none',
        }}>
			<div className="mobile-select">
				<SelectNetwork_1.SelectNetwork />
			</div>
			<simplebar_react_1.default>
				<Table_1.Table data={data} status={status} error={error}/>
			</simplebar_react_1.default>
			{viewAllBanner ? (<material_1.Button sx={{
                height: '42px',
                border: `1px ${color_palette_1.colorPalette.primary.main} solid`,
                borderRadius: '4px',
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center',
                cursor: 'pointer',
            }} fullWidth onClick={() => {
                navigate('/leaderboard');
            }}>
					<material_1.Typography variant="Components/Button Large">View All</material_1.Typography>
				</material_1.Button>) : null}
		</TableContainer_1.default>);
};
exports.Leaderboard = Leaderboard;
