"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TableHead = void 0;
const react_1 = __importDefault(require("react"));
const TableHead_1 = __importDefault(require("@mui/material/TableHead"));
const TableRow_1 = __importDefault(require("@mui/material/TableRow"));
const TableCell_1 = __importDefault(require("@mui/material/TableCell"));
const TableSortLabel_1 = __importDefault(require("@mui/material/TableSortLabel"));
const Tooltip_1 = __importDefault(require("@mui/material/Tooltip"));
const HelpOutline_1 = __importDefault(require("@mui/icons-material/HelpOutline"));
const SelectNetwork_1 = require("./SelectNetwork");
const color_palette_1 = require("@assets/styles/color-palette");
const use_is_mobile_1 = require("@utils/hooks/use-is-mobile");
const material_1 = require("@mui/material");
const TableHead = ({ orderBy, order, onRequestSort, }) => {
    const { mobile } = (0, use_is_mobile_1.useBreakPoints)();
    const createSortHandler = (property) => (event) => {
        onRequestSort(event, property);
    };
    return (<TableHead_1.default>
			<TableRow_1.default sx={{ whiteSpace: 'nowrap' }} className="home-page-table-header">
				{mobile.isMobile ? null : (<TableCell_1.default sx={{ minWidth: '52px' }}></TableCell_1.default>)}
				<TableCell_1.default sx={{
            minWidth: '300px',
            justifyContent: 'flex-start',
            [mobile.mediaQuery]: {
                minWidth: 'unset',
                position: 'sticky',
                left: 0,
                zIndex: 2,
                backgroundColor: color_palette_1.colorPalette.whiteSolid,
            },
        }} key="id" sortDirection={orderBy === 'role' ? order : false}>
					<TableSortLabel_1.default active={orderBy === 'role'} direction={orderBy === 'role' ? order : 'asc'} onClick={createSortHandler('role')}>
						<div className="icon-table">
							<material_1.Typography mt="3px" variant="body3">
								ROLE
							</material_1.Typography>
						</div>
					</TableSortLabel_1.default>
				</TableCell_1.default>
				<TableCell_1.default sx={{ minWidth: '240px', [mobile.mediaQuery]: { minWidth: 'unset' } }} key="address" sortDirection={orderBy === 'address' ? order : false}>
					<TableSortLabel_1.default active={orderBy === 'address'} direction={orderBy === 'address' ? order : 'asc'} onClick={createSortHandler('address')}>
						<div className="icon-table">
							<Tooltip_1.default title="Address of the role" arrow>
								<HelpOutline_1.default style={{
            color: color_palette_1.colorPalette.sky.main,
        }}/>
							</Tooltip_1.default>
							<material_1.Typography mt="3px" component="span" variant="body3">
								ADDRESS
							</material_1.Typography>
						</div>
					</TableSortLabel_1.default>
				</TableCell_1.default>
				<TableCell_1.default sx={{ minWidth: '246px', [mobile.mediaQuery]: { minWidth: 'unset' } }} key="amountStaked" sortDirection={orderBy === 'amountStaked' ? order : false}>
					<TableSortLabel_1.default active={orderBy === 'amountStaked'} direction={orderBy === 'amountStaked' ? order : 'asc'} onClick={createSortHandler('amountStaked')}>
						<div className="icon-table">
							<Tooltip_1.default title="Amount of HMT staked" arrow>
								<HelpOutline_1.default style={{
            color: color_palette_1.colorPalette.sky.main,
        }}/>
							</Tooltip_1.default>
							<material_1.Typography mt="3px" component="span" variant="body3">
								STAKE
							</material_1.Typography>
						</div>
					</TableSortLabel_1.default>
				</TableCell_1.default>
				<TableCell_1.default sx={{ minWidth: '246px', [mobile.mediaQuery]: { minWidth: 'unset' } }} className="table-filter-select">
					<SelectNetwork_1.SelectNetwork />
					<span className="mobile-title">NETWORK</span>
				</TableCell_1.default>
				<TableCell_1.default sx={{ minWidth: '246px', [mobile.mediaQuery]: { minWidth: 'unset' } }} key="reputation" sortDirection={orderBy === 'reputation' ? order : false}>
					<TableSortLabel_1.default active={orderBy === 'reputation'} direction={orderBy === 'reputation' ? order : 'asc'} onClick={createSortHandler('reputation')}>
						<div className="icon-table">
							<Tooltip_1.default title="Reputation of the role as per their activities " arrow>
								<HelpOutline_1.default style={{
            color: color_palette_1.colorPalette.sky.main,
        }}/>
							</Tooltip_1.default>
							<material_1.Typography mt="3px" component="span" variant="body3">
								REPUTATION SCORE
							</material_1.Typography>
						</div>
					</TableSortLabel_1.default>
				</TableCell_1.default>
				<TableCell_1.default sx={{ minWidth: '157px', [mobile.mediaQuery]: { minWidth: 'unset' } }} key="operator" sortDirection={orderBy === 'operator' ? order : false}>
					<TableSortLabel_1.default active={orderBy === 'fee'} direction={orderBy === 'fee' ? order : 'asc'} onClick={createSortHandler('fee')} sx={{
            display: 'flex',
            justifyContent: 'center',
            alignSelf: 'center',
        }}>
						<material_1.Typography mt="3px" variant="body3" component="div">
							OPERATOR FEE
						</material_1.Typography>
					</TableSortLabel_1.default>
				</TableCell_1.default>
			</TableRow_1.default>
		</TableHead_1.default>);
};
exports.TableHead = TableHead;
