"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Table = void 0;
const react_1 = __importStar(require("react"));
const TableCell_1 = __importStar(require("@mui/material/TableCell"));
const Table_1 = __importDefault(require("@mui/material/Table"));
const TableBody_1 = __importDefault(require("@mui/material/TableBody"));
const Grid_1 = __importDefault(require("@mui/material/Grid"));
const AbbreviateClipboard_1 = __importDefault(require("@components/SearchResults/AbbreviateClipboard"));
const ReputationLabel_1 = require("./ReputationLabel");
const EntityIcon_1 = require("./EntityIcon");
const TableHead_1 = require("./TableHead");
const sorting_1 = require("../helpers/sorting");
const use_leaderboard_search_1 = require("@utils/hooks/use-leaderboard-search");
const networks_1 = require("@utils/config/networks");
const NetworkIcon_1 = require("@components/NetworkIcon");
const color_palette_1 = require("@assets/styles/color-palette");
const material_1 = require("@mui/material");
const Stack_1 = __importDefault(require("@mui/material/Stack"));
const handle_error_message_1 = require("@services/handle-error-message");
const Loader_1 = __importDefault(require("@components/Loader"));
const use_is_mobile_1 = require("@utils/hooks/use-is-mobile");
const ROWS_SPACING = '4px';
const Table = ({ data = [], status, error, }) => {
    const { mobile } = (0, use_is_mobile_1.useBreakPoints)();
    const [visibleTablePartWidth, setVisibleTablePartWidth] = (0, react_1.useState)(0);
    const { filterParams: { chainId }, } = (0, use_leaderboard_search_1.useLeaderboardSearch)();
    const [order, setOrder] = (0, react_1.useState)('asc');
    const [orderBy, setOrderBy] = (0, react_1.useState)('role');
    const handleRequestSort = (_event, property) => {
        const isAsc = orderBy === property && order === 'asc';
        setOrder(isAsc ? 'desc' : 'asc');
        setOrderBy(property);
    };
    const visibleRows = (0, react_1.useMemo)(() => {
        let filteredRows = data;
        if (chainId !== -1) {
            filteredRows = data.filter((elem) => elem.chainId === chainId);
        }
        return (0, sorting_1.stableSort)(filteredRows, (0, sorting_1.getComparator)(order, orderBy));
    }, [chainId, data, order, orderBy]);
    const tableIsEmpty = status === 'success' && visibleRows.length === 0;
    const tableMinHeight = status === 'success' && !tableIsEmpty ? 'unset' : 400;
    const handleVisibleTablePart = (0, react_1.useCallback)(() => {
        const width = document.querySelector('.simplebar-scrollable-x')?.clientWidth;
        if (width) {
            setVisibleTablePartWidth(width);
        }
    }, []);
    (0, react_1.useEffect)(() => {
        handleVisibleTablePart();
        window.addEventListener('resize', handleVisibleTablePart);
        return () => {
            window.removeEventListener('resize', handleVisibleTablePart);
        };
    }, [handleVisibleTablePart, status]);
    (0, react_1.useEffect)(() => {
        handleVisibleTablePart();
        // eslint-disable-next-line react-hooks/exhaustive-deps -- ...
    }, []);
    return (<Table_1.default sx={{
            minWidth: 650,
            minHeight: tableMinHeight,
            borderCollapse: 'collapse',
            [`& .${TableCell_1.tableCellClasses.root}`]: {
                borderBottom: 'none',
            },
        }} aria-label="simple table">
			<TableHead_1.TableHead onRequestSort={handleRequestSort} order={order} orderBy={orderBy} rowCount={data.length}/>
			<TableBody_1.default sx={{
            position: 'relative',
        }}>
				{status === 'pending' ? (<TableBodyWrapper width={visibleTablePartWidth ? `${visibleTablePartWidth}px` : undefined}>
						<Loader_1.default height="30vh"/>
					</TableBodyWrapper>) : null}

				{status === 'error' ? (<TableBodyWrapper width={visibleTablePartWidth ? `${visibleTablePartWidth}px` : undefined}>
						{(0, handle_error_message_1.handleErrorMessage)(error)}
					</TableBodyWrapper>) : null}

				{tableIsEmpty ? (<TableBodyWrapper width={visibleTablePartWidth ? `${visibleTablePartWidth}px` : undefined}>
						No data
					</TableBodyWrapper>) : (<>
						{visibleRows.map((row, index) => (<material_1.TableRow key={index + row.address} className={'home-page-table-row'} sx={{
                    paddingTop: '1px',
                    borderTop: `4px solid ${color_palette_1.colorPalette.whiteBackground}`,
                    ':hover': {
                        backgroundColor: color_palette_1.colorPalette.overlay.light,
                    },
                    ':first-child': {
                        borderTop: `15px solid ${color_palette_1.colorPalette.whiteBackground}`,
                    },
                    ':last-child': {
                        borderBottom: `15px solid ${color_palette_1.colorPalette.whiteBackground}`,
                    },
                    textDecoration: 'none',
                }}>
								{mobile.isMobile ? null : (<TableCell_1.default sx={{ marginTop: '5px' }}>
										<material_1.Typography variant="body1">{index + 1}</material_1.Typography>
									</TableCell_1.default>)}
								<TableCell_1.default sx={{
                    justifyContent: 'flex-start',
                    [mobile.mediaQuery]: {
                        position: 'sticky',
                        left: 0,
                        zIndex: 2,
                        backgroundColor: color_palette_1.colorPalette.whiteBackground,
                        '&::after': {
                            ...getAfterElementProperties(index === 0, visibleRows.length === index + 1),
                            backgroundColor: `${color_palette_1.colorPalette.skyOpacity}`,
                        },
                    },
                }}>
									<Grid_1.default container wrap="nowrap" alignItems="center" gap="8px" justifyContent="flex-start">
										{mobile.isMobile ? (<>
												<material_1.Typography variant="subtitle2" sx={{ wordBreak: 'unset' }}>
													{row.role}
												</material_1.Typography>
											</>) : (<>
												<EntityIcon_1.EntityIcon role={row.role}/>
												<material_1.Typography variant="h6" sx={{ wordBreak: 'unset' }}>
													{row.role}
												</material_1.Typography>
											</>)}
									</Grid_1.default>
								</TableCell_1.default>
								<TableCell_1.default sx={{ justifyContent: 'flex-start' }}>
									<Grid_1.default container wrap="nowrap" alignItems="center" sx={{ gap: '18px' }}>
										<AbbreviateClipboard_1.default value={row.address} link={`/search/${row.chainId}/${row.address}`}/>
									</Grid_1.default>
								</TableCell_1.default>
								<TableCell_1.default sx={{ justifyContent: 'flex-start' }}>
									<material_1.Typography variant="body1">
										{row.amountStaked} HMT
									</material_1.Typography>
								</TableCell_1.default>
								<TableCell_1.default>
									<material_1.Typography component="div" variant="body1">
										<Grid_1.default whiteSpace="nowrap" container wrap="nowrap" alignItems="center" justifyContent="flex-start" gap="6px">
											<NetworkIcon_1.NetworkIcon chainId={row.chainId}/>
											{(0, networks_1.getNetwork)(row.chainId)?.name}
										</Grid_1.default>
									</material_1.Typography>
								</TableCell_1.default>
								<TableCell_1.default sx={{ justifyContent: 'flex-start' }}>
									<ReputationLabel_1.ReputationLabel reputation={row.reputation}/>
								</TableCell_1.default>
								<TableCell_1.default>{row.fee}%</TableCell_1.default>
							</material_1.TableRow>))}
					</>)}
			</TableBody_1.default>
		</Table_1.default>);
};
exports.Table = Table;
function TableBodyWrapper({ children, width = '100%', }) {
    return (<Stack_1.default component="tr" sx={{
            position: 'absolute',
            top: 0,
            left: 0,
            width,
            height: '100%',
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
        }}>
			<th>{children}</th>
		</Stack_1.default>);
}
function getAfterElementProperties(isFirstElement, isLastElement) {
    if (isFirstElement) {
        return {
            content: '""',
            position: 'absolute',
            bottom: '0',
            right: '0',
            height: '100%',
            width: '1px',
        };
    }
    if (isLastElement) {
        return {
            content: '""',
            position: 'absolute',
            bottom: '0',
            right: '0',
            height: `calc(100% + ${ROWS_SPACING})`,
            width: '1px',
        };
    }
    return {
        content: '""',
        position: 'absolute',
        bottom: '0',
        right: '0',
        height: `calc(100% + ${ROWS_SPACING})`,
        width: '1px',
    };
}
