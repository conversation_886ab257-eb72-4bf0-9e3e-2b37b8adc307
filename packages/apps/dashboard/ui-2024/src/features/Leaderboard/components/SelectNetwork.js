"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SelectNetwork = void 0;
const Select_1 = __importDefault(require("@mui/material/Select"));
const FormControl_1 = __importDefault(require("@mui/material/FormControl"));
const InputLabel_1 = __importDefault(require("@mui/material/InputLabel"));
const MenuItem_1 = __importDefault(require("@mui/material/MenuItem"));
const HumanIcon_1 = __importDefault(require("@components/Icons/HumanIcon"));
const use_leaderboard_search_1 = require("@utils/hooks/use-leaderboard-search");
const NetworkIcon_1 = require("@components/NetworkIcon");
const SelectNetwork = () => {
    const { setChainId, filterParams: { chainId }, } = (0, use_leaderboard_search_1.useLeaderboardSearch)();
    const handleChange = (event) => {
        const value = event.target.value;
        if (typeof value === 'number') {
            setChainId(value);
        }
    };
    return (<FormControl_1.default fullWidth size="small" sx={{ width: '210px' }}>
			<InputLabel_1.default id="network-select-label">By Network</InputLabel_1.default>
			<Select_1.default labelId="network-select-label" id="network-select" value={chainId} label="By Network" onChange={handleChange}>
				{use_leaderboard_search_1.leaderboardSearchSelectConfig.map((selectItem) => {
            if ('allNetworksId' in selectItem) {
                return (<MenuItem_1.default key={selectItem.name} className="select-item" value={-1}>
								<HumanIcon_1.default />
								All Networks
							</MenuItem_1.default>);
            }
            return (<MenuItem_1.default key={selectItem.name} className="select-item" value={selectItem.id}>
							<NetworkIcon_1.NetworkIcon chainId={selectItem.id}/>
							{selectItem.name}
						</MenuItem_1.default>);
        })}
			</Select_1.default>
		</FormControl_1.default>);
};
exports.SelectNetwork = SelectNetwork;
