"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.EntityIcon = void 0;
const ExchangeOracleIcon_1 = require("@components/Icons/ExchangeOracleIcon");
const HumanIcon_1 = __importDefault(require("@components/Icons/HumanIcon"));
const JobLauncher_1 = require("@components/Icons/JobLauncher");
const RecordingOracle_1 = require("@components/Icons/RecordingOracle");
const ReputationOracle_1 = require("@components/Icons/ReputationOracle");
const EntityIcon = ({ role }) => {
    switch (role) {
        case 'Job Launcher':
            return <JobLauncher_1.JobLauncher />;
        case 'Recording Oracle':
            return <RecordingOracle_1.RecordingOracle />;
        case 'Reputation Oracle':
            return <ReputationOracle_1.ReputationOracle />;
        case 'Exchange Oracle':
            return <ExchangeOracleIcon_1.ExchangeOracleIcon />;
        case 'HUMAN App':
            return <HumanIcon_1.default />;
        default:
            return <HumanIcon_1.default />;
    }
};
exports.EntityIcon = EntityIcon;
