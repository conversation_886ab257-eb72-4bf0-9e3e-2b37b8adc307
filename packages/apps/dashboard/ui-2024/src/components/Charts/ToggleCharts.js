"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const material_1 = require("@mui/material");
const Stack_1 = __importDefault(require("@mui/material/Stack"));
const Checkbox_1 = __importDefault(require("@mui/material/Checkbox"));
const color_palette_1 = require("@assets/styles/color-palette");
const FormatNumber_1 = require("@components/Home/FormatNumber");
const ToggleCharts = ({ handleChange, chartOptions, onMouseLeave, onMouseEnter, }) => {
    return (<material_1.FormGroup sx={{
            marginX: { sx: 4, md: 0 },
        }}>
			<Stack_1.default gap={{ xs: 2, md: 6 }} direction={{ xs: 'column', md: 'row' }} justifyContent="center">
				{chartOptions.map((elem) => (<material_1.FormControlLabel onMouseEnter={() => onMouseEnter ? onMouseEnter(elem.name) : undefined} onMouseLeave={() => (onMouseLeave ? onMouseLeave() : undefined)} id={elem.name} key={elem.name} sx={{
                m: 0,
                gap: 1,
            }} control={<Checkbox_1.default name={elem.name} onChange={handleChange} defaultChecked sx={{
                    '&.Mui-checked': {
                        color: elem.color,
                    },
                }}/>} label={<>
								<material_1.Typography variant="subtitle2">{elem.title}</material_1.Typography>
								<material_1.Typography variant="h4" component="p">
									{elem.amount ? <FormatNumber_1.FormatNumber value={elem.amount}/> : ''}
									{elem.name === 'totalTransactionAmount' &&
                    elem.isAreaChart && (<material_1.Typography variant="h4" component="span" sx={{
                        marginLeft: 1,
                        color: color_palette_1.colorPalette.fog.main,
                    }}>
												HMT
											</material_1.Typography>)}
								</material_1.Typography>
							</>}/>))}
			</Stack_1.default>
		</material_1.FormGroup>);
};
exports.default = ToggleCharts;
