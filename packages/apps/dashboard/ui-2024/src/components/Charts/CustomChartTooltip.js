"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const Card_1 = __importDefault(require("@mui/material/Card"));
const Box_1 = __importDefault(require("@mui/material/Box"));
const material_1 = require("@mui/material");
const Stack_1 = __importDefault(require("@mui/material/Stack"));
const FiberManualRecord_1 = __importDefault(require("@mui/icons-material/FiberManualRecord"));
const color_palette_1 = require("@assets/styles/color-palette");
const formatDate_1 = require("@helpers/formatDate");
const renderTitle = (title) => {
    const currentTitle = {
        totalTransactionAmount: 'Transfer Amount',
        totalTransactionCount: 'Transactions Count',
        solved: 'Number of Tasks',
        dailyUniqueReceivers: 'Unique Receivers',
        dailyUniqueSenders: 'Unique Senders',
    };
    return currentTitle[title];
};
const CustomChartTooltip = ({ payload, label, active, }) => {
    if (active) {
        return (<Card_1.default sx={{
                border: `1px solid ${color_palette_1.colorPalette.fog.light}`,
                borderRadius: '10px',
            }}>
				<Box_1.default sx={{
                padding: '6px 10px',
            }}>
					<material_1.Typography color={color_palette_1.colorPalette.fog.main} variant="subtitle1" fontWeight={500}>
						{(0, formatDate_1.formatDate)(label, 'MMMM DD, YYYY')}
					</material_1.Typography>
					{payload?.map((elem) => (<Box_1.default key={elem.name} sx={{
                    display: 'grid',
                    gap: 1,
                    gridTemplateColumns: 'repeat(2, 1fr)',
                }}>
							<Stack_1.default direction="row" alignItems="center" gap={1} width="100%">
								<material_1.Grid container alignItems="center" gap={1}>
									<FiberManualRecord_1.default sx={{
                    color: elem.stroke,
                    fontSize: '12px',
                }}/>
									<material_1.Typography fontWeight={500} variant="subtitle1">
										{renderTitle(elem.name ?? '')}
									</material_1.Typography>
								</material_1.Grid>
							</Stack_1.default>
							<material_1.Grid container width="100%">
								<material_1.Typography whiteSpace="nowrap" textAlign="start" variant="subtitle2">
									{elem.value}{' '}
									{elem.name === 'totalTransactionAmount' ? 'HMT' : ''}
								</material_1.Typography>
							</material_1.Grid>
						</Box_1.default>))}
				</Box_1.default>
			</Card_1.default>);
    }
    return null;
};
exports.default = CustomChartTooltip;
