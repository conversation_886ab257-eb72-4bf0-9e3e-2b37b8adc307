"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AreaChart = void 0;
const recharts_1 = require("recharts");
const CustomChartTooltip_1 = __importDefault(require("./CustomChartTooltip"));
const react_1 = require("react");
const Card_1 = __importDefault(require("@mui/material/Card"));
const material_1 = require("@mui/material");
const Stack_1 = __importDefault(require("@mui/material/Stack"));
const color_palette_1 = require("@assets/styles/color-palette");
const CustomXAxisTick_1 = __importDefault(require("@components/Charts/CustomXAxisTick"));
const DatePicker_1 = __importDefault(require("@components/DataEntry/DatePicker"));
const ToggleButtons_1 = __importDefault(require("@components/DataEntry/ToggleButtons"));
const ToggleCharts_1 = __importDefault(require("@components/Charts/ToggleCharts"));
const formatNumber_1 = require("@helpers/formatNumber");
const use_graph_page_chart_data_1 = require("@services/api/use-graph-page-chart-data");
const use_graph_page_chart_params_1 = require("@utils/hooks/use-graph-page-chart-params");
const CHECKED_CHARTS_DEFAULT_STATE = {
    totalTransactionAmount: true,
    totalTransactionCount: true,
    solved: true,
    dailyUniqueReceivers: true,
    dailyUniqueSenders: true,
};
const HOVERED_CHARTS_DEFAULT_STATE = {
    totalTransactionAmount: false,
    totalTransactionCount: false,
    solved: false,
    dailyUniqueReceivers: false,
    dailyUniqueSenders: false,
};
const sumNumericProperties = (chartData) => {
    return chartData.reduce((acc, chartEntry) => {
        acc.dailyUniqueReceivers += chartEntry.dailyUniqueReceivers;
        acc.dailyUniqueSenders += chartEntry.dailyUniqueSenders;
        acc.solved += chartEntry.solved;
        acc.totalTransactionAmount += chartEntry.totalTransactionAmount;
        acc.totalTransactionCount += chartEntry.totalTransactionCount;
        return acc;
    }, {
        dailyUniqueReceivers: 0,
        dailyUniqueSenders: 0,
        solved: 0,
        totalTransactionAmount: 0,
        totalTransactionCount: 0,
    });
};
const AreaChart = ({ changeDateOnScroll = false, }) => {
    const { data } = (0, use_graph_page_chart_data_1.useGraphPageChartData)();
    const chartData = data || [];
    const { setFromDate, setToDate, clearTimePeriod, dateRangeParams: { from, to }, effectiveFromAllTimeDate, } = (0, use_graph_page_chart_params_1.useGraphPageChartParams)();
    const sum = sumNumericProperties(chartData);
    const [checkedCharts, setCheckedCharts] = (0, react_1.useState)(CHECKED_CHARTS_DEFAULT_STATE);
    const chartRef = (0, react_1.useRef)(null);
    const [currentHoveredChart, setCurrentHoveredChart] = (0, react_1.useState)(HOVERED_CHARTS_DEFAULT_STATE);
    const toggleChart = (event) => {
        setCheckedCharts((prevState) => ({
            ...prevState,
            [event.target.name]: event.target.checked,
        }));
    };
    const onFromDateChange = (value) => {
        if (value)
            setFromDate(value);
    };
    const onToDateChange = (value) => {
        if (value)
            setToDate(value);
    };
    const onChartHover = (name) => {
        setCurrentHoveredChart((prevState) => ({
            ...prevState,
            [name]: true,
        }));
    };
    const onChartLeave = () => {
        setCurrentHoveredChart(HOVERED_CHARTS_DEFAULT_STATE);
    };
    (0, react_1.useEffect)(() => {
        if (!changeDateOnScroll) {
            return;
        }
        const currentRef = chartRef.current;
        if (currentRef) {
            const handleScrollChangeDate = (event) => {
                event.preventDefault();
                clearTimePeriod();
                if (event.deltaY < 0) {
                    if (from.add(1, 'day').isAfter(to)) {
                        return;
                    }
                    setFromDate(from.add(1, 'day'));
                }
                else if (event.deltaY > 0) {
                    if (effectiveFromAllTimeDate?.isSame(from)) {
                        return;
                    }
                    setFromDate(from.subtract(1, 'day'));
                }
            };
            currentRef.addEventListener('wheel', handleScrollChangeDate);
            return () => {
                currentRef.removeEventListener('wheel', handleScrollChangeDate);
            };
        }
    }, [
        changeDateOnScroll,
        clearTimePeriod,
        effectiveFromAllTimeDate,
        from,
        setFromDate,
        to,
    ]);
    return (<Card_1.default sx={{
            paddingY: { xs: 4, md: 4 },
            paddingX: { xs: 2, md: 8 },
        }}>
			<Stack_1.default sx={{ marginBottom: 4 }} direction={{ xs: 'column', md: 'row' }} gap={{ xs: 6, md: 8 }}>
				<ToggleButtons_1.default />
				<Stack_1.default direction="row" alignItems="center" gap={2}>
					<DatePicker_1.default onChange={onFromDateChange} value={from} customProps={{
            disableFuture: true,
            maxDate: to,
            minDate: effectiveFromAllTimeDate,
        }}/>
					<material_1.Typography>-</material_1.Typography>
					<DatePicker_1.default onChange={onToDateChange} value={to} customProps={{
            disableFuture: true,
            minDate: from,
        }}/>
				</Stack_1.default>
			</Stack_1.default>
			<recharts_1.ResponsiveContainer ref={chartRef} height={300}>
				<recharts_1.AreaChart data={chartData}>
					<defs>
						<linearGradient id="colorTotalTransactionAmount" x1="0" y1="0" x2="0" y2="1">
							<stop offset={currentHoveredChart.totalTransactionAmount ? '20%' : '30%'} stopColor="#330B8D33" stopOpacity={currentHoveredChart.totalTransactionAmount ? 1 : 0.2}/>
							<stop offset={currentHoveredChart.totalTransactionAmount ? '90%' : '60%'} stopColor="#330B8D00" stopOpacity={currentHoveredChart.totalTransactionAmount ? 1 : 0}/>
						</linearGradient>
						<linearGradient id="colorTotalTransactionCount" x1="0" y1="0" x2="0" y2="1">
							<stop offset={currentHoveredChart.totalTransactionCount ? '20%' : '30%'} stopColor="#6309FF26" stopOpacity={currentHoveredChart.totalTransactionCount ? 1 : 0.2}/>
							<stop offset={currentHoveredChart.totalTransactionCount ? '90%' : '60%'} stopColor="#6309FF00" stopOpacity={currentHoveredChart.totalTransactionCount ? 1 : 0}/>
						</linearGradient>
						<linearGradient id="colorSolved" x1="0" y1="0" x2="0" y2="1">
							<stop offset={currentHoveredChart.solved ? '20%' : '30%'} stopColor="#03a9f480" stopOpacity={currentHoveredChart.solved ? 1 : 0.2}/>
							<stop offset={currentHoveredChart.solved ? '90%' : '60%'} stopColor="#03a9f400" stopOpacity={currentHoveredChart.solved ? 1 : 0}/>
						</linearGradient>
						<linearGradient id="colorDailyUniqueReceivers" x1="0" y1="0" x2="0" y2="1">
							<stop offset={currentHoveredChart.dailyUniqueReceivers ? '20%' : '30%'} stopColor="#F20D5F33" stopOpacity={currentHoveredChart.dailyUniqueReceivers ? 1 : 0.2}/>
							<stop offset={currentHoveredChart.dailyUniqueReceivers ? '90%' : '60%'} stopColor="#F20D5F00" stopOpacity={currentHoveredChart.dailyUniqueReceivers ? 1 : 0}/>
						</linearGradient>
						<linearGradient id="colorDailyUniqueSenders" x1="0" y1="0" x2="0" y2="1">
							<stop offset={currentHoveredChart.dailyUniqueSenders ? '20%' : '30%'} stopColor="#0AD39780" stopOpacity={currentHoveredChart.dailyUniqueSenders ? 1 : 0.2}/>
							<stop offset={currentHoveredChart.dailyUniqueSenders ? '90%' : '70%'} stopColor="#0AD39700" stopOpacity={currentHoveredChart.dailyUniqueSenders ? 1 : 0}/>
						</linearGradient>
					</defs>
					<recharts_1.YAxis tickFormatter={formatNumber_1.formatNumber} tick={{ dx: -10 }} tickSize={0} axisLine={false} stroke={color_palette_1.colorPalette.fog.main}/>
					<recharts_1.CartesianGrid stroke="#ccc" strokeDasharray="5" vertical={false}/>
					<recharts_1.XAxis axisLine={false} tick={<CustomXAxisTick_1.default />} height={50} stroke={color_palette_1.colorPalette.fog.dark} tickSize={20} dataKey="date" tickMargin={10}/>
					<recharts_1.Tooltip content={<CustomChartTooltip_1.default />}/>
					{checkedCharts.totalTransactionAmount && (<recharts_1.Area type="monotone" dataKey="totalTransactionAmount" stroke={color_palette_1.colorPalette.primary.main} fillOpacity={1} fill="url(#colorTotalTransactionAmount)"/>)}
					{checkedCharts.totalTransactionCount && (<recharts_1.Area type="monotone" dataKey="totalTransactionCount" stroke={color_palette_1.colorPalette.secondary.main} fillOpacity={1} fill="url(#colorTotalTransactionCount)"/>)}
					{checkedCharts.solved && (<recharts_1.Area type="monotone" dataKey="solved" stroke={color_palette_1.colorPalette.ocean.dark} fillOpacity={1} fill="url(#colorSolved)"/>)}
					{checkedCharts.dailyUniqueReceivers && (<recharts_1.Area type="monotone" dataKey="dailyUniqueReceivers" stroke={color_palette_1.colorPalette.error.light} fillOpacity={1} fill="url(#colorDailyUniqueReceivers)"/>)}
					{checkedCharts.dailyUniqueSenders && (<recharts_1.Area type="monotone" dataKey="dailyUniqueSenders" stroke={color_palette_1.colorPalette.success.main} fillOpacity={1} fill="url(#colorDailyUniqueSenders)"/>)}
				</recharts_1.AreaChart>
			</recharts_1.ResponsiveContainer>
			<Card_1.default sx={{
            paddingY: 3,
            marginTop: 3,
            marginLeft: { xs: 0, xl: 6 },
            backgroundColor: color_palette_1.colorPalette.overlay.light,
        }}>
				<ToggleCharts_1.default handleChange={toggleChart} onMouseLeave={onChartLeave} onMouseEnter={onChartHover} chartOptions={[
            {
                title: 'Transfer Amount',
                isAreaChart: true,
                name: 'totalTransactionAmount',
                amount: `${Number(sum.totalTransactionAmount.toFixed())}`,
                color: color_palette_1.colorPalette.primary.main,
            },
            {
                title: 'Transactions Count',
                name: 'totalTransactionCount',
                amount: sum.totalTransactionCount,
                color: color_palette_1.colorPalette.secondary.main,
            },
            {
                title: 'Number of Tasks',
                name: 'solved',
                amount: sum.solved,
                color: color_palette_1.colorPalette.ocean.dark,
            },
            {
                title: 'Unique Receivers',
                name: 'dailyUniqueReceivers',
                amount: sum.dailyUniqueReceivers,
                color: color_palette_1.colorPalette.error.light,
            },
            {
                title: 'Unique Senders',
                name: 'dailyUniqueSenders',
                amount: sum.dailyUniqueSenders,
                color: color_palette_1.colorPalette.success.main,
            },
        ]}/>
			</Card_1.default>
		</Card_1.default>);
};
exports.AreaChart = AreaChart;
