"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const color_palette_1 = require("@assets/styles/color-palette");
const formatDate_1 = require("@helpers/formatDate");
const CustomXAxisTick = ({ x, y, payload }) => {
    return (<g transform={`translate(${x},${y})`}>
			<text x={-30} y={0} fill={color_palette_1.colorPalette.fog.main} transform="rotate(-35)" fontSize={10} fontWeight={500}>
				{(0, formatDate_1.formatDate)(payload.value, 'D MMM')}
			</text>
		</g>);
};
exports.default = CustomXAxisTick;
