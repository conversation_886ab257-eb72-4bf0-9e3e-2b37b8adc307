"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.EscrowAddressIcon = void 0;
const SvgIcon_1 = __importDefault(require("@mui/material/SvgIcon"));
const EscrowAddressIcon = (props) => {
    return (<SvgIcon_1.default {...props} style={{
            width: '130',
            height: '130',
        }}>
			<svg xmlns="http://www.w3.org/2000/svg" width="130" height="130" viewBox="0 0 130 130" fill="none">
				<g filter="url(#filter0_d_1100_4659)">
					<path d="M98 41C98 59.2254 83.2254 74 65 74C46.7746 74 32 59.2254 32 41C32 22.7746 46.7746 8 65 8C83.2254 8 98 22.7746 98 41Z" fill="url(#paint0_radial_1100_4659)"/>
				</g>
				<g filter="url(#filter1_d_1100_4659)">
					<path fillRule="evenodd" clipRule="evenodd" d="M65 69.9304C80.9778 69.9304 93.9304 56.9778 93.9304 41C93.9304 25.0222 80.9778 12.0696 65 12.0696C49.0222 12.0696 36.0696 25.0222 36.0696 41C36.0696 56.9778 49.0222 69.9304 65 69.9304ZM65 74C83.2254 74 98 59.2254 98 41C98 22.7746 83.2254 8 65 8C46.7746 8 32 22.7746 32 41C32 59.2254 46.7746 74 65 74Z" fill="url(#paint1_linear_1100_4659)"/>
				</g>
				<path fillRule="evenodd" clipRule="evenodd" d="M80.8426 57.587V37.4303L72.2985 28.9478H58.662V44.7379C58.1729 44.6015 57.657 44.5286 57.124 44.5286C53.971 44.5286 51.4209 47.0787 51.4209 50.2317C51.4209 54.5091 57.124 60.8232 57.124 60.8232C57.124 60.8232 58.3485 59.4676 59.6627 57.587H80.8426ZM58.662 48.8968V51.5667C58.2885 51.9966 57.7378 52.2686 57.124 52.2686C55.9997 52.2686 55.0872 51.3561 55.0872 50.2317C55.0872 49.1074 55.9997 48.1949 57.124 48.1949C57.7378 48.1949 58.2885 48.4669 58.662 48.8968Z" fill="url(#paint2_linear_1100_4659)"/>
				<path d="M77.7353 33.548V53.7047H55.5547V25.0654H69.1912" stroke="#320A8D" strokeWidth="1.2" strokeMiterlimit="6.2" strokeLinecap="round" strokeLinejoin="round"/>
				<path d="M77.4401 33.8412H69.1913C69.0277 33.8412 68.896 33.7104 68.896 33.548V25.3586C68.896 25.1962 69.0277 25.0654 69.1913 25.0654L77.7354 33.548C77.7354 33.7104 77.6037 33.8412 77.4401 33.8412Z" stroke="#320A8D" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round"/>
				<path d="M60.4233 43.7734H73.2491" stroke="#320A8D" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round"/>
				<path d="M60.4233 39.061H73.2491" stroke="#320A8D" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round"/>
				<path d="M60.4233 48.4863H73.2491" stroke="#320A8D" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round"/>
				<circle cx="54.1398" cy="46.4533" r="2.71893" fill="#F7F7FE"/>
				<ellipse cx="55.3517" cy="49.4591" rx="2.69898" ry="5.04944" transform="rotate(27.1851 55.3517 49.4591)" fill="url(#paint3_linear_1100_4659)"/>
				<path d="M54.0166 56.0239C53.8944 55.879 53.751 55.7063 53.5914 55.5096C53.0644 54.8603 52.3628 53.9519 51.6624 52.9138C50.9609 51.874 50.2679 50.7149 49.7522 49.5637C49.2329 48.4046 48.9135 47.2975 48.9135 46.3496C48.9135 43.528 51.195 41.2465 54.0166 41.2465C56.8382 41.2465 59.1197 43.528 59.1197 46.3496C59.1197 47.2975 58.8003 48.4046 58.281 49.5637C57.7653 50.7149 57.0723 51.874 56.3708 52.9138C55.6704 53.9519 54.9688 54.8603 54.4418 55.5096C54.2822 55.7063 54.1388 55.879 54.0166 56.0239ZM51.3798 46.3496C51.3798 47.8053 52.5609 48.9864 54.0166 48.9864C55.4723 48.9864 56.6534 47.8053 56.6534 46.3496C56.6534 44.8939 55.4723 43.7128 54.0166 43.7128C52.5609 43.7128 51.3798 44.8939 51.3798 46.3496Z" fill="#F7F7FE" stroke="#320A8D" strokeWidth="1.2"/>
				<defs>
					<filter id="filter0_d_1100_4659" x="0" y="0" width="130" height="130" filterUnits="userSpaceOnUse" colorInterpolationFilters="sRGB">
						<feFlood floodOpacity="0" result="BackgroundImageFix"/>
						<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
						<feOffset dy="24"/>
						<feGaussianBlur stdDeviation="16"/>
						<feComposite in2="hardAlpha" operator="out"/>
						<feColorMatrix type="matrix" values="0 0 0 0 0.0486111 0 0 0 0 0.127083 0 0 0 0 0.833333 0 0 0 0.06 0"/>
						<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1100_4659"/>
						<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1100_4659" result="shape"/>
					</filter>
					<filter id="filter1_d_1100_4659" x="0" y="0" width="130" height="130" filterUnits="userSpaceOnUse" colorInterpolationFilters="sRGB">
						<feFlood floodOpacity="0" result="BackgroundImageFix"/>
						<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
						<feOffset dy="24"/>
						<feGaussianBlur stdDeviation="16"/>
						<feComposite in2="hardAlpha" operator="out"/>
						<feColorMatrix type="matrix" values="0 0 0 0 0.0486111 0 0 0 0 0.127083 0 0 0 0 0.833333 0 0 0 0.06 0"/>
						<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1100_4659"/>
						<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1100_4659" result="shape"/>
					</filter>
					<radialGradient id="paint0_radial_1100_4659" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(65 18.6895) rotate(90) scale(55.3105)">
						<stop stopColor="#F0F0FF"/>
						<stop stopColor="#F1F1FD"/>
						<stop offset="0.703125" stopColor="white"/>
					</radialGradient>
					<linearGradient id="paint1_linear_1100_4659" x1="104.526" y1="74" x2="109.495" y2="52.1101" gradientUnits="userSpaceOnUse">
						<stop stopColor="#F7F8FD"/>
						<stop offset="1" stopColor="white"/>
					</linearGradient>
					<linearGradient id="paint2_linear_1100_4659" x1="56.631" y1="28.9478" x2="90.1448" y2="49.5609" gradientUnits="userSpaceOnUse">
						<stop stopColor="#244CB3" stopOpacity="0.2"/>
						<stop offset="1" stopColor="#B4C2E5" stopOpacity="0.07"/>
					</linearGradient>
					<linearGradient id="paint3_linear_1100_4659" x1="53.6086" y1="44.4097" x2="61.1294" y2="47.0884" gradientUnits="userSpaceOnUse">
						<stop stopColor="#244CB3" stopOpacity="0.2"/>
						<stop offset="1" stopColor="#B4C2E5" stopOpacity="0.07"/>
					</linearGradient>
				</defs>
			</svg>
		</SvgIcon_1.default>);
};
exports.EscrowAddressIcon = EscrowAddressIcon;
