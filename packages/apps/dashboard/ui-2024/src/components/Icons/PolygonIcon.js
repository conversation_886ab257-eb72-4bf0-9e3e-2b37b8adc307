"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PolygonIcon = void 0;
const SvgIcon_1 = __importDefault(require("@mui/material/SvgIcon"));
const PolygonIcon = (props) => {
    return (<SvgIcon_1.default width="17" height="15" viewBox="0 0 17 15" fill="none" {...props}>
			<path d="M12.3951 4.42362C12.2384 4.33332 12.0607 4.28579 11.8799 4.28579C11.699 4.28579 11.5214 4.33332 11.3647 4.42362L9.00084 5.83178L7.39466 6.75017L5.03082 8.15839C4.87413 8.24867 4.69647 8.2962 4.51563 8.2962C4.3348 8.2962 4.15714 8.24867 4.00045 8.15839L2.12152 7.05633C1.96733 6.96426 1.83913 6.83445 1.74899 6.67912C1.65886 6.5238 1.60976 6.34808 1.60632 6.16853V3.99503C1.60333 3.81419 1.64972 3.63596 1.7405 3.47952C1.83128 3.32309 1.96302 3.19438 2.12152 3.10726L3.97015 2.03581C4.12684 1.94552 4.3045 1.89799 4.48535 1.89799C4.66619 1.89799 4.84386 1.94552 5.00055 2.03581L6.84918 3.10726C7.00335 3.19933 7.13154 3.32914 7.22167 3.48446C7.3118 3.63978 7.3609 3.81549 7.36434 3.99503V5.40322L8.97052 4.45423V3.04602C8.97353 2.86518 8.92714 2.68694 8.83636 2.53051C8.74559 2.37408 8.61385 2.24537 8.45536 2.15825L5.03082 0.137821C4.87413 0.0475279 4.69647 0 4.51563 0C4.3348 0 4.15713 0.0475279 4.00045 0.137821L0.515322 2.15827C0.356826 2.24539 0.225095 2.37409 0.134315 2.53052C0.0435349 2.68695 -0.0028564 2.86518 0.000136415 3.04602V7.11754C-0.00285917 7.29837 0.0435316 7.47661 0.134314 7.63304C0.225096 7.78947 0.35683 7.91817 0.515331 8.00529L4.00046 10.0257C4.15714 10.116 4.3348 10.1636 4.51564 10.1636C4.69649 10.1636 4.87415 10.116 5.03083 10.0257L7.39467 8.64815L9.00085 7.69917L11.3647 6.32159C11.5214 6.23131 11.699 6.18378 11.8799 6.18378C12.0607 6.18378 12.2384 6.23131 12.3951 6.32159L14.2437 7.39306C14.3979 7.48513 14.5261 7.61494 14.6162 7.77025C14.7063 7.92557 14.7554 8.10127 14.7589 8.28081V10.4543C14.7619 10.6352 14.7155 10.8134 14.6247 10.9698C14.5339 11.1263 14.4022 11.255 14.2437 11.3421L12.3951 12.4442C12.2384 12.5345 12.0607 12.582 11.8799 12.582C11.699 12.582 11.5214 12.5345 11.3647 12.4442L9.51606 11.3727C9.36187 11.2807 9.23366 11.1508 9.14353 10.9955C9.05339 10.8402 9.00428 10.6645 9.00085 10.4849V9.07675L7.39467 10.0257V11.4339C7.39167 11.6148 7.43805 11.793 7.52883 11.9494C7.61961 12.1059 7.75134 12.2346 7.90984 12.3217L11.395 14.3422C11.5517 14.4325 11.7293 14.48 11.9102 14.48C12.091 14.48 12.2687 14.4325 12.4253 14.3422L15.9105 12.3217C16.0647 12.2296 16.1929 12.0998 16.283 11.9445C16.3731 11.7892 16.4222 11.6135 16.4257 11.4339V7.36243C16.4287 7.18159 16.3823 7.00335 16.2915 6.84692C16.2007 6.69049 16.069 6.56179 15.9105 6.47467L12.3951 4.42362Z" fill="currentColor"/>
		</SvgIcon_1.default>);
};
exports.PolygonIcon = PolygonIcon;
exports.default = exports.PolygonIcon;
