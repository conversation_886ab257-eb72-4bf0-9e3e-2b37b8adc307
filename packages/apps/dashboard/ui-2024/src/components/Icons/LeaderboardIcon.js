"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.LeaderboardIcon = void 0;
const SvgIcon_1 = __importDefault(require("@mui/material/SvgIcon"));
const LeaderboardIcon = (props) => {
    return (<SvgIcon_1.default {...props} style={{
            width: '130',
            height: '130',
        }}>
			<svg xmlns="http://www.w3.org/2000/svg" width="130" height="130" viewBox="0 0 130 130" fill="none">
				<g filter="url(#filter0_d_1100_2733)">
					<path d="M98 41C98 59.2254 83.2254 74 65 74C46.7746 74 32 59.2254 32 41C32 22.7746 46.7746 8 65 8C83.2254 8 98 22.7746 98 41Z" fill="url(#paint0_radial_1100_2733)"/>
				</g>
				<g filter="url(#filter1_d_1100_2733)">
					<path fillRule="evenodd" clipRule="evenodd" d="M65 69.9304C80.9778 69.9304 93.9304 56.9778 93.9304 41C93.9304 25.0222 80.9778 12.0696 65 12.0696C49.0222 12.0696 36.0696 25.0222 36.0696 41C36.0696 56.9778 49.0222 69.9304 65 69.9304ZM65 74C83.2254 74 98 59.2254 98 41C98 22.7746 83.2254 8 65 8C46.7746 8 32 22.7746 32 41C32 59.2254 46.7746 74 65 74Z" fill="url(#paint1_linear_1100_2733)"/>
				</g>
				<path fillRule="evenodd" clipRule="evenodd" d="M79.0239 27.5869H58.7766V30.8353H56.9169C54.8856 30.8353 52.9534 32.5483 53.4819 34.7925C54.2633 38.1109 56.2482 40.9549 58.9438 42.8216L59.0827 42.621C59.9593 46.101 62.6377 48.8648 66.068 49.8624C66.0232 50.8597 65.8554 51.839 65.5722 52.7591C65.2376 53.8461 64.7506 54.8225 64.1461 55.6361C63.5416 56.4495 62.8357 57.079 62.0766 57.5021C61.8152 57.6479 61.5488 57.7685 61.2791 57.8639C61.0636 57.8999 60.8559 57.9588 60.6586 58.0381C60.3478 58.103 60.0345 58.1355 59.7214 58.1355V58.6373C59.1398 59.1845 58.7766 59.9612 58.7766 60.8228V61.6023H78.1465V60.8228C78.1465 60.2275 77.9731 59.6727 77.674 59.2061V58.1355C76.8758 58.1355 76.0763 57.9244 75.3188 57.5021C74.5597 57.079 73.8539 56.4495 73.2494 55.6361C72.6449 54.8225 72.1578 53.8461 71.8232 52.7591C71.5502 51.8721 71.3845 50.93 71.3328 49.9698C74.9535 49.0767 77.8069 46.2355 78.7175 42.6218L78.8559 42.8216C81.5514 40.9549 83.5364 38.1109 84.3178 34.7925C84.8463 32.5483 82.9141 30.8353 80.8828 30.8353H79.0239V27.5869ZM79.0239 32.4353V40.1403C79.0239 40.3194 79.0192 40.4975 79.01 40.6744C80.8405 39.0683 82.1786 36.8965 82.7604 34.4257C82.9915 33.4445 82.1651 32.4353 80.8828 32.4353H79.0239ZM56.9169 32.4353H58.7766V40.1403C58.7766 40.3197 58.7812 40.498 58.7904 40.6751C56.9595 39.0689 55.6212 36.8969 55.0393 34.4257C54.8082 33.4445 55.6345 32.4353 56.9169 32.4353Z" fill="url(#paint2_linear_1100_2733)"/>
				<g clipPath="url(#clip0_1100_2733)">
					<path d="M62.4134 45.2676C62.4134 46.5239 62.2296 47.768 61.8723 48.9287C61.5151 50.0894 60.9914 51.144 60.3313 52.0324C59.6712 52.9207 58.8876 53.6254 58.0251 54.1062C57.1626 54.587 56.2382 54.8344 55.3047 54.8344" stroke="#320A8D" strokeWidth="1.2"/>
					<path d="M66.9254 45.2676C66.9254 46.5239 67.1093 47.768 67.4666 48.9287C67.8238 50.0894 68.3474 51.144 69.0075 52.0324C69.6676 52.9207 70.4513 53.6254 71.3138 54.1062C72.1763 54.587 73.1006 54.8344 74.0342 54.8344" stroke="#320A8D" strokeWidth="1.2"/>
				</g>
				<path d="M74.6746 57.5515V56.772C74.6746 55.1151 73.3315 53.772 71.6746 53.772H58.3047C56.6478 53.772 55.3047 55.1151 55.3047 56.772V57.5515H74.6746Z" stroke="#320A8D" strokeWidth="1.2"/>
				<path d="M55.1283 24.1361H74.1756V36.0895C74.1756 41.3492 69.9118 45.6131 64.652 45.6131C59.3922 45.6131 55.1283 41.3492 55.1283 36.0895V24.1361Z" stroke="#320A8D" strokeWidth="1.2"/>
				<path d="M55.2679 27.5864H52.8672C51.2103 27.5864 49.8314 28.9471 50.2059 30.561C50.9306 33.6839 52.7708 36.3597 55.2679 38.115" stroke="#320A8D" strokeWidth="1.2"/>
				<path d="M73.986 27.5864H76.3867C78.0436 27.5864 79.4225 28.9471 79.048 30.561C78.3234 33.6839 76.4831 36.3597 73.986 38.115" stroke="#320A8D" strokeWidth="1.2"/>
				<path d="M64.1608 35.7228C64.2798 35.6479 64.4285 35.6479 64.5475 35.7228L65.6818 36.4371C65.9666 36.6164 66.3168 36.3482 66.2409 36.0089L65.9414 34.6697C65.9094 34.5266 65.9559 34.3768 66.062 34.2809L67.0577 33.381C67.31 33.153 67.1759 32.7187 66.8441 32.6893L65.5297 32.573C65.3911 32.5607 65.2702 32.469 65.2157 32.3347L64.6998 31.0649C64.57 30.7453 64.1383 30.7453 64.0085 31.0649L63.4926 32.3347C63.4381 32.469 63.3172 32.5607 63.1786 32.573L61.8642 32.6893C61.5324 32.7187 61.3983 33.153 61.6506 33.381L62.6463 34.2809C62.7524 34.3768 62.7989 34.5266 62.7669 34.6697L62.4674 36.0089C62.3915 36.3482 62.7417 36.6164 63.0265 36.4371L64.1608 35.7228Z" stroke="#320A8D" strokeWidth="1.2"/>
				<defs>
					<filter id="filter0_d_1100_2733" x="0" y="0" width="130" height="130" filterUnits="userSpaceOnUse" colorInterpolationFilters="sRGB">
						<feFlood floodOpacity="0" result="BackgroundImageFix"/>
						<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
						<feOffset dy="24"/>
						<feGaussianBlur stdDeviation="16"/>
						<feComposite in2="hardAlpha" operator="out"/>
						<feColorMatrix type="matrix" values="0 0 0 0 0.0486111 0 0 0 0 0.127083 0 0 0 0 0.833333 0 0 0 0.06 0"/>
						<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1100_2733"/>
						<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1100_2733" result="shape"/>
					</filter>
					<filter id="filter1_d_1100_2733" x="0" y="0" width="130" height="130" filterUnits="userSpaceOnUse" colorInterpolationFilters="sRGB">
						<feFlood floodOpacity="0" result="BackgroundImageFix"/>
						<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
						<feOffset dy="24"/>
						<feGaussianBlur stdDeviation="16"/>
						<feComposite in2="hardAlpha" operator="out"/>
						<feColorMatrix type="matrix" values="0 0 0 0 0.0486111 0 0 0 0 0.127083 0 0 0 0 0.833333 0 0 0 0.06 0"/>
						<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1100_2733"/>
						<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1100_2733" result="shape"/>
					</filter>
					<radialGradient id="paint0_radial_1100_2733" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(65 18.6895) rotate(90) scale(55.3105)">
						<stop stopColor="#F0F0FF"/>
						<stop stopColor="#F1F1FD"/>
						<stop offset="0.703125" stopColor="white"/>
					</radialGradient>
					<linearGradient id="paint1_linear_1100_2733" x1="104.526" y1="74" x2="109.495" y2="52.1101" gradientUnits="userSpaceOnUse">
						<stop stopColor="#F7F8FD"/>
						<stop offset="1" stopColor="white"/>
					</linearGradient>
					<linearGradient id="paint2_linear_1100_2733" x1="58.8854" y1="27.5869" x2="94.4489" y2="49.193" gradientUnits="userSpaceOnUse">
						<stop stopColor="#244CB3" stopOpacity="0.2"/>
						<stop offset="1" stopColor="#B4C2E5" stopOpacity="0.07"/>
					</linearGradient>
					<clipPath id="clip0_1100_2733">
						<rect width="15.118" height="8.50388" fill="white" transform="translate(56.8584 45.2676)"/>
					</clipPath>
				</defs>
			</svg>
		</SvgIcon_1.default>);
};
exports.LeaderboardIcon = LeaderboardIcon;
