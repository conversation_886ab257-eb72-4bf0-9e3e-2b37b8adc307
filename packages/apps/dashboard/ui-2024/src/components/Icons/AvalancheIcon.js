"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AvalancheIcon = void 0;
const SvgIcon_1 = __importDefault(require("@mui/material/SvgIcon"));
const AvalancheIcon = (props) => {
    return (<SvgIcon_1.default {...props} style={{
            width: '24',
            height: '24',
        }}>
			<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
				<path d="M19.4012 4.11694H4.58282V17.585H19.4012V4.11694Z" fill="white"/>
				<path fillRule="evenodd" clipRule="evenodd" d="M22 12C22 17.5229 17.5229 22 12 22C6.47715 22 2 17.5229 2 12C2 6.47715 6.47715 2 12 2C17.5229 2 22 6.47715 22 12ZM9.16628 15.9795H7.22557C6.81776 15.9795 6.61632 15.9795 6.4935 15.9009C6.36084 15.8149 6.27977 15.6724 6.26995 15.5152C6.26257 15.3703 6.3633 15.1934 6.56474 14.8396L11.3566 6.39328C11.5605 6.03461 11.6637 5.85527 11.7939 5.78895C11.9339 5.71771 12.101 5.71771 12.241 5.78895C12.3712 5.85527 12.4744 6.03461 12.6783 6.39328L13.6634 8.1129L13.6684 8.12168C13.8886 8.50646 14.0003 8.70158 14.0491 8.90638C14.1031 9.12993 14.1031 9.36577 14.0491 9.58932C13.9999 9.79567 13.8894 9.99221 13.6658 10.3828L11.1488 14.8323L11.1423 14.8436C10.9206 15.2316 10.8082 15.4282 10.6525 15.5766C10.483 15.7387 10.2791 15.8566 10.0556 15.923C9.85168 15.9795 9.62322 15.9795 9.16628 15.9795ZM14.0672 15.9795H16.8481C17.2583 15.9795 17.4647 15.9795 17.5876 15.8985C17.7202 15.8125 17.8037 15.6675 17.8112 15.5104C17.8182 15.3702 17.7197 15.2001 17.5266 14.8669C17.52 14.8556 17.5133 14.844 17.5065 14.8323L16.1136 12.4494L16.0977 12.4226C15.902 12.0916 15.8032 11.9244 15.6763 11.8598C15.5364 11.7886 15.3716 11.7886 15.2317 11.8598C15.1039 11.9261 15.0008 12.1006 14.7969 12.4519L13.4089 14.8348L13.4041 14.843C13.2009 15.1937 13.0994 15.369 13.1067 15.5128C13.1165 15.67 13.1976 15.8149 13.3303 15.9009C13.4506 15.9795 13.657 15.9795 14.0672 15.9795Z" fill="#320A8D"/>
			</svg>
		</SvgIcon_1.default>);
};
exports.AvalancheIcon = AvalancheIcon;
