"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.BinanceSmartChainIcon = void 0;
const SvgIcon_1 = __importDefault(require("@mui/material/SvgIcon"));
const BinanceSmartChainIcon = (props) => {
    return (<SvgIcon_1.default width="21" height="23" viewBox="0 0 21 23" fill="none" {...props}>
      <path d="M7.77098 15.0238C7.77098 14.3376 7.76744 13.6515 7.77422 12.9653C7.7754 12.8407 7.73269 12.7787 7.62578 12.7179C6.41945 12.0326 5.21694 11.34 4.01119 10.6536C3.91989 10.6015 3.88072 10.5495 3.88161 10.4407C3.88691 9.66209 3.88632 8.88323 3.8822 8.10466C3.88161 8.00522 3.91076 7.94935 4.0003 7.89905C4.67268 7.52 5.34299 7.13685 6.01036 6.74932C6.1105 6.69112 6.18413 6.68937 6.28633 6.74845C7.49207 7.44454 8.70194 8.13391 9.90739 8.83058C10.0078 8.88879 10.0764 8.87884 10.1701 8.82503C11.3823 8.1301 12.5975 7.44015 13.8091 6.74464C13.9063 6.68878 13.9764 6.68849 14.073 6.74464C14.7489 7.13685 15.4281 7.52409 16.1078 7.91016C16.1794 7.95082 16.2094 7.99527 16.2091 8.08097C16.2053 8.87416 16.2053 9.66736 16.2091 10.4608C16.2097 10.5524 16.1759 10.5986 16.0993 10.6419C14.8756 11.3362 13.6542 12.0347 12.4296 12.7275C12.3316 12.7831 12.2968 12.8416 12.2971 12.9545C12.3015 14.3414 12.2986 15.7283 12.3027 17.1156C12.303 17.2226 12.2694 17.279 12.1766 17.3311C11.4957 17.7148 10.8172 18.103 10.1404 18.4943C10.0611 18.5402 10.0052 18.537 9.92742 18.4922C9.24621 18.0989 8.56352 17.7081 7.87848 17.3218C7.79307 17.2735 7.76803 17.2206 7.76862 17.1267C7.77275 16.4259 7.77068 15.7251 7.77068 15.0244L7.77098 15.0238Z" fill="currentColor"/>
      <path d="M20.0942 10.2034V10.3625C20.0942 12.6108 20.0933 14.8591 20.0971 17.1073C20.0971 17.2208 20.0615 17.2778 19.964 17.3334C17.9961 18.4583 16.0302 19.5872 14.064 20.715C14.0227 20.7387 13.9803 20.7598 13.9362 20.7834C13.9052 20.7296 13.9188 20.6811 13.9188 20.6349C13.9176 19.8709 13.9206 19.1067 13.9155 18.3427C13.9147 18.2278 13.9468 18.1623 14.051 18.1029C15.2577 17.4176 16.4599 16.725 17.6665 16.0404C17.7769 15.9778 17.8076 15.9099 17.8073 15.7891C17.8029 14.4072 17.8043 13.0249 17.8046 11.643C17.8046 11.5722 17.7937 11.5076 17.88 11.4584C18.5983 11.0513 19.3128 10.6377 20.0288 10.2265C20.0409 10.2195 20.0553 10.2169 20.0939 10.2031L20.0942 10.2034Z" fill="currentColor"/>
      <path d="M3.88281 3.51719C5.17956 2.77137 6.45835 2.0355 7.73743 1.29992C8.47019 0.878466 9.20353 0.458178 9.93481 0.0337952C10.0079 -0.00861381 10.0612 -0.0115386 10.1351 0.0308705C12.1301 1.17825 14.1267 2.3233 16.1226 3.46893C16.1435 3.48092 16.1629 3.49584 16.1821 3.5087C16.1727 3.55901 16.1285 3.56603 16.0964 3.58446C15.414 3.97579 14.7295 4.36391 14.0495 4.75904C13.9632 4.80905 13.9052 4.79999 13.8254 4.75407C12.604 4.05505 11.3803 3.35983 10.1598 2.65965C10.0665 2.60612 10.0008 2.60876 9.90919 2.66169C8.70285 3.3572 7.49328 4.04745 6.28694 4.74325C6.18651 4.80116 6.1123 4.80233 6.01098 4.74325C5.34803 4.35747 4.68066 3.97901 4.01476 3.59791C3.97735 3.57656 3.94083 3.55316 3.88281 3.51777V3.51719Z" fill="currentColor"/>
      <path d="M0.00249332 10.2135C0.268441 10.3647 0.504937 10.4989 0.741433 10.6341C1.20382 10.8985 1.66415 11.1661 2.12948 11.4249C2.23139 11.4817 2.27056 11.5425 2.27026 11.6612C2.26555 13.0385 2.26908 14.4161 2.26408 15.7933C2.26349 15.9165 2.30001 15.9826 2.40721 16.0437C3.62121 16.736 4.83137 17.4344 6.04477 18.1273C6.13696 18.1799 6.17407 18.2358 6.17348 18.3431C6.16877 19.117 6.16906 19.8909 6.17289 20.6648C6.17348 20.7868 6.15168 20.8093 6.03947 20.7447C4.05709 19.6049 2.07382 18.4672 0.0887864 17.3321C0.000726227 17.2817 0.00219881 17.2165 0.00219881 17.1378C0.00278784 15.4099 0.00249332 13.6822 0.00249332 11.9543V10.2129V10.2135Z" fill="currentColor"/>
      <path d="M12.3007 19.1183V19.8038C12.3007 20.4069 12.2992 21.01 12.3024 21.6131C12.3027 21.6953 12.2803 21.7429 12.2052 21.7854C11.5128 22.1776 10.8222 22.573 10.133 22.971C10.0585 23.014 10.0034 23.0073 9.93216 22.9661C9.24712 22.5709 8.56001 22.179 7.8732 21.7865C7.81783 21.7549 7.76924 21.7301 7.76953 21.6479C7.77277 20.8117 7.77159 19.9755 7.77159 19.137C7.8358 19.1323 7.87526 19.1735 7.91974 19.199C8.58593 19.5783 9.25212 19.9574 9.91567 20.3414C10.0011 20.3908 10.0626 20.3967 10.1519 20.3446C10.8189 19.9574 11.4899 19.5766 12.1599 19.194C12.2008 19.1706 12.2426 19.149 12.3009 19.1177L12.3007 19.1183Z" fill="currentColor"/>
      <path d="M15.5452 5.72699C15.83 5.5635 16.1001 5.40761 16.3707 5.25289C16.8161 4.99814 17.2628 4.74544 17.7064 4.48806C17.7765 4.44741 17.828 4.43805 17.9034 4.48251C18.5902 4.88554 19.2794 5.28506 19.9703 5.68166C20.0439 5.72377 20.071 5.76911 20.0707 5.85305C20.0678 6.65122 20.0681 7.44938 20.0704 8.24784C20.0704 8.32447 20.0501 8.36951 19.9794 8.40958C19.2906 8.79975 18.6035 9.19225 17.9184 9.58885C17.8095 9.65202 17.8027 9.61312 17.803 9.51514C17.8053 8.74125 17.8021 7.96736 17.8068 7.19347C17.8074 7.0832 17.7773 7.02208 17.6772 6.96534C17.0146 6.58833 16.3566 6.20344 15.6972 5.82088C15.6512 5.79426 15.6068 5.7656 15.5449 5.72728L15.5452 5.72699Z" fill="currentColor"/>
      <path d="M2.24716 9.64766C1.93468 9.46925 1.64782 9.30547 1.36096 9.14139C0.940986 8.90156 0.522185 8.65997 0.100733 8.42277C0.0291656 8.3827 -0.000580463 8.34088 8.56764e-06 8.2546C0.0044263 7.45175 0.00383727 6.64891 0.000597599 5.84606C0.000303083 5.75861 0.0226863 5.70596 0.1025 5.66092C0.795495 5.269 1.48643 4.87299 2.17736 4.47727C2.22507 4.45007 2.26277 4.42696 2.32256 4.46235C3.04176 4.88586 3.76303 5.30644 4.4846 5.72848C4.46575 5.79605 4.40655 5.8057 4.36326 5.83026C3.70266 6.21019 3.04235 6.59041 2.37852 6.96478C2.2825 7.01889 2.24274 7.07475 2.24363 7.18969C2.25011 7.94399 2.24687 8.69829 2.24687 9.45229V9.64825L2.24716 9.64766Z" fill="currentColor"/>
      <path d="M16.1802 12.4341C16.1831 12.4665 16.1869 12.4894 16.1869 12.5122C16.1872 13.325 16.1869 14.1375 16.1881 14.9503C16.1881 15.0126 16.1793 15.0567 16.1163 15.0927C15.3979 15.4998 14.682 15.9102 13.9648 16.3193C13.9572 16.3237 13.9466 16.3234 13.9348 16.3258C13.9027 16.3003 13.9189 16.2638 13.9189 16.2333C13.9177 15.4352 13.9183 14.6373 13.9174 13.8391C13.9174 13.7701 13.9162 13.7125 13.9934 13.6689C14.7035 13.2673 15.4112 12.8614 16.1198 12.4569C16.1354 12.4478 16.154 12.4437 16.1802 12.4341Z" fill="currentColor"/>
      <path d="M7.76697 5.74691C7.86828 5.68753 7.94279 5.64337 8.0176 5.60008C8.65552 5.2301 9.29374 4.86012 9.93136 4.48955C9.99086 4.45504 10.0386 4.42696 10.1154 4.47229C10.8187 4.88556 11.5253 5.29327 12.2306 5.70274C12.2468 5.7121 12.2604 5.72526 12.2745 5.73579C12.2683 5.78288 12.2298 5.78902 12.2027 5.80452C11.5162 6.1982 10.8293 6.59099 10.1422 6.98408C10.0877 7.01508 10.0415 7.04959 9.96877 7.00777C9.24544 6.5907 8.51975 6.17743 7.76697 5.74691Z" fill="currentColor"/>
      <path d="M6.13966 16.3369C5.77328 16.1283 5.41338 15.9239 5.05407 15.7189C4.70183 15.5179 4.35136 15.3138 3.99735 15.1161C3.91607 15.0707 3.88072 15.0228 3.88161 14.9245C3.88661 14.1459 3.88426 13.3671 3.88455 12.5885C3.88455 12.4367 3.88632 12.4358 4.02121 12.5127C4.70007 12.9003 5.37863 13.2887 6.05808 13.6753C6.1161 13.7084 6.15615 13.7382 6.15586 13.8172C6.15232 14.6396 6.1535 15.4621 6.15291 16.2845C6.15291 16.2971 6.14673 16.31 6.13995 16.3366L6.13966 16.3369Z" fill="currentColor"/>
    </SvgIcon_1.default>);
};
exports.BinanceSmartChainIcon = BinanceSmartChainIcon;
exports.default = exports.BinanceSmartChainIcon;
