"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const Link_1 = __importDefault(require("@mui/material/Link"));
const NothingFound = () => {
    return (<>
			<div className="nothing-found-title">Nothing found :(</div>
			<div className="nothing-found-desc">
				We couldn't find anything within this criteria.
				<br />
				Please search by <b>wallet address or escrow address.</b>
			</div>
			<Link_1.default href="/" underline="none">
				<div className="nothing-found-link">Back Home</div>
			</Link_1.default>
		</>);
};
exports.default = NothingFound;
