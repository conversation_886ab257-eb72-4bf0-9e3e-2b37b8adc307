"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const clsx_1 = __importDefault(require("clsx"));
const Header_1 = __importDefault(require("@components/Header"));
const Footer_1 = __importDefault(require("@components/Footer"));
const PageWrapper = ({ children, violetHeader, displaySearchBar, className }) => {
    return (<div className="page-wrapper">
			<Header_1.default displaySearchBar={displaySearchBar}/>
			<div className={(0, clsx_1.default)(className, {
            'violet-header': violetHeader,
            'search-white-header': displaySearchBar,
        })}>
				<div className="container">{children}</div>
			</div>
			<Footer_1.default />
		</div>);
};
exports.default = PageWrapper;
