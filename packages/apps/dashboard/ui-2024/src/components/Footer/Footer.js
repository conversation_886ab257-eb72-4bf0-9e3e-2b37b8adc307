"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const Typography_1 = __importDefault(require("@mui/material/Typography"));
const Twitter_1 = __importDefault(require("@mui/icons-material/Twitter"));
const LinkedIn_1 = __importDefault(require("@mui/icons-material/LinkedIn"));
const GitHub_1 = __importDefault(require("@mui/icons-material/GitHub"));
const Telegram_1 = __importDefault(require("@mui/icons-material/Telegram"));
const DiscordIcon_1 = __importDefault(require("@components/Icons/DiscordIcon"));
const color_palette_1 = require("@assets/styles/color-palette");
const env_1 = require("@helpers/env");
const Footer = () => {
    const handleClick = (url) => {
        window.open(url, '_blank');
    };
    return (<footer>
			<div className="footer-wrapper">
				<div className="footer-link-wrapper">
					<div className="footer-link">
						<Typography_1.default component="span" color="text.secondary" onClick={() => handleClick(env_1.env.VITE_FOOTER_LINK_PRIVACY_POLICY)}>
							Privacy Policy
						</Typography_1.default>
						<Typography_1.default component="span" color="text.secondary" onClick={() => handleClick(env_1.env.VITE_FOOTER_LINK_TERMS_OF_SERVICE)}>
							Terms of Service
						</Typography_1.default>
						<Typography_1.default component="span" color="text.secondary" onClick={() => handleClick(env_1.env.VITE_FOOTER_LINK_HUMAN_PROTOCOL)}>
							HUMAN Protocol
						</Typography_1.default>
					</div>
					<Typography_1.default variant="subtitle1" color="text.secondary">
						© 2021 HPF. HUMAN Protocol® is a registered trademark
					</Typography_1.default>
				</div>
				<div className="footer-icon">
					<GitHub_1.default style={{
            color: color_palette_1.colorPalette.sky.main,
        }} onClick={() => handleClick(env_1.env.VITE_FOOTER_LINK_GITHUB)}/>
					<DiscordIcon_1.default style={{
            color: color_palette_1.colorPalette.sky.main,
        }} onClick={() => handleClick(env_1.env.VITE_FOOTER_LINK_DISCORD)}/>
					<Twitter_1.default style={{
            color: color_palette_1.colorPalette.sky.main,
        }} onClick={() => handleClick(env_1.env.VITE_FOOTER_LINK_X)}/>
					<Telegram_1.default style={{
            color: color_palette_1.colorPalette.sky.main,
        }} onClick={() => handleClick(env_1.env.VITE_FOOTER_LINK_TELEGRAM)}/>
					<LinkedIn_1.default style={{
            color: color_palette_1.colorPalette.sky.main,
        }} onClick={() => handleClick(env_1.env.VITE_FOOTER_LINK_LINKEDIN)}/>
				</div>
			</div>
		</footer>);
};
exports.default = Footer;
