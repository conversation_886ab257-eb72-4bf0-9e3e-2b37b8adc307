"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const clsx_1 = __importDefault(require("clsx"));
const ShadowIcon = ({ className, title, img }) => {
    return (<div className={(0, clsx_1.default)('shadow-icon', className)}>
			<div className="shadow-icon__icon">
				{typeof img === 'string' ? <img src={img} alt="logo"/> : <>{img}</>}
			</div>
			<span>{title}</span>
		</div>);
};
exports.default = ShadowIcon;
