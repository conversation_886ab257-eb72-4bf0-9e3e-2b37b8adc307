"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.default = void 0;
var Breadcrumbs_1 = require("./Breadcrumbs");
Object.defineProperty(exports, "default", { enumerable: true, get: function () { return __importDefault(Breadcrumbs_1).default; } });
