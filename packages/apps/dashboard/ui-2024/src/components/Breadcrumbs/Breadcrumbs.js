"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const Typography_1 = __importDefault(require("@mui/material/Typography"));
const KeyboardArrowRight_1 = __importDefault(require("@mui/icons-material/KeyboardArrowRight"));
const react_router_dom_1 = require("react-router-dom");
const Breadcrumbs = ({ title }) => {
    const navigate = (0, react_router_dom_1.useNavigate)();
    return (<div className="breadcrumbs">
			<Typography_1.default onClick={() => {
            navigate('/');
        }} component="span" fontSize={16} sx={{ textDecoration: 'unset', cursor: 'pointer' }} color="text.secondary">
				Dashboard
			</Typography_1.default>
			<KeyboardArrowRight_1.default color="primary"/>
			<Typography_1.default component="span" fontSize={16} color="primary">
				{title}
			</Typography_1.default>
		</div>);
};
exports.default = Breadcrumbs;
