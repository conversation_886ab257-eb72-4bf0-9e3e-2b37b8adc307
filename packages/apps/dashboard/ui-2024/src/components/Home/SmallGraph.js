"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const recharts_1 = require("recharts");
const Card_1 = __importDefault(require("@mui/material/Card"));
const color_palette_1 = require("@assets/styles/color-palette");
const Box_1 = __importDefault(require("@mui/material/Box"));
const material_1 = require("@mui/material");
const Stack_1 = __importDefault(require("@mui/material/Stack"));
const ToggleButtons_1 = __importDefault(require("@components/DataEntry/ToggleButtons"));
const react_1 = require("react");
const formatDate_1 = require("@helpers/formatDate");
const formatNumber_1 = require("@helpers/formatNumber");
const CustomSmallChartTooltip = ({ payload, active, }) => {
    if (active) {
        return (<Card_1.default sx={{
                border: `2px solid ${color_palette_1.colorPalette.fog.light}`,
            }}>
				<Box_1.default sx={{
                paddingX: 2,
                paddingY: 1,
            }}>
					{payload?.map((elem) => (<react_1.Fragment key={elem.name}>
							<material_1.Typography fontWeight={500} variant="caption">
								{(0, formatDate_1.formatDate)(elem.payload.date, 'MMMM DD, YYYY')}
							</material_1.Typography>
							<material_1.Typography fontWeight={500} variant="h6" component="p">
								{elem.value ? elem.value.toLocaleString('en-US') : ''}
							</material_1.Typography>
						</react_1.Fragment>))}
				</Box_1.default>
			</Card_1.default>);
    }
    return null;
};
const SmallGraph = ({ title, graphData }) => {
    return (<>
			<recharts_1.ResponsiveContainer height={150}>
				<recharts_1.AreaChart data={graphData} margin={{
            top: 5,
            right: 50,
            left: 20,
        }}>
					<defs>
						<linearGradient id="value" x1="0" y1="0" x2="0" y2="1">
							<stop offset={'90%'} stopColor="#244CB20F" stopOpacity={0.9}/>
							<stop offset={'100%'} stopColor="#B4C2E505" stopOpacity={0}/>
						</linearGradient>
					</defs>
					<recharts_1.XAxis style={{
            fontSize: 10,
            fontWeight: 500,
        }} axisLine={false} interval="preserveStartEnd" dataKey="date" stroke={color_palette_1.colorPalette.fog.main} tickFormatter={(value) => (0, formatDate_1.formatDate)(value, 'DD MMMM')} tick={{ dy: 10 }} tickSize={0}/>
					<recharts_1.YAxis style={{
            fontSize: 10,
            fontWeight: 500,
        }} axisLine={false} dataKey="value" tick={{ dx: -10 }} tickSize={0} stroke={color_palette_1.colorPalette.fog.main} tickFormatter={formatNumber_1.formatNumber}/>
					<recharts_1.CartesianGrid stroke="#ccc" strokeDasharray="7" vertical={false}/>
					<recharts_1.Tooltip content={<CustomSmallChartTooltip />}/>
					<recharts_1.Area type="monotone" dataKey="value" stroke={color_palette_1.colorPalette.primary.main} fill="url(#value)"/>
				</recharts_1.AreaChart>
			</recharts_1.ResponsiveContainer>
			<Stack_1.default sx={{
            marginTop: 2,
        }} direction={{ xs: 'column', xl: 'row' }} justifyContent="center" alignItems="center" gap={2}>
				<material_1.Typography fontWeight={400} variant="body1" component="p">
					{title}
				</material_1.Typography>
				<ToggleButtons_1.default />
			</Stack_1.default>
		</>);
};
exports.default = SmallGraph;
