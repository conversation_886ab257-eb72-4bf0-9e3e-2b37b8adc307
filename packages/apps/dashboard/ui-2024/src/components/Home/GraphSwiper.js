"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const modules_1 = require("swiper/modules");
const react_1 = require("swiper/react");
const SmallGraph_1 = __importDefault(require("@components/Home/SmallGraph"));
require("swiper/css");
require("swiper/css/navigation");
const use_graph_page_chart_data_1 = require("@services/api/use-graph-page-chart-data");
const GraphSwiper = () => {
    const { data } = (0, use_graph_page_chart_data_1.useGraphPageChartData)();
    const transactionHistoryData = (data || []).map(({ totalTransactionCount, date }) => ({
        value: totalTransactionCount,
        date,
    }));
    const transferAmount = (data || []).map(({ totalTransactionAmount, date }) => ({
        value: totalTransactionAmount,
        date,
    }));
    const solvedTasks = (data || []).map(({ solved, date }) => ({
        value: solved,
        date,
    }));
    const uniqueSenders = (data || []).map(({ dailyUniqueSenders, date }) => ({
        value: dailyUniqueSenders,
        date,
    }));
    const uniqueReceivers = (data || []).map(({ dailyUniqueReceivers, date }) => ({
        value: dailyUniqueReceivers,
        date,
    }));
    return (<react_1.Swiper loop={true} navigation={true} modules={[modules_1.Navigation]} className="mySwiper">
			<react_1.SwiperSlide>
				<SmallGraph_1.default graphData={transactionHistoryData} title="Transaction history"/>
			</react_1.SwiperSlide>
			<react_1.SwiperSlide>
				<SmallGraph_1.default graphData={transferAmount} title="Transfer Amount"/>
			</react_1.SwiperSlide>
			<react_1.SwiperSlide>
				<SmallGraph_1.default graphData={solvedTasks} title="Number of Tasks"/>
			</react_1.SwiperSlide>
			<react_1.SwiperSlide>
				<SmallGraph_1.default graphData={uniqueSenders} title="Unique Senders"/>
			</react_1.SwiperSlide>
			<react_1.SwiperSlide>
				<SmallGraph_1.default graphData={uniqueReceivers} title="Unique Receivers"/>
			</react_1.SwiperSlide>
		</react_1.Swiper>);
};
exports.default = GraphSwiper;
