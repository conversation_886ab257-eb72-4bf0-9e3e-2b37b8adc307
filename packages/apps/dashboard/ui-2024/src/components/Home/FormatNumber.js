"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.FormatNumberWithDecimals = exports.FormatNumber = void 0;
const react_number_format_1 = require("react-number-format");
const FormatNumber = ({ value, }) => {
    return (<react_number_format_1.NumericFormat displayType="text" value={value} thousandsGroupStyle="thousand" thousandSeparator=","/>);
};
exports.FormatNumber = FormatNumber;
const FormatNumberWithDecimals = ({ value, }) => {
    if (value && Number(value) < 1) {
        return value;
    }
    return (<react_number_format_1.NumericFormat displayType="text" value={value} thousandsGroupStyle="thousand" thousandSeparator=","/>);
};
exports.FormatNumberWithDecimals = FormatNumberWithDecimals;
