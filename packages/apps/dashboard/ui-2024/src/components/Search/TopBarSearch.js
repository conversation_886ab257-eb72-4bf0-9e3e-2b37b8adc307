"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const react_1 = require("react");
const clsx_1 = __importDefault(require("clsx"));
const react_router_dom_1 = require("react-router-dom");
const IconButton_1 = __importDefault(require("@mui/material/IconButton"));
const Search_1 = __importDefault(require("@mui/icons-material/Search"));
const Close_1 = __importDefault(require("@mui/icons-material/Close"));
const material_1 = require("@mui/material");
const color_palette_1 = require("@assets/styles/color-palette");
const networks_1 = require("@utils/config/networks");
const use_wallet_search_1 = require("@utils/hooks/use-wallet-search");
const use_is_mobile_1 = require("@utils/hooks/use-is-mobile");
const NetworkIcon_1 = require("@components/NetworkIcon");
const TopSearchBar = ({ className, displaySearchBar, }) => {
    const { mobile } = (0, use_is_mobile_1.useBreakPoints)();
    const { filterParams, setAddress, setChainId } = (0, use_wallet_search_1.useWalletSearch)();
    const [inputValue, setInputValue] = (0, react_1.useState)('');
    const [selectValue, setSelectValue] = (0, react_1.useState)('');
    const [focus, setFocus] = (0, react_1.useState)(false);
    const navigate = (0, react_router_dom_1.useNavigate)();
    const handleInputChange = (event) => {
        setAddress(event.target.value);
    };
    const handleSelectChange = (event) => {
        const chainId = Number(event.target.value);
        setChainId(chainId);
        const networkName = (0, networks_1.getNetwork)(chainId)?.name || '';
        setSelectValue(networkName);
    };
    const handleClearClick = () => {
        setInputValue('');
        setAddress('');
    };
    const handleInputBlur = () => {
        setFocus(false);
    };
    const handleInputFocus = () => {
        setFocus(true);
    };
    const handleSubmit = (event) => {
        event.preventDefault();
        navigate(`/search/${filterParams.chainId || -1}/${filterParams.address || '0x0'}`);
    };
    (0, react_1.useEffect)(() => {
        const networkName = (0, networks_1.getNetwork)(filterParams.chainId || -1)?.name || '';
        if (networkName) {
            setSelectValue(networkName);
        }
    }, [filterParams.chainId]);
    (0, react_1.useEffect)(() => {
        setInputValue(filterParams.address);
    }, [filterParams.address]);
    return (<form className={(0, clsx_1.default)('search', className, {
            'search-white': displaySearchBar,
        })} onSubmit={handleSubmit}>
			<material_1.TextField id="search-bar" placeholder="Search by Wallet/Escrow" value={filterParams.address} onChange={handleInputChange} onBlur={handleInputBlur} onFocus={handleInputFocus} fullWidth sx={{
            fontSize: '16px',
            '& .MuiOutlinedInput-root': {
                '& input': {
                    [mobile.mediaQuery]: {
                        padding: '12px 0px',
                    },
                },
                '& fieldset': {
                    border: 'none',
                },
            },
            '& .MuiInputBase-input': {
                overflow: 'hidden',
                textOverflow: 'ellipsis',
            },
        }} InputProps={{
            sx: {
                width: '100%',
                height: '100%',
                borderRadius: '10px',
                border: `1px solid ${color_palette_1.colorPalette.skyOpacity}`,
                backgroundColor: `${color_palette_1.colorPalette.white}`,
                fontSize: 'inherit',
                'input::placeholder': {
                    color: `${color_palette_1.colorPalette.sky.main}`,
                    opacity: 1,
                },
                padding: '0 5px',
            },
            startAdornment: (<material_1.InputAdornment position="start" sx={{
                    height: '100%',
                    backgroundColor: `${color_palette_1.colorPalette.white}`,
                    marginLeft: '1rem',
                }}>
							<material_1.Select value={selectValue} displayEmpty sx={{
                    backgroundColor: `${color_palette_1.colorPalette.white}`,
                    fontSize: '16px',
                    boxShadow: 'none',
                    outline: 'none',
                    '& .MuiOutlinedInput-notchedOutline': { border: 0 },
                    '& .MuiSelect-select': {
                        padding: 0,
                        paddingRight: '24px',
                        backgroundColor: `${color_palette_1.colorPalette.white}`,
                        border: 0,
                        outline: 'none',
                    },
                    '& .MuiInputBase-input': {
                        backgroundColor: `${color_palette_1.colorPalette.white}`,
                    },
                    '& .MuiOutlinedInput-root:hover .MuiOutlinedInput-notchedOutline': {
                        border: 0,
                        outline: 'none',
                    },
                    '& .MuiOutlinedInput-root.Mui-focused .MuiOutlinedInput-notchedOutline': {
                        border: 0,
                        outline: 'none',
                    },
                    [mobile.mediaQuery]: {
                        width: 'unset',
                    },
                }} onChange={handleSelectChange} renderValue={selectValue === ''
                    ? () => (<span style={{ color: color_palette_1.colorPalette.sky.main }}>
													Network
												</span>)
                    : () => {
                        return (<material_1.Grid sx={{
                                display: 'flex',
                                justifyContent: 'flex-start',
                                alignItems: 'center',
                                gap: '8px',
                                textOverflow: 'ellipsis',
                                overflow: 'hidden',
                            }}>
														<NetworkIcon_1.NetworkIcon chainId={filterParams.chainId}/>
														<div>
															{mobile.isMobile
                                ? null
                                : (0, networks_1.getNetwork)(filterParams.chainId)?.name}
														</div>
													</material_1.Grid>);
                    }}>
								{networks_1.networks.map((network) => (<material_1.MenuItem key={network.name} value={network.id}>
										<material_1.Grid sx={{ display: 'flex', gap: '8px' }}>
											<NetworkIcon_1.NetworkIcon chainId={network.id}/> {network.name}
										</material_1.Grid>
									</material_1.MenuItem>))}
							</material_1.Select>
						</material_1.InputAdornment>),
            endAdornment: inputValue && (<material_1.InputAdornment sx={{
                    display: 'flex',
                    justifyContent: 'center',
                    alignItems: 'center',
                    gap: '0.7rem',
                }} position="end">
							<IconButton_1.default onClick={handleClearClick} edge="end">
								<Close_1.default style={{
                    color: focus
                        ? color_palette_1.colorPalette.textSecondary.main
                        : color_palette_1.colorPalette.primary.main,
                }}/>
							</IconButton_1.default>
							<IconButton_1.default className="search-button" type="submit" aria-label="search" sx={{
                    [mobile.mediaQuery]: {
                        padding: '4px',
                    },
                }}>
								<Search_1.default style={{
                    color: displaySearchBar
                        ? color_palette_1.colorPalette.textSecondary.main
                        : color_palette_1.colorPalette.white,
                }}/>
							</IconButton_1.default>
						</material_1.InputAdornment>),
        }}/>
		</form>);
};
exports.default = TopSearchBar;
