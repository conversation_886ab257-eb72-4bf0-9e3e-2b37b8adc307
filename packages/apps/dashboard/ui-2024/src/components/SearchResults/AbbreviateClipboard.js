"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const Stack_1 = __importDefault(require("@mui/material/Stack"));
const Typography_1 = __importDefault(require("@mui/material/Typography"));
const abbreviateValue_1 = __importDefault(require("@helpers/abbreviateValue"));
const IconButton_1 = __importDefault(require("@mui/material/IconButton"));
const ContentCopy_1 = __importDefault(require("@mui/icons-material/ContentCopy"));
const color_palette_1 = require("@assets/styles/color-palette");
const material_1 = require("@mui/material");
const react_router_dom_1 = require("react-router-dom");
const react_1 = require("react");
const AbbreviateClipboard = ({ value, link }) => {
    const [tooltipOpen, setTooltipOpen] = (0, react_1.useState)(false);
    const navigate = (0, react_router_dom_1.useNavigate)();
    return (<Stack_1.default direction="row" gap={1}>
			<Typography_1.default sx={{ whiteSpace: 'nowrap', textDecoration: 'inherit' }}>
				{link ? (<span onClick={(e) => {
                e.stopPropagation();
                e.preventDefault();
                navigate(link, {
                    preventScrollReset: false,
                });
            }}>
						<material_1.Link href={link} target="_blank" underline="none">
							{(0, abbreviateValue_1.default)(value)}
						</material_1.Link>
					</span>) : (<>{(0, abbreviateValue_1.default)(value)}</>)}
			</Typography_1.default>
			<IconButton_1.default onClick={() => {
            navigator.clipboard.writeText(value);
            setTooltipOpen(true);
            setTimeout(() => {
                setTooltipOpen(false);
            }, 1500);
        }} sx={{
            p: 0,
        }}>
				<material_1.Tooltip title="Copied!" arrow open={tooltipOpen}>
					<ContentCopy_1.default fontSize="small" sx={{
            color: color_palette_1.colorPalette.fog.main,
        }}/>
				</material_1.Tooltip>
			</IconButton_1.default>
		</Stack_1.default>);
};
exports.default = AbbreviateClipboard;
