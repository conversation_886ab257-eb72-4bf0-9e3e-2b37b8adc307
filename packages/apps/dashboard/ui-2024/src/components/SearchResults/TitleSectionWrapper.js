"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const Stack_1 = __importDefault(require("@mui/material/Stack"));
const Tooltip_1 = __importDefault(require("@mui/material/Tooltip"));
const IconButton_1 = __importDefault(require("@mui/material/IconButton"));
const HelpOutline_1 = __importDefault(require("@mui/icons-material/HelpOutline"));
const Typography_1 = __importDefault(require("@mui/material/Typography"));
const color_palette_1 = require("@assets/styles/color-palette");
const TitleSectionWrapper = ({ title, children, tooltip, }) => {
    return (<Stack_1.default alignItems={{ xs: 'start', md: 'center' }} gap={{ xs: 1, md: 0 }} direction={{ sm: 'column', md: 'row' }} sx={{ whiteSpace: 'nowrap' }}>
			{tooltip ? (<Stack_1.default sx={{
                width: 300,
            }} direction="row" alignItems="center">
					<Tooltip_1.default title={tooltip.description}>
						<IconButton_1.default sx={{ padding: 0, paddingRight: 1, color: color_palette_1.colorPalette.fog.main }}>
							<HelpOutline_1.default fontSize="small"/>
						</IconButton_1.default>
					</Tooltip_1.default>
					<Typography_1.default variant="subtitle2">{title}</Typography_1.default>
				</Stack_1.default>) : (<Typography_1.default sx={{
                width: 300,
            }} variant="subtitle2">
					{title}
				</Typography_1.default>)}
			{children}
		</Stack_1.default>);
};
exports.default = TitleSectionWrapper;
