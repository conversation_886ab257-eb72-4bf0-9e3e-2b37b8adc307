"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const CircularProgress_1 = __importDefault(require("@mui/material/CircularProgress"));
const Box_1 = __importDefault(require("@mui/material/Box"));
const Loader = ({ height = '100vh' }) => {
    return (<Box_1.default sx={{
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            height,
        }}>
			<CircularProgress_1.default />
		</Box_1.default>);
};
exports.default = Loader;
