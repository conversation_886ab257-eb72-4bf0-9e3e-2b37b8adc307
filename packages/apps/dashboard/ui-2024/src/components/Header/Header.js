"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const react_1 = require("react");
const clsx_1 = __importDefault(require("clsx"));
const Toolbar_1 = __importDefault(require("@mui/material/Toolbar"));
const Button_1 = __importDefault(require("@mui/material/Button"));
const IconButton_1 = __importDefault(require("@mui/material/IconButton"));
const Menu_1 = __importDefault(require("@mui/icons-material/Menu"));
const Drawer_1 = __importDefault(require("@mui/material/Drawer"));
const Box_1 = __importDefault(require("@mui/material/Box"));
const Link_1 = __importDefault(require("@mui/material/Link"));
const Search_1 = __importDefault(require("@components/Search"));
const env_1 = require("@helpers/env");
const react_router_dom_1 = require("react-router-dom");
const LogoBlockIcon_1 = require("@components/Icons/LogoBlockIcon");
const LogoBlockIconMobile_1 = require("@components/Icons/LogoBlockIconMobile");
const TopBarSearch_1 = __importDefault(require("@components/Search/TopBarSearch"));
const Header = ({ displaySearchBar }) => {
    const navigate = (0, react_router_dom_1.useNavigate)();
    const [open, setState] = (0, react_1.useState)(false);
    const handleClick = (url) => {
        window.open(url, '_blank');
    };
    const toggleDrawer = (open) => {
        setState(open);
    };
    return (<Toolbar_1.default className={(0, clsx_1.default)('header-toolbar', {
            'header-toolbar-search': displaySearchBar,
        })}>
			{displaySearchBar && (<Search_1.default displaySearchBar className="search-header-mobile"/>)}
			<Link_1.default onClick={() => {
            navigate('/');
        }} underline="none" sx={{
            ':hover': {
                cursor: 'pointer',
            },
        }}>
				<span className="logo">
					<LogoBlockIcon_1.LogoBlockIcon />
				</span>
				<span className="logo-mobile">
					<LogoBlockIconMobile_1.LogoBlockIconMobile />
				</span>
			</Link_1.default>

			{displaySearchBar && (<TopBarSearch_1.default displaySearchBar className="search-header"/>)}

			<div className="header-list-link">
				<span className="header-link" onClick={() => handleClick(env_1.env.VITE_NAVBAR_LINK_GITBOOK)}>
					GitBook
				</span>
				<span className="header-link" onClick={() => handleClick(env_1.env.VITE_NAVBAR_LINK_FAUCETS)}>
					Faucet
				</span>
				<span className="header-link" onClick={() => handleClick(env_1.env.VITE_NAVBAR_LINK_HUMAN_WEBSITE)}>
					HUMAN Website
				</span>
				<Button_1.default variant="contained" color="primary" onClick={() => handleClick(env_1.env.VITE_NAVBAR_LINK_LAUNCH_JOBS)}>
					Launch Jobs
				</Button_1.default>
				<Button_1.default variant="contained" color="secondary" onClick={() => handleClick(env_1.env.VITE_NAVBAR_LINK_WORK_AND_EARN)}>
					Work & Earn
				</Button_1.default>
			</div>

			<IconButton_1.default edge="start" color="inherit" aria-label="open drawer" className="mobile-icon" onClick={() => toggleDrawer(true)}>
				<Menu_1.default />
			</IconButton_1.default>

			<Drawer_1.default anchor="right" variant="temporary" open={open} onClose={() => toggleDrawer(false)} PaperProps={{
            sx: {
                width: '80%',
            },
        }}>
				<Box_1.default className="header-mobile-menu">
					<div className="header-list-link">
						<div>
							<span className="header-link" onClick={() => handleClick(env_1.env.VITE_NAVBAR_LINK_GITBOOK)}>
								GitBook
							</span>
						</div>
						<div>
							<span className="header-link" onClick={() => handleClick(env_1.env.VITE_NAVBAR_LINK_FAUCETS)}>
								Faucet
							</span>
						</div>
						<div>
							<span className="header-link" onClick={() => handleClick(env_1.env.VITE_NAVBAR_LINK_HUMAN_WEBSITE)}>
								HUMAN Website
							</span>
						</div>
						<Button_1.default variant="contained" color="primary" onClick={() => handleClick(env_1.env.VITE_NAVBAR_LINK_LAUNCH_JOBS)}>
							Launch Jobs
						</Button_1.default>
						<Button_1.default variant="contained" color="secondary" onClick={() => handleClick(env_1.env.VITE_NAVBAR_LINK_WORK_AND_EARN)}>
							Work & Earn
						</Button_1.default>
					</div>
				</Box_1.default>
			</Drawer_1.default>
		</Toolbar_1.default>);
};
exports.default = Header;
