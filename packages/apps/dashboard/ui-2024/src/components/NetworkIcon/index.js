"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.NetworkIcon = void 0;
const EthereumIcon_1 = __importDefault(require("@components/Icons/EthereumIcon"));
const BinanceSmartChainIcon_1 = __importDefault(require("@components/Icons/BinanceSmartChainIcon"));
const PolygonIcon_1 = __importDefault(require("@components/Icons/PolygonIcon"));
const MoonbeamIcon_1 = __importDefault(require("@components/Icons/MoonbeamIcon"));
const MoonbaseAlphaIcon_1 = __importDefault(require("@components/Icons/MoonbaseAlphaIcon"));
const celo_svg_1 = __importDefault(require("@assets/icons/celo.svg"));
const XLayerIcon_1 = require("@components/Icons/XLayerIcon");
const HumanIcon_1 = __importDefault(require("@components/Icons/HumanIcon"));
const AvalancheIcon_1 = require("@components/Icons/AvalancheIcon");
const NetworkIcon = ({ chainId }) => {
    const icon = (() => {
        switch (chainId) {
            case 1:
            case 4:
            case 5:
            case 11155111:
                return <EthereumIcon_1.default />;
            case 56:
            case 97:
                return <BinanceSmartChainIcon_1.default />;
            case 137:
            case 80001:
            case 80002:
                return <PolygonIcon_1.default />;
            case 1284:
                return <MoonbeamIcon_1.default />;
            case 1287:
                return <MoonbaseAlphaIcon_1.default />;
            case 42220:
            case 44787:
                return <celo_svg_1.default />;
            case 195:
            case 196:
                return <XLayerIcon_1.XLayerIcon />;
            case 43113:
            case 43114:
                return <AvalancheIcon_1.AvalancheIcon />;
            default:
                return <HumanIcon_1.default />;
        }
    })();
    return <>{icon}</>;
};
exports.NetworkIcon = NetworkIcon;
