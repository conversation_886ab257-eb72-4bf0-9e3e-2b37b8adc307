"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const Card_1 = __importDefault(require("@mui/material/Card"));
const Stack_1 = __importDefault(require("@mui/material/Stack"));
const Typography_1 = __importDefault(require("@mui/material/Typography"));
const IconButton_1 = __importDefault(require("@mui/material/IconButton"));
const ContentCopy_1 = __importDefault(require("@mui/icons-material/ContentCopy"));
const color_palette_1 = require("@assets/styles/color-palette");
const Clipboard = ({ value }) => {
    return (<Card_1.default sx={{
            paddingX: 3,
            paddingY: 2,
            borderRadius: 16,
        }}>
			<Stack_1.default gap={2} direction="row" alignItems="center" justifyContent="space-between">
				<Typography_1.default fontWeight={600} sx={{
            textOverflow: 'ellipsis',
            overflow: 'hidden',
            wordBreak: 'normal',
        }}>
					{value}
				</Typography_1.default>
				<IconButton_1.default onClick={() => {
            navigator.clipboard.writeText(value);
        }} sx={{
            p: 0,
        }}>
					<ContentCopy_1.default sx={{
            color: color_palette_1.colorPalette.fog.main,
        }}/>
				</IconButton_1.default>
			</Stack_1.default>
		</Card_1.default>);
};
exports.default = Clipboard;
