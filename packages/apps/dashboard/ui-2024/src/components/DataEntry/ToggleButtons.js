"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.StyledToggleButtonGroup = void 0;
const ToggleButton_1 = __importDefault(require("@mui/material/ToggleButton"));
const Typography_1 = __importDefault(require("@mui/material/Typography"));
const material_1 = require("@mui/material");
const ToggleButtonGroup_1 = __importDefault(require("@mui/material/ToggleButtonGroup"));
const color_palette_1 = require("@assets/styles/color-palette");
const use_graph_page_chart_params_1 = require("@utils/hooks/use-graph-page-chart-params");
exports.StyledToggleButtonGroup = (0, material_1.styled)(ToggleButtonGroup_1.default)({
    '.MuiToggleButtonGroup-grouped': {
        border: 'none',
        borderRadius: 4,
        width: 50,
        color: color_palette_1.colorPalette.primary.main,
    },
});
const ToggleButtons = () => {
    const { setTimePeriod, selectedTimePeriod, dateRangeParams, effectiveFromAllTimeDate, } = (0, use_graph_page_chart_params_1.useGraphPageChartParams)();
    const checkIfSelected = (element) => {
        if (element.name !== 'ALL' || !effectiveFromAllTimeDate) {
            return element.value.isSame(dateRangeParams.from);
        }
        return dateRangeParams.from.isSame(effectiveFromAllTimeDate);
    };
    return (<exports.StyledToggleButtonGroup value={selectedTimePeriod} aria-label="text-alignment" exclusive>
			{use_graph_page_chart_params_1.TIME_PERIOD_OPTIONS.map((elem) => (<ToggleButton_1.default onClick={() => {
                setTimePeriod(elem);
            }} selected={checkIfSelected(elem)} key={elem.name} sx={{
                '.MuiTypography-root': {
                    wordBreak: 'normal',
                },
                '&.Mui-selected': {
                    backgroundColor: color_palette_1.colorPalette.primary.main,
                    color: color_palette_1.colorPalette.white,
                },
                '&.Mui-selected:hover': {
                    cursor: 'pointer',
                    backgroundColor: color_palette_1.colorPalette.primary.main,
                },
            }} value={elem.name}>
					<Typography_1.default variant="Components/Button Small">{elem.name}</Typography_1.default>
				</ToggleButton_1.default>))}
		</exports.StyledToggleButtonGroup>);
};
exports.default = ToggleButtons;
