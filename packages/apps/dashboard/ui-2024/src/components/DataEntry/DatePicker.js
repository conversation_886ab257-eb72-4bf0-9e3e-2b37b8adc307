"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const x_date_pickers_1 = require("@mui/x-date-pickers");
const AdapterDayjs_1 = require("@mui/x-date-pickers/AdapterDayjs");
const DatePicker_1 = require("@mui/x-date-pickers/DatePicker");
const react_1 = require("react");
const Typography_1 = __importDefault(require("@mui/material/Typography"));
const color_palette_1 = require("@assets/styles/color-palette");
const CustomDateField = ({ setOpen, label, id, InputProps: { ref } = {}, inputProps: { 'aria-label': ariaLabel } = {}, }) => {
    return (<Typography_1.default id={id} ref={ref} aria-label={ariaLabel} onClick={() => setOpen((prevState) => !prevState)} sx={{
            borderBottom: `1px solid ${color_palette_1.colorPalette.primary.main}`,
            lineHeight: 2.5,
            '&:hover': {
                cursor: 'pointer',
            },
        }}>
			{label}
		</Typography_1.default>);
};
const CustomDaterPicker = ({ props }) => {
    const [open, setOpen] = (0, react_1.useState)(false);
    return (<DatePicker_1.DatePicker {...props} slots={{ ...props.slots, field: CustomDateField }} slotProps={{ ...props.slotProps, field: { setOpen } }} open={open} onClose={() => setOpen(false)} onOpen={() => setOpen(true)} sx={{
            '& .StaticDatePicker-calendarContainer .DayPicker-Day': {
                fontSize: 14,
            },
        }}/>);
};
const DatePicker = ({ value, onChange, customProps }) => {
    return (<x_date_pickers_1.LocalizationProvider dateAdapter={AdapterDayjs_1.AdapterDayjs}>
			<CustomDaterPicker props={{
            label: value.format('DD MMM, YYYY'),
            value: value,
            onChange: onChange,
            ...customProps,
        }}/>
		</x_date_pickers_1.LocalizationProvider>);
};
exports.default = DatePicker;
