"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.formatHMTDecimals = void 0;
const ethers_1 = require("ethers");
const formatHMTDecimals = (value) => {
    const formattedValue = Number((0, ethers_1.formatEther)(value));
    if (Number.isInteger(formattedValue)) {
        return formattedValue.toString();
    }
    if (Math.abs(formattedValue) < 1) {
        if (Math.abs(formattedValue) < 0.00000001) {
            return '0.00000001';
        }
        return formattedValue.toFixed(Math.min(8, formattedValue.toString().split('.')[1]?.length || 8));
    }
    const decimalPlaces = formattedValue.toString().split('.')[1]?.length || 0;
    return decimalPlaces > 4
        ? formattedValue.toFixed(4)
        : formattedValue.toString();
};
exports.formatHMTDecimals = formatHMTDecimals;
