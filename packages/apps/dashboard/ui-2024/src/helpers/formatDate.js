"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.formatDate = void 0;
const dayjs_1 = __importDefault(require("dayjs"));
const formatDate = (date, dateFormat) => {
    return (0, dayjs_1.default)(new Date(date)).format(dateFormat ?? 'dd/MM/yyyy');
};
exports.formatDate = formatDate;
