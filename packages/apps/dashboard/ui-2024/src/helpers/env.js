"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.env = void 0;
const zod_1 = require("zod");
const envSchema = zod_1.z.object({
    VITE_API_URL: zod_1.z.string(),
    VITE_NAVBAR_LINK_GITBOOK: zod_1.z.string(),
    VITE_NAVBAR_LINK_FAUCETS: zod_1.z.string(),
    VITE_NAVBAR_LINK_HUMAN_WEBSITE: zod_1.z.string(),
    VITE_NAVBAR_LINK_LAUNCH_JOBS: zod_1.z.string(),
    VITE_NAVBAR_LINK_WORK_AND_EARN: zod_1.z.string(),
    VITE_HUMANPROTOCOL_CORE_ARCHITECTURE: zod_1.z.string().optional(),
    VITE_FOOTER_LINK_TERMS_OF_SERVICE: zod_1.z.string(),
    VITE_FOOTER_LINK_PRIVACY_POLICY: zod_1.z.string(),
    VITE_FOOTER_LINK_HUMAN_PROTOCOL: zod_1.z.string(),
    VITE_FOOTER_LINK_GITHUB: zod_1.z.string(),
    VITE_FOOTER_LINK_DISCORD: zod_1.z.string(),
    VITE_FOOTER_LINK_X: zod_1.z.string(),
    VITE_FOOTER_LINK_TELEGRAM: zod_1.z.string(),
    VITE_FOOTER_LINK_LINKEDIN: zod_1.z.string(),
});
let validEnvs;
function setError() {
    const root = document.getElementById('root');
    if (!root)
        return;
    const errorDiv = document.createElement('div');
    errorDiv.textContent = 'Invalid .env file. Open devtools to see more details';
    root.appendChild(errorDiv);
}
try {
    validEnvs = envSchema.parse(import.meta.env);
}
catch (error) {
    if (error instanceof zod_1.ZodError) {
        console.error('Invalid .env file');
        error.issues.forEach((issue) => {
            console.error('Invalid env:', issue.path.join());
            console.error(issue);
        });
        setError();
        throw new Error();
    }
    setError();
    console.error(error);
    throw new Error();
}
exports.env = validEnvs;
