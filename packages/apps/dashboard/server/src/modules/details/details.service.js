"use strict";
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
exports.__esModule = true;
exports.DetailsService = void 0;
var class_transformer_1 = require("class-transformer");
var constants_1 = require("../../common/utils/constants");
var common_1 = require("@nestjs/common");
var sdk_1 = require("@human-protocol/sdk");
var wallet_dto_1 = require("./dto/wallet.dto");
var escrow_dto_1 = require("./dto/escrow.dto");
var leader_dto_1 = require("./dto/leader.dto");
var transaction_dto_1 = require("./dto/transaction.dto");
var rxjs_1 = require("rxjs");
var typechain_types_1 = require("@human-protocol/core/typechain-types");
var ethers_1 = require("ethers");
var DetailsService = /** @class */ (function () {
    function DetailsService(configService, httpService, networkConfig) {
        this.configService = configService;
        this.httpService = httpService;
        this.networkConfig = networkConfig;
        this.logger = new common_1.Logger(DetailsService_1.name);
    }
    DetailsService_1 = DetailsService;
    DetailsService.prototype.getDetails = function (chainId, address) {
        return __awaiter(this, void 0, void 0, function () {
            var escrowData, escrowDto, leaderData, leaderDto, _a, reputation, walletDto, _b, _c;
            var _d;
            return __generator(this, function (_e) {
                switch (_e.label) {
                    case 0: return [4 /*yield*/, sdk_1.EscrowUtils.getEscrow(chainId, address)];
                    case 1:
                        escrowData = _e.sent();
                        if (escrowData) {
                            escrowDto = (0, class_transformer_1.plainToInstance)(escrow_dto_1.EscrowDto, escrowData, {
                                excludeExtraneousValues: true
                            });
                            return [2 /*return*/, escrowDto];
                        }
                        return [4 /*yield*/, sdk_1.OperatorUtils.getLeader(chainId, address)];
                    case 2:
                        leaderData = _e.sent();
                        if (!leaderData) return [3 /*break*/, 5];
                        leaderDto = (0, class_transformer_1.plainToInstance)(leader_dto_1.LeaderDto, leaderData, {
                            excludeExtraneousValues: true
                        });
                        leaderDto.chainId = chainId;
                        _a = leaderDto;
                        return [4 /*yield*/, this.getHmtBalance(chainId, address)];
                    case 3:
                        _a.balance = _e.sent();
                        return [4 /*yield*/, this.fetchReputation(chainId, address)];
                    case 4:
                        reputation = (_e.sent()).reputation;
                        leaderDto.reputation = reputation;
                        return [2 /*return*/, leaderDto];
                    case 5:
                        _b = class_transformer_1.plainToInstance;
                        _c = [wallet_dto_1.WalletDto];
                        _d = {
                            chainId: chainId,
                            address: address
                        };
                        return [4 /*yield*/, this.getHmtBalance(chainId, address)];
                    case 6:
                        walletDto = _b.apply(void 0, _c.concat([(_d.balance = _e.sent(),
                                _d)]));
                        return [2 /*return*/, walletDto];
                }
            });
        });
    };
    DetailsService.prototype.getHmtBalance = function (chainId, hmtAddress) {
        return __awaiter(this, void 0, void 0, function () {
            var network, provider, hmtContract, _a, _b;
            return __generator(this, function (_c) {
                switch (_c.label) {
                    case 0:
                        network = this.networkConfig.networks.find(function (network) { return network.chainId === chainId; });
                        if (!network)
                            throw new common_1.BadRequestException('Invalid chainId provided');
                        provider = new ethers_1.ethers.JsonRpcProvider(network.rpcUrl);
                        hmtContract = typechain_types_1.HMToken__factory.connect(sdk_1.NETWORKS[chainId].hmtAddress, provider);
                        _b = (_a = ethers_1.ethers).formatEther;
                        return [4 /*yield*/, hmtContract.balanceOf(hmtAddress)];
                    case 1: return [2 /*return*/, _b.apply(_a, [_c.sent()])];
                }
            });
        });
    };
    DetailsService.prototype.getTransactions = function (chainId, address, first, skip) {
        return __awaiter(this, void 0, void 0, function () {
            var transactions, result;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0: return [4 /*yield*/, sdk_1.TransactionUtils.getTransactions({
                            chainId: chainId,
                            fromAddress: address,
                            toAddress: address,
                            first: first,
                            skip: skip
                        })];
                    case 1:
                        transactions = _a.sent();
                        result = transactions.map(function (transaction) {
                            var transcationPaginationObject = (0, class_transformer_1.plainToInstance)(transaction_dto_1.TransactionPaginationDto, __assign(__assign({}, transaction), { currentAddress: address }), { excludeExtraneousValues: true });
                            return transcationPaginationObject;
                        });
                        return [2 /*return*/, result];
                }
            });
        });
    };
    DetailsService.prototype.getEscrows = function (chainId, role, address, first, skip) {
        return __awaiter(this, void 0, void 0, function () {
            var filter, escrows, result;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        filter = {
                            chainId: chainId,
                            first: first,
                            skip: skip
                        };
                        switch (role) {
                            case sdk_1.Role.JobLauncher:
                                filter.launcher = address;
                                break;
                            case sdk_1.Role.ReputationOracle:
                                filter.reputationOracle = address;
                                break;
                            case sdk_1.Role.RecordingOracle:
                                filter.recordingOracle = address;
                                break;
                            case sdk_1.Role.ExchangeOracle:
                                filter.exchangeOracle = address;
                                break;
                        }
                        return [4 /*yield*/, sdk_1.EscrowUtils.getEscrows(filter)];
                    case 1:
                        escrows = _a.sent();
                        result = escrows.map(function (escrow) {
                            var escrowPaginationObject = (0, class_transformer_1.plainToInstance)(escrow_dto_1.EscrowPaginationDto, escrow, {
                                excludeExtraneousValues: true
                            });
                            return escrowPaginationObject;
                        });
                        return [2 /*return*/, result];
                }
            });
        });
    };
    DetailsService.prototype.getBestLeadersByRole = function (chainId) {
        return __awaiter(this, void 0, void 0, function () {
            var chainIds, leadersByRole, _i, chainIds_1, id, leadersData, _a, leadersData_1, leaderData, leaderDto, role, reputations;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        chainIds = !chainId
                            ? Object.values(constants_1.MainnetsId).filter(function (value) { return typeof value === 'number'; })
                            : [chainId];
                        leadersByRole = {};
                        _i = 0, chainIds_1 = chainIds;
                        _b.label = 1;
                    case 1:
                        if (!(_i < chainIds_1.length)) return [3 /*break*/, 4];
                        id = chainIds_1[_i];
                        return [4 /*yield*/, sdk_1.OperatorUtils.getLeaders({ chainId: id })];
                    case 2:
                        leadersData = _b.sent();
                        for (_a = 0, leadersData_1 = leadersData; _a < leadersData_1.length; _a++) {
                            leaderData = leadersData_1[_a];
                            leaderDto = (0, class_transformer_1.plainToInstance)(leader_dto_1.LeaderDto, leaderData, {
                                excludeExtraneousValues: true
                            });
                            leaderDto.chainId = id;
                            role = leaderDto.role;
                            if (Object.values(sdk_1.Role).includes(role)) {
                                if (!leadersByRole[role] ||
                                    BigInt(leaderDto.amountStaked) >
                                        BigInt(leadersByRole[role].amountStaked)) {
                                    leadersByRole[role] = leaderDto;
                                }
                            }
                        }
                        _b.label = 3;
                    case 3:
                        _i++;
                        return [3 /*break*/, 1];
                    case 4: return [4 /*yield*/, this.fetchReputations()];
                    case 5:
                        reputations = _b.sent();
                        this.assignReputationsToLeaders(Object.values(leadersByRole), reputations);
                        return [2 /*return*/, Object.values(leadersByRole)];
                }
            });
        });
    };
    DetailsService.prototype.getAllLeaders = function (chainId) {
        return __awaiter(this, void 0, void 0, function () {
            var chainIds, allLeaders, _i, chainIds_2, id, leadersData, _a, leadersData_2, leaderData, leaderDto, reputations;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        chainIds = !chainId
                            ? Object.values(constants_1.MainnetsId).filter(function (value) { return typeof value === 'number'; })
                            : [chainId];
                        allLeaders = [];
                        _i = 0, chainIds_2 = chainIds;
                        _b.label = 1;
                    case 1:
                        if (!(_i < chainIds_2.length)) return [3 /*break*/, 4];
                        id = chainIds_2[_i];
                        return [4 /*yield*/, sdk_1.OperatorUtils.getLeaders({ chainId: id })];
                    case 2:
                        leadersData = _b.sent();
                        for (_a = 0, leadersData_2 = leadersData; _a < leadersData_2.length; _a++) {
                            leaderData = leadersData_2[_a];
                            leaderDto = (0, class_transformer_1.plainToInstance)(leader_dto_1.LeaderDto, leaderData, {
                                excludeExtraneousValues: true
                            });
                            leaderDto.chainId = id;
                            if (leaderDto.role) {
                                allLeaders.push(leaderDto);
                            }
                        }
                        _b.label = 3;
                    case 3:
                        _i++;
                        return [3 /*break*/, 1];
                    case 4: return [4 /*yield*/, this.fetchReputations()];
                    case 5:
                        reputations = _b.sent();
                        this.assignReputationsToLeaders(allLeaders, reputations);
                        return [2 /*return*/, allLeaders];
                }
            });
        });
    };
    DetailsService.prototype.fetchReputation = function (chainId, address) {
        return __awaiter(this, void 0, void 0, function () {
            var response, error_1;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        _a.trys.push([0, 2, , 3]);
                        return [4 /*yield*/, (0, rxjs_1.firstValueFrom)(this.httpService.get(this.configService.reputationSource + "/reputation/".concat(address), {
                                params: {
                                    chain_id: chainId
                                }
                            }))];
                    case 1:
                        response = _a.sent();
                        return [2 /*return*/, response.data];
                    case 2:
                        error_1 = _a.sent();
                        this.logger.error('Error fetching reputation:', error_1);
                        return [2 /*return*/, { address: address, reputation: 'Not available' }];
                    case 3: return [2 /*return*/];
                }
            });
        });
    };
    DetailsService.prototype.fetchReputations = function () {
        return __awaiter(this, void 0, void 0, function () {
            var response, error_2;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        _a.trys.push([0, 2, , 3]);
                        return [4 /*yield*/, (0, rxjs_1.firstValueFrom)(this.httpService.get(this.configService.reputationSource + '/reputation', {
                                params: {
                                    chain_id: sdk_1.ChainId.POLYGON,
                                    roles: [
                                        'JOB_LAUNCHER',
                                        'EXCHANGE_ORACLE',
                                        'RECORDING_ORACLE',
                                        'REPUTATION_ORACLE',
                                    ]
                                }
                            }))];
                    case 1:
                        response = _a.sent();
                        return [2 /*return*/, response.data];
                    case 2:
                        error_2 = _a.sent();
                        this.logger.error('Error fetching reputations:', error_2);
                        return [2 /*return*/, []];
                    case 3: return [2 /*return*/];
                }
            });
        });
    };
    DetailsService.prototype.assignReputationsToLeaders = function (leaders, reputations) {
        var reputationMap = new Map(reputations.map(function (rep) { return [rep.address.toLowerCase(), rep.reputation]; }));
        leaders.forEach(function (leader) {
            var reputation = reputationMap.get(leader.address.toLowerCase());
            if (reputation) {
                leader.reputation = reputation;
            }
        });
    };
    var DetailsService_1;
    DetailsService = DetailsService_1 = __decorate([
        (0, common_1.Injectable)()
    ], DetailsService);
    return DetailsService;
}());
exports.DetailsService = DetailsService;
