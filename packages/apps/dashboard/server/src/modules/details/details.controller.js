"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
exports.__esModule = true;
exports.DetailsController = void 0;
var swagger_1 = require("@nestjs/swagger");
var common_1 = require("@nestjs/common");
var address_validation_pipe_1 = require("../../common/pipes/address-validation.pipe");
var details_response_dto_1 = require("./dto/details-response.dto");
var wallet_dto_1 = require("./dto/wallet.dto");
var escrow_dto_1 = require("./dto/escrow.dto");
var leader_dto_1 = require("./dto/leader.dto");
var constants_1 = require("../../common/utils/constants");
var DetailsController = /** @class */ (function () {
    function DetailsController(detailsService) {
        this.detailsService = detailsService;
    }
    DetailsController.prototype.leaders = function (chainId) {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                return [2 /*return*/, this.detailsService.getBestLeadersByRole(chainId)];
            });
        });
    };
    DetailsController.prototype.allLeaders = function (chainId) {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                return [2 /*return*/, this.detailsService.getAllLeaders(chainId)];
            });
        });
    };
    DetailsController.prototype.details = function (address, chainId) {
        return __awaiter(this, void 0, void 0, function () {
            var details, response, response, response;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0: return [4 /*yield*/, this.detailsService.getDetails(chainId, address)];
                    case 1:
                        details = _a.sent();
                        if (details instanceof wallet_dto_1.WalletDto) {
                            response = {
                                wallet: details
                            };
                            return [2 /*return*/, response];
                        }
                        if (details instanceof escrow_dto_1.EscrowDto) {
                            response = {
                                escrow: details
                            };
                            return [2 /*return*/, response];
                        }
                        if (details instanceof leader_dto_1.LeaderDto) {
                            response = {
                                leader: details
                            };
                            return [2 /*return*/, response];
                        }
                        return [2 /*return*/];
                }
            });
        });
    };
    DetailsController.prototype.transactions = function (address, query) {
        return __awaiter(this, void 0, void 0, function () {
            var transactions, response;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0: return [4 /*yield*/, this.detailsService.getTransactions(query.chainId, address, query.first, query.skip)];
                    case 1:
                        transactions = _a.sent();
                        response = {
                            address: address,
                            chainId: query.chainId,
                            first: query.first,
                            skip: query.skip,
                            results: transactions
                        };
                        return [2 /*return*/, response];
                }
            });
        });
    };
    DetailsController.prototype.escrows = function (address, query) {
        return __awaiter(this, void 0, void 0, function () {
            var escrows, response;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0: return [4 /*yield*/, this.detailsService.getEscrows(query.chainId, query.role, address, query.first, query.skip)];
                    case 1:
                        escrows = _a.sent();
                        response = {
                            address: address,
                            chainId: query.chainId,
                            first: query.first,
                            skip: query.skip,
                            results: escrows
                        };
                        return [2 /*return*/, response];
                }
            });
        });
    };
    __decorate([
        (0, common_1.Get)('/leaders'),
        (0, swagger_1.ApiQuery)({ name: 'chainId', "enum": constants_1.MainnetsId, required: false }),
        (0, common_1.HttpCode)(200),
        (0, swagger_1.ApiOperation)({
            summary: 'Get the best leaders by role',
            description: 'Returns the top leader for each role for a given chain or all chains.'
        }),
        (0, swagger_1.ApiResponse)({
            status: 200,
            description: 'Best leaders retrieved successfully',
            type: leader_dto_1.LeaderDto,
            isArray: true
        }),
        __param(0, (0, common_1.Query)('chainId'))
    ], DetailsController.prototype, "leaders");
    __decorate([
        (0, common_1.Get)('/leaders/all'),
        (0, swagger_1.ApiQuery)({ name: 'chainId', "enum": constants_1.MainnetsId, required: false }),
        (0, common_1.HttpCode)(200),
        (0, swagger_1.ApiOperation)({
            summary: 'Get all leaders',
            description: 'Returns all leaders for a given chain or all chains.'
        }),
        (0, swagger_1.ApiResponse)({
            status: 200,
            description: 'All leaders retrieved successfully',
            type: leader_dto_1.LeaderDto,
            isArray: true
        }),
        __param(0, (0, common_1.Query)('chainId'))
    ], DetailsController.prototype, "allLeaders");
    __decorate([
        (0, common_1.Get)('/:address'),
        (0, swagger_1.ApiQuery)({ name: 'chainId', "enum": constants_1.MainnetsId }),
        (0, common_1.HttpCode)(200),
        (0, swagger_1.ApiOperation)({
            summary: 'Get address details',
            description: 'Returns details about given address.'
        }),
        (0, swagger_1.ApiResponse)({
            status: 200,
            description: 'Details retrieved successfully',
            type: details_response_dto_1.DetailsResponseDto
        }),
        __param(0, (0, common_1.Param)('address', address_validation_pipe_1.AddressValidationPipe)),
        __param(1, (0, common_1.Query)('chainId'))
    ], DetailsController.prototype, "details");
    __decorate([
        (0, common_1.Get)('/transactions/:address'),
        (0, common_1.HttpCode)(200),
        (0, swagger_1.ApiOperation)({
            summary: 'Get transactions by address',
            description: 'Returns transactions for a given address.'
        }),
        (0, swagger_1.ApiResponse)({
            status: 200,
            description: 'Transactions retrieved successfully',
            type: details_response_dto_1.DetailsPaginationResponseDto
        }),
        __param(0, (0, common_1.Param)('address', address_validation_pipe_1.AddressValidationPipe)),
        __param(1, (0, common_1.Query)())
    ], DetailsController.prototype, "transactions");
    __decorate([
        (0, common_1.Get)('/escrows/:address'),
        (0, common_1.HttpCode)(200),
        (0, swagger_1.ApiOperation)({
            summary: 'Get escrows by address',
            description: 'Returns escrows for a given address.'
        }),
        (0, swagger_1.ApiResponse)({
            status: 200,
            description: 'Escrows retrieved successfully',
            type: details_response_dto_1.DetailsPaginationResponseDto
        }),
        __param(0, (0, common_1.Param)('address', address_validation_pipe_1.AddressValidationPipe)),
        __param(1, (0, common_1.Query)())
    ], DetailsController.prototype, "escrows");
    DetailsController = __decorate([
        (0, swagger_1.ApiTags)('Details'),
        (0, common_1.Controller)('/details'),
        (0, common_1.UsePipes)(new common_1.ValidationPipe({ transform: true }))
    ], DetailsController);
    return DetailsController;
}());
exports.DetailsController = DetailsController;
