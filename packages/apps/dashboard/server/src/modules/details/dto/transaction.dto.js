"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
exports.__esModule = true;
exports.TransactionPaginationDto = void 0;
var class_transformer_1 = require("class-transformer");
var class_validator_1 = require("class-validator");
var swagger_1 = require("@nestjs/swagger");
var TransactionPaginationDto = /** @class */ (function () {
    function TransactionPaginationDto() {
    }
    __decorate([
        (0, swagger_1.ApiProperty)({ example: 12345 }),
        (0, class_transformer_1.Transform)(function (_a) {
            var value = _a.value;
            return Number(value);
        }),
        (0, class_validator_1.IsNumber)(),
        (0, class_transformer_1.Expose)()
    ], TransactionPaginationDto.prototype, "block");
    __decorate([
        (0, swagger_1.ApiProperty)({
            example: '0x020efc94ef6d9d7aa9a4886cc9e1659f4f2b63557133c29d51f387bcb0c4afd7'
        }),
        (0, class_validator_1.IsString)(),
        (0, class_transformer_1.Expose)()
    ], TransactionPaginationDto.prototype, "txHash");
    __decorate([
        (0, swagger_1.ApiProperty)({ example: '0xad1F7e45D83624A0c628F1B03477c6E129EddB78' }),
        (0, class_validator_1.IsString)(),
        (0, class_transformer_1.Expose)()
    ], TransactionPaginationDto.prototype, "from");
    __decorate([
        (0, swagger_1.ApiProperty)({ example: '0xad1F7e45D83624A0c628F1B03477c6E129EddB78' }),
        (0, class_validator_1.IsString)(),
        (0, class_transformer_1.Expose)()
    ], TransactionPaginationDto.prototype, "to");
    __decorate([
        (0, swagger_1.ApiProperty)({ example: '0.123' }),
        (0, class_transformer_1.Transform)(function (_a) {
            var value = _a.value, obj = _a.obj;
            return obj.currentAddress.toLowerCase() === obj.from.toLowerCase()
                ? "-".concat(value.toString())
                : value.toString();
        }),
        (0, class_validator_1.IsString)(),
        (0, class_transformer_1.Expose)()
    ], TransactionPaginationDto.prototype, "value");
    __decorate([
        (0, swagger_1.ApiProperty)({ example: 'Transfer' }),
        (0, class_validator_1.IsString)(),
        (0, class_transformer_1.Expose)()
    ], TransactionPaginationDto.prototype, "method");
    return TransactionPaginationDto;
}());
exports.TransactionPaginationDto = TransactionPaginationDto;
