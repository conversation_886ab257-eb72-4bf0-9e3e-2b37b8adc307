"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
exports.__esModule = true;
exports.LeaderDto = void 0;
var class_transformer_1 = require("class-transformer");
var class_validator_1 = require("class-validator");
var swagger_1 = require("@nestjs/swagger");
var sdk_1 = require("@human-protocol/sdk");
var LeaderDto = /** @class */ (function () {
    function LeaderDto() {
    }
    __decorate([
        (0, swagger_1.ApiProperty)({ example: sdk_1.ChainId.POLYGON_AMOY }),
        (0, class_validator_1.IsEnum)(sdk_1.ChainId)
    ], LeaderDto.prototype, "chainId");
    __decorate([
        (0, swagger_1.ApiProperty)({ example: '0xb794f5ea0ba39494ce839613fffba74279579268' }),
        (0, class_validator_1.IsString)(),
        (0, class_transformer_1.Expose)()
    ], LeaderDto.prototype, "address");
    __decorate([
        (0, swagger_1.ApiProperty)({ example: '0.07007358932392' }),
        (0, class_validator_1.IsString)()
    ], LeaderDto.prototype, "balance");
    __decorate([
        (0, swagger_1.ApiProperty)({ example: sdk_1.Role.JobLauncher }),
        (0, swagger_1.ApiPropertyOptional)(),
        (0, class_validator_1.IsOptional)(),
        (0, class_validator_1.IsString)(),
        (0, class_transformer_1.Expose)()
    ], LeaderDto.prototype, "role");
    __decorate([
        (0, swagger_1.ApiProperty)({ example: '0.07007358932392' }),
        (0, class_transformer_1.Transform)(function (_a) {
            var value = _a.value;
            return value.toString();
        }),
        (0, class_validator_1.IsString)(),
        (0, class_transformer_1.Expose)()
    ], LeaderDto.prototype, "amountStaked");
    __decorate([
        (0, swagger_1.ApiProperty)({ example: 'High' }),
        (0, class_transformer_1.Transform)(function (_a) {
            var value = _a.value;
            return value.toString();
        }),
        (0, class_validator_1.IsString)(),
        (0, class_transformer_1.Expose)()
    ], LeaderDto.prototype, "reputation");
    __decorate([
        (0, swagger_1.ApiProperty)({ example: 3 }),
        (0, class_transformer_1.Transform)(function (_a) {
            var value = _a.value;
            return Number(value);
        }),
        (0, class_validator_1.IsNumber)(),
        (0, class_transformer_1.Expose)()
    ], LeaderDto.prototype, "fee");
    __decorate([
        (0, swagger_1.ApiProperty)({ example: ['Bounding Box', 'Skeletons'] }),
        (0, swagger_1.ApiPropertyOptional)(),
        (0, class_validator_1.IsOptional)(),
        (0, class_validator_1.IsArray)(),
        (0, class_transformer_1.Expose)()
    ], LeaderDto.prototype, "jobTypes");
    __decorate([
        (0, swagger_1.ApiProperty)({ example: 'https://example.test' }),
        (0, swagger_1.ApiPropertyOptional)(),
        (0, class_validator_1.IsOptional)(),
        (0, class_validator_1.IsUrl)(),
        (0, class_transformer_1.Expose)()
    ], LeaderDto.prototype, "url");
    __decorate([
        (0, swagger_1.ApiProperty)({ example: 1 }),
        (0, class_validator_1.IsNumber)(),
        (0, class_transformer_1.Expose)()
    ], LeaderDto.prototype, "amountJobsProcessed");
    return LeaderDto;
}());
exports.LeaderDto = LeaderDto;
