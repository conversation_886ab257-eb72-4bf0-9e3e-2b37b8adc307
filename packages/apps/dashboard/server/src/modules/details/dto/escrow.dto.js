"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
exports.__esModule = true;
exports.EscrowPaginationDto = exports.EscrowDto = void 0;
var class_transformer_1 = require("class-transformer");
var class_validator_1 = require("class-validator");
var swagger_1 = require("@nestjs/swagger");
var sdk_1 = require("@human-protocol/sdk");
var EscrowDto = /** @class */ (function () {
    function EscrowDto() {
    }
    __decorate([
        (0, swagger_1.ApiProperty)({ example: sdk_1.ChainId.POLYGON_AMOY }),
        (0, class_validator_1.IsEnum)(sdk_1.ChainId),
        (0, class_transformer_1.Expose)()
    ], EscrowDto.prototype, "chainId");
    __decorate([
        (0, swagger_1.ApiProperty)({ example: '0xb794f5ea0ba39494ce839613fffba74279579268' }),
        (0, class_validator_1.IsString)(),
        (0, class_transformer_1.Expose)()
    ], EscrowDto.prototype, "address");
    __decorate([
        (0, swagger_1.ApiProperty)({ example: '0.07007358932392' }),
        (0, class_validator_1.IsString)(),
        (0, class_transformer_1.Expose)()
    ], EscrowDto.prototype, "balance");
    __decorate([
        (0, swagger_1.ApiProperty)({ example: 'HMT' }),
        (0, class_validator_1.IsString)(),
        (0, class_transformer_1.Expose)()
    ], EscrowDto.prototype, "token");
    __decorate([
        (0, swagger_1.ApiProperty)({ example: '0xb794f5ea0ba39494ce839613fffba74279579268' }),
        (0, class_validator_1.IsString)(),
        (0, class_transformer_1.Expose)()
    ], EscrowDto.prototype, "factoryAddress");
    __decorate([
        (0, swagger_1.ApiProperty)({ example: '0.07007358932392' }),
        (0, class_validator_1.IsString)(),
        (0, class_transformer_1.Expose)()
    ], EscrowDto.prototype, "totalFundedAmount");
    __decorate([
        (0, swagger_1.ApiProperty)({ example: '0.07007358932392' }),
        (0, class_validator_1.IsString)(),
        (0, class_transformer_1.Expose)()
    ], EscrowDto.prototype, "amountPaid");
    __decorate([
        (0, swagger_1.ApiProperty)({ example: 'Launched' }),
        (0, class_validator_1.IsString)(),
        (0, class_transformer_1.Expose)()
    ], EscrowDto.prototype, "status");
    __decorate([
        (0, swagger_1.ApiProperty)({ example: 'https://example.test/manifest' }),
        (0, class_validator_1.IsUrl)(),
        (0, class_transformer_1.Expose)()
    ], EscrowDto.prototype, "manifest");
    __decorate([
        (0, swagger_1.ApiProperty)({ example: '0xb794f5ea0ba39494ce839613fffba74279579268' }),
        (0, class_validator_1.IsString)(),
        (0, class_transformer_1.Expose)()
    ], EscrowDto.prototype, "launcher");
    __decorate([
        (0, swagger_1.ApiProperty)({ example: '0xb794f5ea0ba39494ce839613fffba74279579268' }),
        (0, class_validator_1.IsString)(),
        (0, class_transformer_1.Expose)()
    ], EscrowDto.prototype, "exchangeOracle");
    __decorate([
        (0, swagger_1.ApiProperty)({ example: '0xb794f5ea0ba39494ce839613fffba74279579268' }),
        (0, class_validator_1.IsString)(),
        (0, class_transformer_1.Expose)()
    ], EscrowDto.prototype, "recordingOracle");
    __decorate([
        (0, swagger_1.ApiProperty)({ example: '0xb794f5ea0ba39494ce839613fffba74279579268' }),
        (0, class_validator_1.IsString)(),
        (0, class_transformer_1.Expose)()
    ], EscrowDto.prototype, "reputationOracle");
    __decorate([
        (0, swagger_1.ApiProperty)({ example: 'https://example.test/final-results' }),
        (0, class_validator_1.IsUrl)(),
        (0, class_transformer_1.Expose)()
    ], EscrowDto.prototype, "finalResultsUrl");
    return EscrowDto;
}());
exports.EscrowDto = EscrowDto;
var EscrowPaginationDto = /** @class */ (function () {
    function EscrowPaginationDto() {
    }
    __decorate([
        (0, swagger_1.ApiProperty)({ example: sdk_1.ChainId.POLYGON_AMOY }),
        (0, class_validator_1.IsEnum)(sdk_1.ChainId),
        (0, class_transformer_1.Expose)()
    ], EscrowPaginationDto.prototype, "chainId");
    __decorate([
        (0, swagger_1.ApiProperty)({ example: '0xb794f5ea0ba39494ce839613fffba74279579268' }),
        (0, class_validator_1.IsString)(),
        (0, class_transformer_1.Expose)()
    ], EscrowPaginationDto.prototype, "address");
    __decorate([
        (0, swagger_1.ApiProperty)({ example: 'Launched' }),
        (0, class_validator_1.IsString)(),
        (0, class_transformer_1.Expose)()
    ], EscrowPaginationDto.prototype, "status");
    return EscrowPaginationDto;
}());
exports.EscrowPaginationDto = EscrowPaginationDto;
