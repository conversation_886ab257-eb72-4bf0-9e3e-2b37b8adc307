"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
exports.__esModule = true;
exports.DetailsEscrowsPaginationDto = exports.DetailsTransactionsPaginationDto = void 0;
var class_transformer_1 = require("class-transformer");
var class_validator_1 = require("class-validator");
var swagger_1 = require("@nestjs/swagger");
var sdk_1 = require("@human-protocol/sdk");
var role_validation_1 = require("./validation/role-validation");
var constants_1 = require("../../../common/utils/constants");
var DetailsTransactionsPaginationDto = /** @class */ (function () {
    function DetailsTransactionsPaginationDto() {
        this.first = 10;
        this.skip = 0;
    }
    __decorate([
        (0, swagger_1.ApiProperty)({ "enum": constants_1.MainnetsId }),
        (0, class_validator_1.IsEnum)(sdk_1.ChainId),
        (0, class_validator_1.IsIn)(Object.values(constants_1.MainnetsId)),
        (0, class_transformer_1.Transform)(function (_a) {
            var value = _a.value;
            return parseInt(value);
        })
    ], DetailsTransactionsPaginationDto.prototype, "chainId");
    __decorate([
        (0, swagger_1.ApiPropertyOptional)({
            minimum: 0,
            "default": 10
        }),
        (0, class_transformer_1.Type)(function () { return Number; }),
        (0, class_validator_1.IsNumber)(),
        (0, class_validator_1.Min)(0),
        (0, class_validator_1.Max)(1000),
        (0, class_validator_1.IsOptional)()
    ], DetailsTransactionsPaginationDto.prototype, "first");
    __decorate([
        (0, swagger_1.ApiPropertyOptional)({
            minimum: 0,
            "default": 0
        }),
        (0, class_transformer_1.Type)(function () { return Number; }),
        (0, class_validator_1.IsNumber)(),
        (0, class_validator_1.Min)(0),
        (0, class_validator_1.Max)(1000),
        (0, class_validator_1.IsOptional)()
    ], DetailsTransactionsPaginationDto.prototype, "skip");
    return DetailsTransactionsPaginationDto;
}());
exports.DetailsTransactionsPaginationDto = DetailsTransactionsPaginationDto;
var DetailsEscrowsPaginationDto = /** @class */ (function () {
    function DetailsEscrowsPaginationDto() {
        this.first = 10;
        this.skip = 0;
    }
    __decorate([
        (0, swagger_1.ApiProperty)({ "enum": constants_1.MainnetsId }),
        (0, class_validator_1.IsEnum)(sdk_1.ChainId),
        (0, class_validator_1.IsIn)(Object.values(constants_1.MainnetsId)),
        (0, class_transformer_1.Transform)(function (_a) {
            var value = _a.value;
            return parseInt(value);
        })
    ], DetailsEscrowsPaginationDto.prototype, "chainId");
    __decorate([
        (0, swagger_1.ApiProperty)(),
        (0, class_validator_1.IsString)(),
        (0, role_validation_1.IsRoleValid)()
    ], DetailsEscrowsPaginationDto.prototype, "role");
    __decorate([
        (0, swagger_1.ApiPropertyOptional)({
            minimum: 0,
            "default": 10
        }),
        (0, class_transformer_1.Type)(function () { return Number; }),
        (0, class_validator_1.IsNumber)(),
        (0, class_validator_1.Min)(0),
        (0, class_validator_1.Max)(1000),
        (0, class_validator_1.IsOptional)()
    ], DetailsEscrowsPaginationDto.prototype, "first");
    __decorate([
        (0, swagger_1.ApiPropertyOptional)({
            minimum: 0,
            "default": 0
        }),
        (0, class_transformer_1.Type)(function () { return Number; }),
        (0, class_validator_1.IsNumber)(),
        (0, class_validator_1.Min)(0),
        (0, class_validator_1.Max)(1000),
        (0, class_validator_1.IsOptional)()
    ], DetailsEscrowsPaginationDto.prototype, "skip");
    return DetailsEscrowsPaginationDto;
}());
exports.DetailsEscrowsPaginationDto = DetailsEscrowsPaginationDto;
