"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
exports.__esModule = true;
exports.DetailsPaginationResponseDto = exports.DetailsResponseDto = void 0;
var class_validator_1 = require("class-validator");
var swagger_1 = require("@nestjs/swagger");
var sdk_1 = require("@human-protocol/sdk");
var DetailsResponseDto = /** @class */ (function () {
    function DetailsResponseDto() {
    }
    __decorate([
        (0, swagger_1.ApiProperty)(),
        (0, swagger_1.ApiPropertyOptional)(),
        (0, class_validator_1.IsOptional)()
    ], DetailsResponseDto.prototype, "wallet");
    __decorate([
        (0, swagger_1.ApiProperty)(),
        (0, swagger_1.ApiPropertyOptional)(),
        (0, class_validator_1.IsOptional)()
    ], DetailsResponseDto.prototype, "escrow");
    __decorate([
        (0, swagger_1.ApiProperty)(),
        (0, swagger_1.ApiPropertyOptional)(),
        (0, class_validator_1.IsOptional)()
    ], DetailsResponseDto.prototype, "leader");
    return DetailsResponseDto;
}());
exports.DetailsResponseDto = DetailsResponseDto;
var DetailsPaginationResponseDto = /** @class */ (function () {
    function DetailsPaginationResponseDto() {
    }
    __decorate([
        (0, swagger_1.ApiProperty)({ example: '******************************************' })
    ], DetailsPaginationResponseDto.prototype, "address");
    __decorate([
        (0, swagger_1.ApiProperty)({ example: sdk_1.ChainId.POLYGON_AMOY })
    ], DetailsPaginationResponseDto.prototype, "chainId");
    __decorate([
        (0, swagger_1.ApiProperty)({ example: 10 })
    ], DetailsPaginationResponseDto.prototype, "first");
    __decorate([
        (0, swagger_1.ApiProperty)({ example: 0 })
    ], DetailsPaginationResponseDto.prototype, "skip");
    __decorate([
        (0, swagger_1.ApiProperty)(),
        (0, class_validator_1.IsArray)()
    ], DetailsPaginationResponseDto.prototype, "results");
    return DetailsPaginationResponseDto;
}());
exports.DetailsPaginationResponseDto = DetailsPaginationResponseDto;
