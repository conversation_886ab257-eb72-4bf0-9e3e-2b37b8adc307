"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
exports.__esModule = true;
exports.IsRoleValid = exports.IsValidRoleConstraint = void 0;
var class_validator_1 = require("class-validator");
var sdk_1 = require("@human-protocol/sdk");
var IsValidRoleConstraint = /** @class */ (function () {
    function IsValidRoleConstraint() {
    }
    IsValidRoleConstraint.prototype.validate = function (role) {
        return Object.values(sdk_1.Role).includes(role);
    };
    IsValidRoleConstraint.prototype.defaultMessage = function () {
        return "Role must be one of the following values: ".concat(Object.values(sdk_1.Role).join(', '));
    };
    IsValidRoleConstraint = __decorate([
        (0, class_validator_1.ValidatorConstraint)({ async: false })
    ], IsValidRoleConstraint);
    return IsValidRoleConstraint;
}());
exports.IsValidRoleConstraint = IsValidRoleConstraint;
function IsRoleValid() {
    return function (object, propertyName) {
        (0, class_validator_1.registerDecorator)({
            target: object.constructor,
            propertyName: propertyName,
            validator: IsValidRoleConstraint
        });
    };
}
exports.IsRoleValid = IsRoleValid;
