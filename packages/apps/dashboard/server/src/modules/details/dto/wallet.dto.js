"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
exports.__esModule = true;
exports.WalletDto = void 0;
var class_validator_1 = require("class-validator");
var swagger_1 = require("@nestjs/swagger");
var sdk_1 = require("@human-protocol/sdk");
var WalletDto = /** @class */ (function () {
    function WalletDto() {
    }
    __decorate([
        (0, swagger_1.ApiProperty)({ example: sdk_1.ChainId.POLYGON_AMOY }),
        (0, class_validator_1.IsEnum)(sdk_1.ChainId)
    ], WalletDto.prototype, "chainId");
    __decorate([
        (0, swagger_1.ApiProperty)({ example: '******************************************' }),
        (0, class_validator_1.IsString)()
    ], WalletDto.prototype, "address");
    __decorate([
        (0, swagger_1.ApiProperty)({ example: '0.07007358932392' }),
        (0, class_validator_1.IsString)()
    ], WalletDto.prototype, "balance");
    return WalletDto;
}());
exports.WalletDto = WalletDto;
