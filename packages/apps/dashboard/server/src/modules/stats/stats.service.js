"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
exports.__esModule = true;
exports.StatsService = void 0;
var common_1 = require("@nestjs/common");
var rxjs_1 = require("rxjs");
var dayjs = require("dayjs");
var schedule_1 = require("@nestjs/schedule");
var cache_manager_1 = require("@nestjs/cache-manager");
var sdk_1 = require("@human-protocol/sdk");
var env_config_service_1 = require("../../common/config/env-config.service");
var redis_config_service_1 = require("../../common/config/redis-config.service");
var env_config_service_2 = require("../../common/config/env-config.service");
var constants_1 = require("../../common/utils/constants");
var StatsService = /** @class */ (function () {
    function StatsService(cacheManager, redisConfigService, envConfigService, httpService, storageService) {
        this.cacheManager = cacheManager;
        this.redisConfigService = redisConfigService;
        this.envConfigService = envConfigService;
        this.httpService = httpService;
        this.storageService = storageService;
        this.logger = new common_1.Logger(StatsService_1.name);
    }
    StatsService_1 = StatsService;
    StatsService.prototype.onModuleInit = function () {
        return __awaiter(this, void 0, void 0, function () {
            var isHistoricalDataFetched, isHmtGeneralStatsFetched, isHmtDailyStatsFetched;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0: return [4 /*yield*/, this.isHistoricalDataFetched()];
                    case 1:
                        isHistoricalDataFetched = _a.sent();
                        return [4 /*yield*/, this.isHmtGeneralStatsFetched()];
                    case 2:
                        isHmtGeneralStatsFetched = _a.sent();
                        return [4 /*yield*/, this.isHmtDailyStatsFetched()];
                    case 3:
                        isHmtDailyStatsFetched = _a.sent();
                        if (!!isHistoricalDataFetched) return [3 /*break*/, 5];
                        return [4 /*yield*/, this.fetchHistoricalHcaptchaStats()];
                    case 4:
                        _a.sent();
                        _a.label = 5;
                    case 5:
                        if (!!isHmtGeneralStatsFetched) return [3 /*break*/, 7];
                        return [4 /*yield*/, this.fetchHmtGeneralStats()];
                    case 6:
                        _a.sent();
                        _a.label = 7;
                    case 7:
                        if (!!isHmtDailyStatsFetched) return [3 /*break*/, 9];
                        return [4 /*yield*/, this.fetchHistoricalHmtStats()];
                    case 8:
                        _a.sent();
                        _a.label = 9;
                    case 9: return [2 /*return*/];
                }
            });
        });
    };
    StatsService.prototype.isHistoricalDataFetched = function () {
        return __awaiter(this, void 0, void 0, function () {
            var data;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0: return [4 /*yield*/, this.cacheManager.get("".concat(redis_config_service_1.HCAPTCHA_PREFIX).concat(env_config_service_2.HCAPTCHA_STATS_START_DATE))];
                    case 1:
                        data = _a.sent();
                        return [2 /*return*/, !!data];
                }
            });
        });
    };
    StatsService.prototype.fetchHistoricalHcaptchaStats = function () {
        return __awaiter(this, void 0, void 0, function () {
            var startDate, currentDate, dates, from, to, results, _i, dates_1, range, data, _a, results_1, monthData, _b, _c, _d, date, value, multiplier, dates_2, month;
            return __generator(this, function (_e) {
                switch (_e.label) {
                    case 0:
                        this.logger.log('Fetching historical hCaptcha stats.');
                        startDate = dayjs(env_config_service_1.HCAPTCHA_STATS_API_START_DATE);
                        currentDate = dayjs();
                        dates = [];
                        while (startDate <= currentDate) {
                            from = startDate.startOf('month').format('YYYY-MM-DD');
                            to = startDate.endOf('month').format('YYYY-MM-DD');
                            dates.push({ from: from, to: to });
                            startDate = startDate.add(1, 'month');
                        }
                        return [4 /*yield*/, this.storageService.downloadFile(this.envConfigService.hCaptchaStatsFile)];
                    case 1:
                        results = _e.sent();
                        _i = 0, dates_1 = dates;
                        _e.label = 2;
                    case 2:
                        if (!(_i < dates_1.length)) return [3 /*break*/, 5];
                        range = dates_1[_i];
                        return [4 /*yield*/, (0, rxjs_1.lastValueFrom)(this.httpService.get(this.envConfigService.hCaptchaStatsSource, {
                                params: {
                                    start_date: range.from,
                                    end_date: range.to,
                                    api_key: this.envConfigService.hCaptchaApiKey
                                }
                            }))];
                    case 3:
                        data = (_e.sent()).data;
                        results.push(data);
                        _e.label = 4;
                    case 4:
                        _i++;
                        return [3 /*break*/, 2];
                    case 5:
                        _a = 0, results_1 = results;
                        _e.label = 6;
                    case 6:
                        if (!(_a < results_1.length)) return [3 /*break*/, 13];
                        monthData = results_1[_a];
                        _b = 0, _c = Object.entries(monthData);
                        _e.label = 7;
                    case 7:
                        if (!(_b < _c.length)) return [3 /*break*/, 12];
                        _d = _c[_b], date = _d[0], value = _d[1];
                        multiplier = date <= '2022-11-30' ? 18 : 9;
                        if (value.served)
                            delete value.served;
                        value.solved *= multiplier;
                        if (!(date !== 'total')) return [3 /*break*/, 9];
                        return [4 /*yield*/, this.cacheManager.set("".concat(redis_config_service_1.HCAPTCHA_PREFIX).concat(date), value)];
                    case 8:
                        _e.sent();
                        return [3 /*break*/, 11];
                    case 9:
                        dates_2 = Object.keys(monthData).filter(function (key) { return key !== 'total'; });
                        if (!(dates_2.length > 0)) return [3 /*break*/, 11];
                        month = dayjs(dates_2[0]).format('YYYY-MM');
                        return [4 /*yield*/, this.cacheManager.set("".concat(redis_config_service_1.HCAPTCHA_PREFIX).concat(month), value)];
                    case 10:
                        _e.sent();
                        _e.label = 11;
                    case 11:
                        _b++;
                        return [3 /*break*/, 7];
                    case 12:
                        _a++;
                        return [3 /*break*/, 6];
                    case 13: return [2 /*return*/];
                }
            });
        });
    };
    StatsService.prototype.isHmtGeneralStatsFetched = function () {
        return __awaiter(this, void 0, void 0, function () {
            var data;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0: return [4 /*yield*/, this.cacheManager.get(this.redisConfigService.hmtGeneralStatsCacheKey)];
                    case 1:
                        data = _a.sent();
                        return [2 /*return*/, !!data];
                }
            });
        });
    };
    StatsService.prototype.fetchTodayHcaptchaStats = function () {
        return __awaiter(this, void 0, void 0, function () {
            var today, from, to, data, multiplier, stats, currentMonth, daysInMonth, dates, aggregatedStats;
            var _this = this;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        this.logger.log('Fetching hCaptcha stats for today.');
                        today = dayjs().format('YYYY-MM-DD');
                        from = today;
                        to = today;
                        return [4 /*yield*/, (0, rxjs_1.lastValueFrom)(this.httpService.get(this.envConfigService.hCaptchaStatsSource, {
                                params: {
                                    start_date: from,
                                    end_date: to,
                                    api_key: this.envConfigService.hCaptchaApiKey
                                }
                            }))];
                    case 1:
                        data = (_a.sent()).data;
                        multiplier = today <= '2022-11-30' ? 18 : 9;
                        stats = data[today];
                        if (!stats) return [3 /*break*/, 3];
                        if (stats.served)
                            delete stats.served;
                        stats.solved *= multiplier;
                        return [4 /*yield*/, this.cacheManager.set("".concat(redis_config_service_1.HCAPTCHA_PREFIX).concat(today), stats)];
                    case 2:
                        _a.sent();
                        _a.label = 3;
                    case 3:
                        currentMonth = dayjs().format('YYYY-MM');
                        daysInMonth = dayjs().daysInMonth();
                        dates = Array.from({ length: daysInMonth }, function (_, i) { return "".concat(currentMonth, "-").concat(String(i + 1).padStart(2, '0')); });
                        return [4 /*yield*/, Promise.all(dates.map(function (date) { return __awaiter(_this, void 0, void 0, function () {
                                var dailyStats;
                                return __generator(this, function (_a) {
                                    switch (_a.label) {
                                        case 0: return [4 /*yield*/, this.cacheManager.get("".concat(redis_config_service_1.HCAPTCHA_PREFIX).concat(date))];
                                        case 1:
                                            dailyStats = _a.sent();
                                            return [2 /*return*/, dailyStats || { solved: 0 }];
                                    }
                                });
                            }); })).then(function (statsArray) {
                                return statsArray.reduce(function (acc, stats) {
                                    acc.solved += stats.solved;
                                    return acc;
                                }, { solved: 0 });
                            })];
                    case 4:
                        aggregatedStats = _a.sent();
                        return [4 /*yield*/, this.cacheManager.set("".concat(redis_config_service_1.HCAPTCHA_PREFIX).concat(currentMonth), aggregatedStats)];
                    case 5:
                        _a.sent();
                        return [2 /*return*/];
                }
            });
        });
    };
    StatsService.prototype.fetchHmtGeneralStats = function () {
        return __awaiter(this, void 0, void 0, function () {
            var aggregatedStats, _i, _a, network, statisticsClient, generalStats;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        this.logger.log('Fetching HMT general stats across multiple networks.');
                        aggregatedStats = {
                            totalHolders: 0,
                            totalTransactions: 0
                        };
                        _i = 0, _a = Object.values(constants_1.MainnetsId).filter(function (value) { return typeof value === 'number'; });
                        _b.label = 1;
                    case 1:
                        if (!(_i < _a.length)) return [3 /*break*/, 4];
                        network = _a[_i];
                        statisticsClient = new sdk_1.StatisticsClient(sdk_1.NETWORKS[network]);
                        return [4 /*yield*/, statisticsClient.getHMTStatistics()];
                    case 2:
                        generalStats = _b.sent();
                        aggregatedStats.totalHolders += generalStats.totalHolders;
                        aggregatedStats.totalTransactions += generalStats.totalTransferCount;
                        _b.label = 3;
                    case 3:
                        _i++;
                        return [3 /*break*/, 1];
                    case 4: return [4 /*yield*/, this.cacheManager.set(this.redisConfigService.hmtGeneralStatsCacheKey, aggregatedStats)];
                    case 5:
                        _b.sent();
                        return [2 /*return*/];
                }
            });
        });
    };
    StatsService.prototype.isHmtDailyStatsFetched = function () {
        return __awaiter(this, void 0, void 0, function () {
            var data;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0: return [4 /*yield*/, this.cacheManager.get("".concat(redis_config_service_1.HMT_PREFIX).concat(env_config_service_1.HMT_STATS_START_DATE))];
                    case 1:
                        data = _a.sent();
                        return [2 /*return*/, !!data];
                }
            });
        });
    };
    StatsService.prototype.fetchHistoricalHmtStats = function () {
        return __awaiter(this, void 0, void 0, function () {
            var startDate;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        startDate = dayjs(env_config_service_1.HMT_STATS_START_DATE);
                        return [4 /*yield*/, this.fetchAndCacheHmtDailyStats(startDate.format('YYYY-MM-DD'))];
                    case 1:
                        _a.sent();
                        return [2 /*return*/];
                }
            });
        });
    };
    StatsService.prototype.fetchHmtDailyStats = function () {
        return __awaiter(this, void 0, void 0, function () {
            var currentDate;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        currentDate = dayjs().format('YYYY-MM-DD');
                        return [4 /*yield*/, this.fetchAndCacheHmtDailyStats(currentDate)];
                    case 1:
                        _a.sent();
                        return [2 /*return*/];
                }
            });
        });
    };
    StatsService.prototype.fetchAndCacheHmtDailyStats = function (date) {
        return __awaiter(this, void 0, void 0, function () {
            var from, to, dailyData, monthlyData, _i, _a, _b, dailyCacheKey, stats, _c, _d, _e, month, stats, monthlyCacheKey;
            var _this = this;
            return __generator(this, function (_f) {
                switch (_f.label) {
                    case 0:
                        from = new Date(date);
                        to = new Date(dayjs().format('YYYY-MM-DD'));
                        dailyData = {};
                        monthlyData = {};
                        // Fetch daily data for each network
                        return [4 /*yield*/, Promise.all(Object.values(constants_1.MainnetsId).filter(function (value) { return typeof value === 'number'; }).map(function (network) { return __awaiter(_this, void 0, void 0, function () {
                                var statisticsClient, skip, fetchedRecords, _i, fetchedRecords_1, record, dailyCacheKey, month;
                                return __generator(this, function (_a) {
                                    switch (_a.label) {
                                        case 0:
                                            statisticsClient = new sdk_1.StatisticsClient(sdk_1.NETWORKS[network]);
                                            skip = 0;
                                            fetchedRecords = [];
                                            _a.label = 1;
                                        case 1: return [4 /*yield*/, statisticsClient.getHMTDailyData({
                                                from: from,
                                                to: to,
                                                first: 1000,
                                                skip: skip
                                            })];
                                        case 2:
                                            fetchedRecords = _a.sent();
                                            for (_i = 0, fetchedRecords_1 = fetchedRecords; _i < fetchedRecords_1.length; _i++) {
                                                record = fetchedRecords_1[_i];
                                                dailyCacheKey = "".concat(redis_config_service_1.HMT_PREFIX).concat(record.timestamp.toISOString().split('T')[0]);
                                                // Sum daily values
                                                if (!dailyData[dailyCacheKey]) {
                                                    dailyData[dailyCacheKey] = {
                                                        totalTransactionAmount: '0',
                                                        totalTransactionCount: 0,
                                                        dailyUniqueSenders: 0,
                                                        dailyUniqueReceivers: 0
                                                    };
                                                }
                                                dailyData[dailyCacheKey].totalTransactionAmount = (BigInt(dailyData[dailyCacheKey].totalTransactionAmount) +
                                                    BigInt(record.totalTransactionAmount)).toString();
                                                dailyData[dailyCacheKey].totalTransactionCount +=
                                                    record.totalTransactionCount;
                                                dailyData[dailyCacheKey].dailyUniqueSenders +=
                                                    record.dailyUniqueSenders;
                                                dailyData[dailyCacheKey].dailyUniqueReceivers +=
                                                    record.dailyUniqueReceivers;
                                                month = dayjs(record.timestamp).format('YYYY-MM');
                                                if (!monthlyData[month]) {
                                                    monthlyData[month] = {
                                                        totalTransactionAmount: '0',
                                                        totalTransactionCount: 0,
                                                        dailyUniqueSenders: 0,
                                                        dailyUniqueReceivers: 0
                                                    };
                                                }
                                                monthlyData[month].totalTransactionAmount = (BigInt(monthlyData[month].totalTransactionAmount) +
                                                    BigInt(record.totalTransactionAmount)).toString();
                                                monthlyData[month].totalTransactionCount +=
                                                    record.totalTransactionCount;
                                                monthlyData[month].dailyUniqueSenders += record.dailyUniqueSenders;
                                                monthlyData[month].dailyUniqueReceivers +=
                                                    record.dailyUniqueReceivers;
                                            }
                                            skip += 1000;
                                            _a.label = 3;
                                        case 3:
                                            if (fetchedRecords.length === 1000) return [3 /*break*/, 1];
                                            _a.label = 4;
                                        case 4: return [2 /*return*/];
                                    }
                                });
                            }); }))];
                    case 1:
                        // Fetch daily data for each network
                        _f.sent();
                        _i = 0, _a = Object.entries(dailyData);
                        _f.label = 2;
                    case 2:
                        if (!(_i < _a.length)) return [3 /*break*/, 5];
                        _b = _a[_i], dailyCacheKey = _b[0], stats = _b[1];
                        return [4 /*yield*/, this.cacheManager.set(dailyCacheKey, stats)];
                    case 3:
                        _f.sent();
                        _f.label = 4;
                    case 4:
                        _i++;
                        return [3 /*break*/, 2];
                    case 5:
                        _c = 0, _d = Object.entries(monthlyData);
                        _f.label = 6;
                    case 6:
                        if (!(_c < _d.length)) return [3 /*break*/, 9];
                        _e = _d[_c], month = _e[0], stats = _e[1];
                        monthlyCacheKey = "".concat(redis_config_service_1.HMT_PREFIX).concat(month);
                        return [4 /*yield*/, this.cacheManager.set(monthlyCacheKey, stats)];
                    case 7:
                        _f.sent();
                        _f.label = 8;
                    case 8:
                        _c++;
                        return [3 /*break*/, 6];
                    case 9: return [2 /*return*/];
                }
            });
        });
    };
    StatsService.prototype.hmtPrice = function () {
        return __awaiter(this, void 0, void 0, function () {
            var cachedHmtPrice, data, hmtPrice;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0: return [4 /*yield*/, this.cacheManager.get(this.redisConfigService.hmtPriceCacheKey)];
                    case 1:
                        cachedHmtPrice = _a.sent();
                        if (cachedHmtPrice) {
                            return [2 /*return*/, cachedHmtPrice];
                        }
                        return [4 /*yield*/, (0, rxjs_1.lastValueFrom)(this.httpService.get(this.envConfigService.hmtPriceSource, {
                                headers: {
                                    'x-cg-demo-api-key': this.envConfigService.hmtPriceSourceApiKey
                                }
                            }))];
                    case 2:
                        data = (_a.sent()).data;
                        hmtPrice = data[this.envConfigService.hmtPriceFromKey][this.envConfigService.hmtPriceToKey];
                        return [4 /*yield*/, this.cacheManager.set(this.redisConfigService.hmtPriceCacheKey, hmtPrice, { ttl: this.redisConfigService.cacheHmtPriceTTL })];
                    case 3:
                        _a.sent();
                        return [2 /*return*/, hmtPrice];
                }
            });
        });
    };
    StatsService.prototype.hCaptchaStats = function (from, to) {
        return __awaiter(this, void 0, void 0, function () {
            var startDate, endDate, dates, stats;
            var _this = this;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        startDate = dayjs(from);
                        endDate = dayjs(to);
                        dates = [];
                        while (startDate <= endDate) {
                            dates.push(startDate.format('YYYY-MM-DD'));
                            startDate = startDate.add(1, 'day');
                        }
                        return [4 /*yield*/, Promise.all(dates.map(function (date) { return __awaiter(_this, void 0, void 0, function () {
                                var stat;
                                return __generator(this, function (_a) {
                                    switch (_a.label) {
                                        case 0: return [4 /*yield*/, this.cacheManager.get("".concat(redis_config_service_1.HCAPTCHA_PREFIX).concat(date))];
                                        case 1:
                                            stat = _a.sent();
                                            if (stat) {
                                                stat.date = date;
                                            }
                                            return [2 /*return*/, stat];
                                    }
                                });
                            }); }))];
                    case 1:
                        stats = _a.sent();
                        return [2 /*return*/, stats.filter(function (stat) { return stat !== null; })];
                }
            });
        });
    };
    StatsService.prototype.hCaptchaGeneralStats = function () {
        return __awaiter(this, void 0, void 0, function () {
            var startDate, currentDate, dates, stats, aggregatedStats;
            var _this = this;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        startDate = dayjs(env_config_service_2.HCAPTCHA_STATS_START_DATE);
                        currentDate = dayjs();
                        dates = [];
                        while (startDate <= currentDate) {
                            dates.push(startDate.format('YYYY-MM'));
                            startDate = startDate.add(1, 'month');
                        }
                        return [4 /*yield*/, Promise.all(dates.map(function (date) { return __awaiter(_this, void 0, void 0, function () {
                                var stat;
                                return __generator(this, function (_a) {
                                    switch (_a.label) {
                                        case 0: return [4 /*yield*/, this.cacheManager.get("".concat(redis_config_service_1.HCAPTCHA_PREFIX).concat(date))];
                                        case 1:
                                            stat = _a.sent();
                                            return [2 /*return*/, stat];
                                    }
                                });
                            }); }))];
                    case 1:
                        stats = _a.sent();
                        aggregatedStats = stats.reduce(function (acc, stat) {
                            if (stat) {
                                acc.solved += stat.solved;
                            }
                            return acc;
                        });
                        return [2 /*return*/, aggregatedStats];
                }
            });
        });
    };
    StatsService.prototype.hmtGeneralStats = function () {
        return __awaiter(this, void 0, void 0, function () {
            var data;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0: return [4 /*yield*/, this.cacheManager.get(this.redisConfigService.hmtGeneralStatsCacheKey)];
                    case 1:
                        data = _a.sent();
                        return [2 /*return*/, data];
                }
            });
        });
    };
    StatsService.prototype.hmtDailyStats = function (from, to) {
        return __awaiter(this, void 0, void 0, function () {
            var startDate, endDate, dates, stats;
            var _this = this;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        startDate = dayjs(from);
                        endDate = dayjs(to);
                        dates = [];
                        while (startDate <= endDate) {
                            dates.push(startDate.format('YYYY-MM-DD'));
                            startDate = startDate.add(1, 'day');
                        }
                        return [4 /*yield*/, Promise.all(dates.map(function (date) { return __awaiter(_this, void 0, void 0, function () {
                                var stat;
                                return __generator(this, function (_a) {
                                    switch (_a.label) {
                                        case 0: return [4 /*yield*/, this.cacheManager.get("".concat(redis_config_service_1.HMT_PREFIX).concat(date))];
                                        case 1:
                                            stat = _a.sent();
                                            if (stat) {
                                                stat.date = date;
                                            }
                                            return [2 /*return*/, stat];
                                    }
                                });
                            }); }))];
                    case 1:
                        stats = _a.sent();
                        return [2 /*return*/, stats.filter(function (stat) { return stat !== null; })];
                }
            });
        });
    };
    var StatsService_1;
    __decorate([
        (0, schedule_1.Cron)('*/15 * * * *')
    ], StatsService.prototype, "fetchTodayHcaptchaStats");
    __decorate([
        (0, schedule_1.Cron)('*/15 * * * *')
    ], StatsService.prototype, "fetchHmtGeneralStats");
    __decorate([
        (0, schedule_1.Cron)('*/15 * * * *')
    ], StatsService.prototype, "fetchHmtDailyStats");
    StatsService = StatsService_1 = __decorate([
        (0, common_1.Injectable)(),
        __param(0, (0, common_1.Inject)(cache_manager_1.CACHE_MANAGER))
    ], StatsService);
    return StatsService;
}());
exports.StatsService = StatsService;
