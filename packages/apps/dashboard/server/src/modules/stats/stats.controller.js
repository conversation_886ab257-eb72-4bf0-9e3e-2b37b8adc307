"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
exports.__esModule = true;
exports.StatsController = void 0;
var swagger_1 = require("@nestjs/swagger");
var common_1 = require("@nestjs/common");
var hmt_price_dto_1 = require("./dto/hmt-price.dto");
var hcaptcha_dto_1 = require("./dto/hcaptcha.dto");
var hmt_dto_1 = require("./dto/hmt.dto");
var date_validation_pipe_1 = require("../../common/pipes/date-validation.pipe");
var hmt_general_stats_dto_1 = require("./dto/hmt-general-stats.dto");
var StatsController = /** @class */ (function () {
    function StatsController(statsService) {
        this.statsService = statsService;
    }
    StatsController.prototype.hmtPrice = function () {
        return __awaiter(this, void 0, void 0, function () {
            var hmtPrice;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0: return [4 /*yield*/, this.statsService.hmtPrice()];
                    case 1:
                        hmtPrice = _a.sent();
                        return [2 /*return*/, { hmtPrice: hmtPrice }];
                }
            });
        });
    };
    StatsController.prototype.hcaptchaDailyStats = function (from, to) {
        return __awaiter(this, void 0, void 0, function () {
            var results;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0: return [4 /*yield*/, this.statsService.hCaptchaStats(from, to)];
                    case 1:
                        results = _a.sent();
                        return [2 /*return*/, { from: from, to: to, results: results }];
                }
            });
        });
    };
    StatsController.prototype.hcaptchaGeneralStats = function () {
        return __awaiter(this, void 0, void 0, function () {
            var result;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0: return [4 /*yield*/, this.statsService.hCaptchaGeneralStats()];
                    case 1:
                        result = _a.sent();
                        return [2 /*return*/, result];
                }
            });
        });
    };
    StatsController.prototype.hmtGeneral = function () {
        return __awaiter(this, void 0, void 0, function () {
            var results;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0: return [4 /*yield*/, this.statsService.hmtGeneralStats()];
                    case 1:
                        results = _a.sent();
                        return [2 /*return*/, results];
                }
            });
        });
    };
    StatsController.prototype.hmtDailyStats = function (from, to) {
        return __awaiter(this, void 0, void 0, function () {
            var results;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0: return [4 /*yield*/, this.statsService.hmtDailyStats(from, to)];
                    case 1:
                        results = _a.sent();
                        return [2 /*return*/, { from: from, to: to, results: results }];
                }
            });
        });
    };
    __decorate([
        (0, common_1.Get)('/hmt-price'),
        (0, common_1.HttpCode)(200),
        (0, swagger_1.ApiOperation)({
            summary: 'Get current HMT price',
            description: 'Endpoint to return a current HMT price.'
        }),
        (0, swagger_1.ApiResponse)({
            status: 200,
            description: 'Price retrieved successfully',
            type: hmt_price_dto_1.HmtPriceDto
        })
    ], StatsController.prototype, "hmtPrice");
    __decorate([
        (0, common_1.Get)('/hcaptcha/daily'),
        (0, common_1.HttpCode)(200),
        (0, swagger_1.ApiOperation)({
            summary: 'Get Hcaptcha stats',
            description: 'Endpoint to return Hcaptcha stats.'
        }),
        (0, swagger_1.ApiQuery)({
            name: 'from',
            type: String,
            description: 'Start date in the format YYYY-MM-DD',
            required: true
        }),
        (0, swagger_1.ApiQuery)({
            name: 'to',
            type: String,
            description: 'End date in the format YYYY-MM-DD',
            required: true
        }),
        (0, swagger_1.ApiResponse)({
            status: 200,
            description: 'Stats retrieved successfully',
            type: hcaptcha_dto_1.HcaptchaDailyStatsResponseDto
        }),
        __param(0, (0, common_1.Query)('from', date_validation_pipe_1.DateValidationPipe)),
        __param(1, (0, common_1.Query)('to', date_validation_pipe_1.DateValidationPipe))
    ], StatsController.prototype, "hcaptchaDailyStats");
    __decorate([
        (0, common_1.Get)('/hcaptcha/general'),
        (0, common_1.HttpCode)(200),
        (0, swagger_1.ApiOperation)({
            summary: 'Get Hcaptcha general stats',
            description: 'Endpoint to return Hcaptcha general stats.'
        }),
        (0, swagger_1.ApiResponse)({
            status: 200,
            description: 'Stats retrieved successfully',
            type: hcaptcha_dto_1.HcaptchaStats
        })
    ], StatsController.prototype, "hcaptchaGeneralStats");
    __decorate([
        (0, common_1.Get)('/general'),
        (0, common_1.HttpCode)(200),
        (0, swagger_1.ApiOperation)({
            summary: 'Get HMT general stats',
            description: 'Endpoint to return HMT general stats.'
        }),
        (0, swagger_1.ApiResponse)({
            status: 200,
            description: 'General stats retrieved successfully',
            type: hmt_general_stats_dto_1.HmtGeneralStatsDto
        })
    ], StatsController.prototype, "hmtGeneral");
    __decorate([
        (0, common_1.Get)('/hmt/daily'),
        (0, common_1.HttpCode)(200),
        (0, swagger_1.ApiOperation)({
            summary: 'Get HMT stats',
            description: 'Endpoint to return HMT stats.'
        }),
        (0, swagger_1.ApiQuery)({
            name: 'from',
            type: String,
            description: 'Start date in the format YYYY-MM-DD',
            required: true
        }),
        (0, swagger_1.ApiQuery)({
            name: 'to',
            type: String,
            description: 'End date in the format YYYY-MM-DD',
            required: true
        }),
        (0, swagger_1.ApiResponse)({
            status: 200,
            description: 'Stats retrieved successfully',
            type: hmt_dto_1.HmtDailyStatsResponseDto
        }),
        __param(0, (0, common_1.Query)('from', date_validation_pipe_1.DateValidationPipe)),
        __param(1, (0, common_1.Query)('to', date_validation_pipe_1.DateValidationPipe))
    ], StatsController.prototype, "hmtDailyStats");
    StatsController = __decorate([
        (0, swagger_1.ApiTags)('Stats'),
        (0, common_1.Controller)('/stats')
    ], StatsController);
    return StatsController;
}());
exports.StatsController = StatsController;
