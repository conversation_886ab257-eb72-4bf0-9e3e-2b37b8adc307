"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
exports.__esModule = true;
exports.StatsModule = void 0;
var axios_1 = require("@nestjs/axios");
var common_1 = require("@nestjs/common");
var stats_service_1 = require("./stats.service");
var stats_controller_1 = require("./stats.controller");
var storage_module_1 = require("../storage/storage.module");
var StatsModule = /** @class */ (function () {
    function StatsModule() {
    }
    StatsModule = __decorate([
        (0, common_1.Module)({
            imports: [axios_1.HttpModule, storage_module_1.StorageModule],
            controllers: [stats_controller_1.StatsController],
            providers: [stats_service_1.StatsService]
        })
    ], StatsModule);
    return StatsModule;
}());
exports.StatsModule = StatsModule;
