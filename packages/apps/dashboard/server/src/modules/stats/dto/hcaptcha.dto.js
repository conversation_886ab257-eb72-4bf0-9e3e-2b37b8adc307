"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        if (typeof b !== "function" && b !== null)
            throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
exports.__esModule = true;
exports.HcaptchaDailyStatsResponseDto = exports.HcaptchaDailyStats = exports.HcaptchaStats = void 0;
var class_validator_1 = require("class-validator");
var swagger_1 = require("@nestjs/swagger");
var HcaptchaStats = /** @class */ (function () {
    function HcaptchaStats() {
    }
    __decorate([
        (0, swagger_1.ApiProperty)({ example: 1000 }),
        (0, class_validator_1.IsNumber)()
    ], HcaptchaStats.prototype, "solved");
    return HcaptchaStats;
}());
exports.HcaptchaStats = HcaptchaStats;
var HcaptchaDailyStats = /** @class */ (function (_super) {
    __extends(HcaptchaDailyStats, _super);
    function HcaptchaDailyStats() {
        return _super !== null && _super.apply(this, arguments) || this;
    }
    __decorate([
        (0, class_validator_1.IsString)()
    ], HcaptchaDailyStats.prototype, "date");
    return HcaptchaDailyStats;
}(HcaptchaStats));
exports.HcaptchaDailyStats = HcaptchaDailyStats;
var HcaptchaDailyStatsResponseDto = /** @class */ (function () {
    function HcaptchaDailyStatsResponseDto() {
    }
    __decorate([
        (0, swagger_1.ApiProperty)({ example: '2024-05-01' })
    ], HcaptchaDailyStatsResponseDto.prototype, "from");
    __decorate([
        (0, swagger_1.ApiProperty)({ example: '2024-05-01' })
    ], HcaptchaDailyStatsResponseDto.prototype, "to");
    __decorate([
        (0, swagger_1.ApiProperty)({ isArray: true, type: HcaptchaDailyStats })
    ], HcaptchaDailyStatsResponseDto.prototype, "results");
    return HcaptchaDailyStatsResponseDto;
}());
exports.HcaptchaDailyStatsResponseDto = HcaptchaDailyStatsResponseDto;
