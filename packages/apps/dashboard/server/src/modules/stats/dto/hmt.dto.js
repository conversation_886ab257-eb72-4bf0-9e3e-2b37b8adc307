"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
exports.__esModule = true;
exports.HmtDailyStatsResponseDto = exports.HmtDailyStatsData = void 0;
var swagger_1 = require("@nestjs/swagger");
var HmtDailyStatsData = /** @class */ (function () {
    function HmtDailyStatsData() {
    }
    __decorate([
        (0, swagger_1.ApiProperty)({ example: '2024-05-01' })
    ], HmtDailyStatsData.prototype, "date");
    __decorate([
        (0, swagger_1.ApiProperty)()
    ], HmtDailyStatsData.prototype, "totalTransactionAmount");
    __decorate([
        (0, swagger_1.ApiProperty)()
    ], HmtDailyStatsData.prototype, "totalTransactionCount");
    __decorate([
        (0, swagger_1.ApiProperty)()
    ], HmtDailyStatsData.prototype, "dailyUniqueSenders");
    __decorate([
        (0, swagger_1.ApiProperty)()
    ], HmtDailyStatsData.prototype, "dailyUniqueReceivers");
    return HmtDailyStatsData;
}());
exports.HmtDailyStatsData = HmtDailyStatsData;
var HmtDailyStatsResponseDto = /** @class */ (function () {
    function HmtDailyStatsResponseDto() {
    }
    __decorate([
        (0, swagger_1.ApiProperty)({ example: '2024-05-01' })
    ], HmtDailyStatsResponseDto.prototype, "from");
    __decorate([
        (0, swagger_1.ApiProperty)({ example: '2024-05-01' })
    ], HmtDailyStatsResponseDto.prototype, "to");
    __decorate([
        (0, swagger_1.ApiProperty)({ isArray: true, type: HmtDailyStatsData })
    ], HmtDailyStatsResponseDto.prototype, "results");
    return HmtDailyStatsResponseDto;
}());
exports.HmtDailyStatsResponseDto = HmtDailyStatsResponseDto;
