"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
exports.__esModule = true;
exports.AppModule = void 0;
var cache_manager_1 = require("@nestjs/cache-manager");
var config_1 = require("@nestjs/config");
var schedule_1 = require("@nestjs/schedule");
var common_1 = require("@nestjs/common");
var Joi = require("joi");
var app_controller_1 = require("./app.controller");
var cache_factory_config_1 = require("./common/config/cache-factory.config");
var config_module_1 = require("./common/config/config.module");
var details_module_1 = require("./modules/details/details.module");
var stats_module_1 = require("./modules/stats/stats.module");
var AppModule = /** @class */ (function () {
    function AppModule() {
    }
    AppModule = __decorate([
        (0, common_1.Module)({
            imports: [
                config_1.ConfigModule.forRoot({
                    envFilePath: process.env.NODE_ENV
                        ? ".env.".concat(process.env.NODE_ENV)
                        : '.env',
                    isGlobal: true,
                    validationSchema: Joi.object({
                        HOST: Joi.string().required(),
                        PORT: Joi.number().port()["default"](3000),
                        SUBGRAPH_API_KEY: Joi.string().required(),
                        HCAPTCHA_API_KEY: Joi.string().required()
                    })
                }),
                cache_manager_1.CacheModule.registerAsync(cache_factory_config_1.CacheFactoryConfig),
                config_module_1.CommonConfigModule,
                details_module_1.DetailsModule,
                schedule_1.ScheduleModule.forRoot(),
                stats_module_1.StatsModule,
            ],
            controllers: [app_controller_1.AppController]
        })
    ], AppModule);
    return AppModule;
}());
exports.AppModule = AppModule;
