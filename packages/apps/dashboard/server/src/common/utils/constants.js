"use strict";
exports.__esModule = true;
exports.MAINNET_CHAIN_IDS = exports.TESTNET_CHAIN_IDS = exports.LOCALHOST_CHAIN_IDS = exports.MainnetsId = void 0;
var sdk_1 = require("@human-protocol/sdk");
var MainnetsId;
(function (MainnetsId) {
    MainnetsId[MainnetsId["MAINNET"] = sdk_1.ChainId.MAINNET] = "MAINNET";
    MainnetsId[MainnetsId["BSC_MAINNET"] = sdk_1.ChainId.BSC_MAINNET] = "BSC_MAINNET";
    MainnetsId[MainnetsId["POLYGON"] = sdk_1.ChainId.POLYGON] = "POLYGON";
    MainnetsId[MainnetsId["MOONBEAM"] = sdk_1.ChainId.MOONBEAM] = "MOONBEAM";
    MainnetsId[MainnetsId["AVALANCHE"] = sdk_1.ChainId.AVALANCHE] = "AVALANCHE";
    MainnetsId[MainnetsId["CELO"] = sdk_1.ChainId.CELO] = "CELO";
    MainnetsId[MainnetsId["XLAYER"] = sdk_1.ChainId.XLAYER] = "XLAYER";
})(MainnetsId = exports.MainnetsId || (exports.MainnetsId = {}));
exports.LOCALHOST_CHAIN_IDS = [sdk_1.ChainId.LOCALHOST];
exports.TESTNET_CHAIN_IDS = [
    sdk_1.ChainId.BSC_TESTNET,
    sdk_1.ChainId.POLYGON_AMOY,
    sdk_1.ChainId.SEPOLIA,
    sdk_1.ChainId.XLAYER_TESTNET,
];
exports.MAINNET_CHAIN_IDS = [
    sdk_1.ChainId.BSC_MAINNET,
    sdk_1.ChainId.POLYGON,
    sdk_1.ChainId.MOONBEAM,
];
