"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
exports.__esModule = true;
exports.EnvironmentConfigService = exports.HMT_STATS_START_DATE = exports.HCAPTCHA_STATS_API_START_DATE = exports.HCAPTCHA_STATS_START_DATE = void 0;
var common_1 = require("@nestjs/common");
var DEFAULT_CORS_ALLOWED_ORIGIN = 'http://localhost:3001';
var DEFAULT_CORS_ALLOWED_HEADERS = 'Content-Type, Accept';
var DEFAULT_HMT_PRICE_SOURCE = 'https://api.coingecko.com/api/v3/simple/price?ids=human-protocol&vs_currencies=usd';
var DEFAULT_HMT_PRICE_FROM = 'human-protocol';
var DEFAULT_HMT_PRICE_TO = 'usd';
var DEFAULT_HCAPTCHA_STATS_SOURCE = 'https://foundation-accounts.hmt.ai/support/summary-stats';
var DEFAULT_HCAPTCHA_STATS_FILE = 'hcaptchaStats.json';
exports.HCAPTCHA_STATS_START_DATE = '2022-07-01';
exports.HCAPTCHA_STATS_API_START_DATE = '2024-09-14';
exports.HMT_STATS_START_DATE = '2021-04-06';
var EnvironmentConfigService = /** @class */ (function () {
    function EnvironmentConfigService(configService) {
        this.configService = configService;
    }
    Object.defineProperty(EnvironmentConfigService.prototype, "host", {
        get: function () {
            return this.configService.getOrThrow('HOST');
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(EnvironmentConfigService.prototype, "port", {
        get: function () {
            return +this.configService.getOrThrow('PORT');
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(EnvironmentConfigService.prototype, "isCorsEnabled", {
        get: function () {
            return this.configService.get('CORS_ENABLED', false);
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(EnvironmentConfigService.prototype, "corsEnabledOrigin", {
        get: function () {
            return this.configService.get('CORS_ALLOWED_ORIGIN', DEFAULT_CORS_ALLOWED_ORIGIN);
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(EnvironmentConfigService.prototype, "corsAllowedHeaders", {
        get: function () {
            return this.configService.get('CORS_ALLOWED_HEADERS', DEFAULT_CORS_ALLOWED_HEADERS);
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(EnvironmentConfigService.prototype, "subgraphApiKey", {
        get: function () {
            return this.configService.getOrThrow('SUBGRAPH_API_KEY');
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(EnvironmentConfigService.prototype, "hmtPriceSource", {
        get: function () {
            return this.configService.get('HMT_PRICE_SOURCE', DEFAULT_HMT_PRICE_SOURCE);
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(EnvironmentConfigService.prototype, "hmtPriceSourceApiKey", {
        get: function () {
            return this.configService.getOrThrow('HMT_PRICE_SOURCE_API_KEY');
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(EnvironmentConfigService.prototype, "hmtPriceFromKey", {
        get: function () {
            return this.configService.get('HMT_PRICE_FROM', DEFAULT_HMT_PRICE_FROM);
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(EnvironmentConfigService.prototype, "hmtPriceToKey", {
        get: function () {
            return this.configService.get('HMT_PRICE_TO', DEFAULT_HMT_PRICE_TO);
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(EnvironmentConfigService.prototype, "hCaptchaApiKey", {
        get: function () {
            return this.configService.getOrThrow('HCAPTCHA_API_KEY');
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(EnvironmentConfigService.prototype, "hCaptchaStatsSource", {
        get: function () {
            return this.configService.get('HCAPTCHA_STATS_SOURCE', DEFAULT_HCAPTCHA_STATS_SOURCE);
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(EnvironmentConfigService.prototype, "hCaptchaStatsFile", {
        get: function () {
            return this.configService.get('HCAPTCHA_STATS_FILE', DEFAULT_HCAPTCHA_STATS_FILE);
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(EnvironmentConfigService.prototype, "reputationSource", {
        get: function () {
            return this.configService.getOrThrow('REPUTATION_SOURCE_URL');
        },
        enumerable: false,
        configurable: true
    });
    EnvironmentConfigService = __decorate([
        (0, common_1.Injectable)()
    ], EnvironmentConfigService);
    return EnvironmentConfigService;
}());
exports.EnvironmentConfigService = EnvironmentConfigService;
