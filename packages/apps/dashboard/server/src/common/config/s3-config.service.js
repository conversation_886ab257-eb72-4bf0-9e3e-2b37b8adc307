"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
exports.__esModule = true;
exports.S3ConfigService = void 0;
var common_1 = require("@nestjs/common");
var S3ConfigService = /** @class */ (function () {
    function S3ConfigService(configService) {
        this.configService = configService;
    }
    Object.defineProperty(S3ConfigService.prototype, "endpoint", {
        get: function () {
            return this.configService.get('S3_ENDPOINT', '127.0.0.1');
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(S3ConfigService.prototype, "port", {
        get: function () {
            return +this.configService.get('S3_PORT', 9000);
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(S3ConfigService.prototype, "accessKey", {
        get: function () {
            return this.configService.get('S3_ACCESS_KEY', '');
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(S3ConfigService.prototype, "secretKey", {
        get: function () {
            return this.configService.get('S3_SECRET_KEY', '');
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(S3ConfigService.prototype, "bucket", {
        get: function () {
            return this.configService.get('S3_BUCKET', 'exchange');
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(S3ConfigService.prototype, "useSSL", {
        get: function () {
            return this.configService.get('S3_USE_SSL', 'false') === 'true';
        },
        enumerable: false,
        configurable: true
    });
    S3ConfigService = __decorate([
        (0, common_1.Injectable)()
    ], S3ConfigService);
    return S3ConfigService;
}());
exports.S3ConfigService = S3ConfigService;
