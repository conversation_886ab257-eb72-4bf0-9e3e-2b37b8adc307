"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
exports.__esModule = true;
exports.RedisConfigService = exports.HMT_PREFIX = exports.HCAPTCHA_PREFIX = void 0;
var common_1 = require("@nestjs/common");
var DEFAULT_REDIS_HOST = 'localhost';
var DEFAULT_REDIS_PORT = 6379;
var DEFAULT_CACHE_HMT_PRICE_TTL = 60;
var DEFAULT_CACHE_HMT_GENERAL_STATS_TTL = 2 * 60;
var DEFAULT_HMT_PRICE_CACHE_KEY = 'hmt-price';
var DEFAULT_HMT_GENERAL_STATS_CACHE_KEY = 'hmt-general';
exports.HCAPTCHA_PREFIX = 'hcaptcha-';
exports.HMT_PREFIX = 'hmt-';
var RedisConfigService = /** @class */ (function () {
    function RedisConfigService(configService) {
        this.configService = configService;
    }
    Object.defineProperty(RedisConfigService.prototype, "redisHost", {
        get: function () {
            return this.configService.get('REDIS_HOST', DEFAULT_REDIS_HOST);
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(RedisConfigService.prototype, "redisPort", {
        get: function () {
            return +this.configService.get('REDIS_PORT', DEFAULT_REDIS_PORT);
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(RedisConfigService.prototype, "cacheHmtPriceTTL", {
        get: function () {
            return +this.configService.get('CACHE_HMT_PRICE_TTL', DEFAULT_CACHE_HMT_PRICE_TTL);
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(RedisConfigService.prototype, "cacheHmtGeneralStatsTTL", {
        get: function () {
            return +this.configService.get('CACHE_HMT_GENERAL_STATS_TTL', DEFAULT_CACHE_HMT_GENERAL_STATS_TTL);
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(RedisConfigService.prototype, "hmtPriceCacheKey", {
        get: function () {
            return this.configService.get('HMT_PRICE_CACHE_KEY', DEFAULT_HMT_PRICE_CACHE_KEY);
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(RedisConfigService.prototype, "hmtGeneralStatsCacheKey", {
        get: function () {
            return this.configService.get('HMT_GENERAL_STATS_CACHE_KEY', DEFAULT_HMT_GENERAL_STATS_CACHE_KEY);
        },
        enumerable: false,
        configurable: true
    });
    RedisConfigService = __decorate([
        (0, common_1.Injectable)()
    ], RedisConfigService);
    return RedisConfigService;
}());
exports.RedisConfigService = RedisConfigService;
