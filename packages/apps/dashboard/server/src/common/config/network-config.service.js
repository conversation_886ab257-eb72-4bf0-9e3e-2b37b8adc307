"use strict";
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
exports.__esModule = true;
exports.NetworkConfigService = void 0;
var sdk_1 = require("@human-protocol/sdk");
var common_1 = require("@nestjs/common");
var web3_1 = require("../enums/web3");
var constants_1 = require("../utils/constants");
var NetworkConfigService = /** @class */ (function () {
    function NetworkConfigService(configService) {
        var _this = this;
        this.configService = configService;
        this.networkMap = __assign(__assign(__assign(__assign(__assign(__assign(__assign(__assign(__assign({}, (this.configService.get('RPC_URL_SEPOLIA') && {
            sepolia: {
                chainId: sdk_1.ChainId.SEPOLIA,
                rpcUrl: this.configService.get('RPC_URL_SEPOLIA')
            }
        })), (this.configService.get('RPC_URL_POLYGON') && {
            polygon: {
                chainId: sdk_1.ChainId.POLYGON,
                rpcUrl: this.configService.get('RPC_URL_POLYGON')
            }
        })), (this.configService.get('RPC_URL_POLYGON_AMOY') && {
            amoy: {
                chainId: sdk_1.ChainId.POLYGON_AMOY,
                rpcUrl: this.configService.get('RPC_URL_POLYGON_AMOY')
            }
        })), (this.configService.get('RPC_URL_BSC_MAINNET') && {
            bsc: {
                chainId: sdk_1.ChainId.BSC_MAINNET,
                rpcUrl: this.configService.get('RPC_URL_BSC_MAINNET')
            }
        })), (this.configService.get('RPC_URL_BSC_TESTNET') && {
            bsctest: {
                chainId: sdk_1.ChainId.BSC_TESTNET,
                rpcUrl: this.configService.get('RPC_URL_BSC_TESTNET')
            }
        })), (this.configService.get('RPC_URL_MOONBEAM') && {
            moonbeam: {
                chainId: sdk_1.ChainId.MOONBEAM,
                rpcUrl: this.configService.get('RPC_URL_MOONBEAM')
            }
        })), (this.configService.get('RPC_URL_XLAYER_TESTNET') && {
            xlayertestnet: {
                chainId: sdk_1.ChainId.XLAYER_TESTNET,
                rpcUrl: this.configService.get('RPC_URL_XLAYER_TESTNET')
            }
        })), (this.configService.get('RPC_URL_XLAYER') && {
            xlayer: {
                chainId: sdk_1.ChainId.XLAYER,
                rpcUrl: this.configService.get('RPC_URL_XLAYER')
            }
        })), (this.configService.get('RPC_URL_LOCALHOST') && {
            localhost: {
                chainId: sdk_1.ChainId.LOCALHOST,
                rpcUrl: this.configService.get('RPC_URL_LOCALHOST')
            }
        }));
        var validChainIds = (function () {
            switch (_this.configService.get('WEB3_ENV')) {
                case web3_1.Web3Env.MAINNET:
                    return constants_1.MAINNET_CHAIN_IDS;
                case web3_1.Web3Env.LOCALHOST:
                    return constants_1.LOCALHOST_CHAIN_IDS;
                default:
                    return constants_1.TESTNET_CHAIN_IDS;
            }
        })();
        // Remove networks without RPC URLs
        this.networkMap = Object.keys(this.networkMap)
            .filter(function (network) {
            var networkConfig = _this.networkMap[network];
            return (networkConfig.rpcUrl && validChainIds.includes(networkConfig.chainId));
        })
            .reduce(function (newNetworkMap, network) {
            newNetworkMap[network] = _this.networkMap[network];
            return newNetworkMap;
        }, {});
    }
    Object.defineProperty(NetworkConfigService.prototype, "networks", {
        get: function () {
            return Object.values(this.networkMap).map(function (network) { return network; });
        },
        enumerable: false,
        configurable: true
    });
    NetworkConfigService = __decorate([
        (0, common_1.Injectable)()
    ], NetworkConfigService);
    return NetworkConfigService;
}());
exports.NetworkConfigService = NetworkConfigService;
