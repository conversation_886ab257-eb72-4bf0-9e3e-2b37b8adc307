"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
exports.__esModule = true;
exports.CommonConfigModule = void 0;
var common_1 = require("@nestjs/common");
var env_config_service_1 = require("./env-config.service");
var redis_config_service_1 = require("./redis-config.service");
var network_config_service_1 = require("./network-config.service");
var s3_config_service_1 = require("./s3-config.service");
var CommonConfigModule = /** @class */ (function () {
    function CommonConfigModule() {
    }
    CommonConfigModule = __decorate([
        (0, common_1.Global)(),
        (0, common_1.Module)({
            providers: [
                redis_config_service_1.RedisConfigService,
                env_config_service_1.EnvironmentConfigService,
                network_config_service_1.NetworkConfigService,
                s3_config_service_1.S3ConfigService,
            ],
            exports: [
                redis_config_service_1.RedisConfigService,
                env_config_service_1.EnvironmentConfigService,
                network_config_service_1.NetworkConfigService,
                s3_config_service_1.S3ConfigService,
            ]
        })
    ], CommonConfigModule);
    return CommonConfigModule;
}());
exports.CommonConfigModule = CommonConfigModule;
