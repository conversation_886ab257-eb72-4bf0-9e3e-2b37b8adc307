"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
exports.__esModule = true;
exports.DateValidationPipe = void 0;
var common_1 = require("@nestjs/common");
var DateValidationPipe = /** @class */ (function () {
    function DateValidationPipe() {
        this.dateRegex = /^\d{4}-\d{2}-\d{2}$/;
    }
    DateValidationPipe.prototype.transform = function (value) {
        if (typeof value === 'string' && !this.dateRegex.test(value)) {
            throw new common_1.BadRequestException("Validation failed. Value must be in the format YYYY-MM-DD.");
        }
        return value;
    };
    DateValidationPipe = __decorate([
        (0, common_1.Injectable)()
    ], DateValidationPipe);
    return DateValidationPipe;
}());
exports.DateValidationPipe = DateValidationPipe;
