"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
exports.__esModule = true;
exports.AddressValidationPipe = void 0;
var common_1 = require("@nestjs/common");
var ethers_1 = require("ethers");
var AddressValidationPipe = /** @class */ (function () {
    function AddressValidationPipe() {
    }
    AddressValidationPipe.prototype.transform = function (value) {
        if (!ethers_1.ethers.isAddress(value)) {
            throw new common_1.BadRequestException('Invalid address');
        }
        return value;
    };
    AddressValidationPipe = __decorate([
        (0, common_1.Injectable)()
    ], AddressValidationPipe);
    return AddressValidationPipe;
}());
exports.AddressValidationPipe = AddressValidationPipe;
