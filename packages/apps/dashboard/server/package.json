{"name": "@human-protocol/dashboard-server", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix"}, "dependencies": {"@human-protocol/sdk": "*", "@nestjs/axios": "^3.0.2", "@nestjs/cache-manager": "^2.2.2", "@nestjs/common": "^10.2.7", "@nestjs/config": "^3.2.3", "@nestjs/core": "^10.2.8", "@nestjs/mapped-types": "*", "@nestjs/platform-express": "^10.3.10", "cache-manager-redis-store": "^3.0.1", "dayjs": "^1.11.12", "reflect-metadata": "^0.2.2", "rxjs": "^7.2.0"}, "devDependencies": {"@nestjs/cli": "^10.3.2", "@nestjs/schematics": "^10.1.3", "@nestjs/testing": "^9.4.3", "@types/express": "^4.17.13", "@types/jest": "29.5.1", "@types/node": "22.5.4", "@types/supertest": "^2.0.11", "@typescript-eslint/eslint-plugin": "^5.0.0", "@typescript-eslint/parser": "^5.0.0", "eslint": "^8.0.1", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.2.1", "jest": "29.5.0", "prettier": "^2.3.2", "source-map-support": "^0.5.20", "supertest": "^7.0.0", "ts-jest": "29.2.2", "ts-loader": "^9.2.3", "ts-node": "^10.0.0", "tsconfig-paths": "4.2.0", "typescript": "^5.0.0"}, "lint-staged": {"*.ts": ["prettier --write", "eslint --fix"]}}