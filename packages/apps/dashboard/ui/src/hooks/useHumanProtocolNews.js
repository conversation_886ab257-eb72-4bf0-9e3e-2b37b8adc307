"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.useHumanProtocolNews = void 0;
const axios_1 = __importDefault(require("axios"));
const react_1 = require("react");
const useHumanProtocolNews = () => {
    const [isLoading, setIsLoading] = (0, react_1.useState)(false);
    const [data, setData] = (0, react_1.useState)({});
    (0, react_1.useEffect)(() => {
        (async () => {
            setIsLoading(true);
            const apiUrl = import.meta.env.VITE_APP_ADMIN_API_URL;
            const apiToken = import.meta.env.VITE_APP_BANNER_API_TOKEN;
            try {
                const url = `${apiUrl}/news-item?populate=*`;
                const { data } = await axios_1.default.get(url, {
                    headers: {
                        Authorization: `Bearer ${apiToken}`,
                    },
                });
                setData({
                    title: data?.data?.attributes?.title,
                    description: data?.data?.attributes?.description,
                    link: data?.data?.attributes?.link,
                    image: data?.data?.attributes?.image?.data?.attributes?.url,
                });
            }
            catch (err) {
                // eslint-disable-next-line no-console
                console.log(err);
            }
            setIsLoading(false);
        })();
    }, []);
    return { isLoading, data };
};
exports.useHumanProtocolNews = useHumanProtocolNews;
