"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.useHumanAppData = void 0;
const swr_1 = __importDefault(require("swr"));
function useHumanAppData(chainId) {
    return (0, swr_1.default)(`human-protocol-dashboard-human-app-data-${chainId}`, async () => {
        const apiURL = import.meta.env.VITE_APP_ADMIN_API_URL;
        const responses = await Promise.all([
            fetch(`${apiURL}/network-data-items?filters[chainId][$eq]=${chainId}`).then((res) => res.json()),
            fetch(`${apiURL}/daily-task-summaries?pagination[limit]=-1`).then((res) => res.json()),
        ]);
        return responses;
    });
}
exports.useHumanAppData = useHumanAppData;
