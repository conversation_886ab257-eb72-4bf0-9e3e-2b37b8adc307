"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.useSlowRefreshEffect = exports.useFastRefreshEffect = void 0;
const react_1 = require("react");
const swr_1 = __importDefault(require("swr"));
const constants_1 = require("src/constants");
const EMPTY_ARRAY = [];
function useFastRefreshEffect(effect, deps) {
    // TODO: Handle multiple networks
    const chainId = 1;
    const { data = 0 } = (0, swr_1.default)(chainId && [constants_1.FAST_INTERVAL, 'blockNumber', chainId]);
    // eslint-disable-next-line react-hooks/exhaustive-deps
    (0, react_1.useEffect)(effect.bind(null, data), [data, ...(deps || EMPTY_ARRAY)]);
}
exports.useFastRefreshEffect = useFastRefreshEffect;
function useSlowRefreshEffect(effect, deps) {
    // TODO: Handle multiple networks
    const chainId = 1;
    const { data = 0 } = (0, swr_1.default)(chainId && [constants_1.SLOW_INTERVAL, 'blockNumber', chainId]);
    // eslint-disable-next-line react-hooks/exhaustive-deps
    (0, react_1.useEffect)(effect.bind(null, data), [data, ...(deps || EMPTY_ARRAY)]);
}
exports.useSlowRefreshEffect = useSlowRefreshEffect;
