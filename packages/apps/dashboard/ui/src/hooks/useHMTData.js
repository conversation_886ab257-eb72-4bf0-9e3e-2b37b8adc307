"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.useHMTData = void 0;
const react_1 = require("react");
const useHMTData = () => {
    const [data, setData] = (0, react_1.useState)();
    (0, react_1.useEffect)(() => {
        fetch(`https://api.coingecko.com/api/v3/coins/ethereum/contract/******************************************`)
            .then((res) => res.json())
            .then((json) => {
            const { market_data } = json;
            setData({
                circulatingSupply: market_data.circulating_supply,
                totalSupply: market_data.total_supply,
                currentPriceInUSD: market_data.current_price.usd,
                priceChangePercentage24h: market_data.price_change_percentage_24h,
            });
        });
    }, []);
    return data;
};
exports.useHMTData = useHMTData;
