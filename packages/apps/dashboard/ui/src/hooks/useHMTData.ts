import { useEffect, useState } from 'react';

type HMTData = {
  circulatingSupply: number;
  totalSupply: number;
  currentPriceInUSD: number;
  priceChangePercentage24h: number;
};

export const useHMTData = () => {
  const [data, setData] = useState<HMTData>();

  useEffect(() => {
    fetch(
      `https://api.coingecko.com/api/v3/coins/ethereum/contract/******************************************`
    )
      .then((res) => res.json())
      .then((json) => {
        const { market_data } = json;
        setData({
          circulatingSupply: market_data.circulating_supply,
          totalSupply: market_data.total_supply,
          currentPriceInUSD: market_data.current_price.usd,
          priceChangePercentage24h: market_data.price_change_percentage_24h,
        });
      });
  }, []);

  return data;
};
