"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.useWorkerStats = void 0;
const sdk_1 = require("@human-protocol/sdk");
const axios_1 = __importDefault(require("axios"));
const dayjs_1 = __importDefault(require("dayjs"));
const swr_1 = __importDefault(require("swr"));
const hooks_1 = require("src/state/humanAppData/hooks");
function useWorkerStats() {
    const chainId = (0, hooks_1.useChainId)();
    return (0, swr_1.default)(`human-protocol-dashboard-worker-stats-${chainId}`, async () => {
        if (chainId !== sdk_1.ChainId.POLYGON && chainId !== sdk_1.ChainId.ALL)
            return null;
        const apiURL = import.meta.env.VITE_APP_ADMIN_API_URL;
        const to = (0, dayjs_1.default)().format('YYYY-MM-DD');
        const from = (0, dayjs_1.default)().subtract(60, 'days').format('YYYY-MM-DD');
        const { data } = await axios_1.default.get(`${apiURL}/stats/workers?to=${to}&from=${from}`);
        return data;
    });
}
exports.useWorkerStats = useWorkerStats;
