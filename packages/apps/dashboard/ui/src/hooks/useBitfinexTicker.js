"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.useBitfinexTicker = void 0;
const axios_1 = __importDefault(require("axios"));
const react_1 = require("react");
const useBitfinexTicker = () => {
    const [ticker, setTicker] = (0, react_1.useState)();
    const url = 'https://api.allorigins.win/get?url=https://api-pub.bitfinex.com/v2/ticker/tHMTUSD';
    (0, react_1.useEffect)(() => {
        axios_1.default.get(url).then((res) => {
            const response = JSON.parse(res.data.contents);
            setTicker({
                bid: response[0],
                bidSize: response[1],
                ask: response[2],
                askSize: response[3],
                dailyChange: response[4],
                dailyChangeRelative: response[5],
                lastPrice: response[6],
                volume: response[7],
                high: response[8],
                low: response[6],
            });
        });
    }, []);
    return ticker;
};
exports.useBitfinexTicker = useBitfinexTicker;
