"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.useHMTStats = void 0;
const HMToken_json_1 = __importDefault(require("@human-protocol/core/abis/HMToken.json"));
const sdk_1 = require("@human-protocol/sdk");
const bignumber_js_1 = __importDefault(require("bignumber.js"));
const ethers_1 = require("ethers");
const swr_1 = __importDefault(require("swr"));
const constants_1 = require("src/constants");
const hooks_1 = require("src/state/humanAppData/hooks");
const fetchTotalSupply = async (chainId) => {
    const rpcUrl = constants_1.RPC_URLS[chainId];
    const hmtAddress = sdk_1.NETWORKS[chainId]?.hmtAddress;
    const provider = new ethers_1.providers.JsonRpcProvider(rpcUrl);
    const contract = new ethers_1.Contract(hmtAddress, HMToken_json_1.default, provider);
    const totalSupplyBN = await contract.totalSupply();
    return new bignumber_js_1.default(ethers_1.utils.formatUnits(totalSupplyBN, constants_1.HM_TOKEN_DECIMALS)).toJSON();
};
function useHMTStats() {
    const chainId = (0, hooks_1.useChainId)();
    return (0, swr_1.default)(`human-protocol-dashboard-hmt-stats-${chainId}`, async () => {
        const network = sdk_1.NETWORKS[chainId];
        if (!network)
            return null;
        const client = new sdk_1.StatisticsClient(network);
        const stats = await client.getHMTStatistics();
        const totalSupply = await fetchTotalSupply(chainId);
        return {
            dailyHMTData: stats.dailyHMTData,
            totalTransferAmount: ethers_1.utils.formatUnits(stats.totalTransferAmount, constants_1.HM_TOKEN_DECIMALS),
            totalTransferCount: stats.totalTransferCount,
            holders: stats.totalHolders,
            totalSupply,
        };
    });
}
exports.useHMTStats = useHMTStats;
