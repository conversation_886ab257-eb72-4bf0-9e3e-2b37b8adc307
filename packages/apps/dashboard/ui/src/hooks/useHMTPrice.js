"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.useHMTPrice = void 0;
const react_1 = require("react");
const useHMTPrice = () => {
    const [price, setPrice] = (0, react_1.useState)();
    (0, react_1.useEffect)(() => {
        if (!price) {
            fetch(`https://api.coingecko.com/api/v3/simple/price?ids=human-protocol&vs_currencies=usd`)
                .then((res) => res.json())
                .then((json) => setPrice(json['human-protocol'].usd));
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);
    return price;
};
exports.useHMTPrice = useHMTPrice;
