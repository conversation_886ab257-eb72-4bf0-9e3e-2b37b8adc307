"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.usePaymentStats = void 0;
const sdk_1 = require("@human-protocol/sdk");
const ethers_1 = require("ethers");
const swr_1 = __importDefault(require("swr"));
const constants_1 = require("src/constants");
const hooks_1 = require("src/state/humanAppData/hooks");
function usePaymentStats() {
    const chainId = (0, hooks_1.useChainId)();
    return (0, swr_1.default)(`human-protocol-dashboard-payment-stats-${chainId}`, async () => {
        const network = sdk_1.NETWORKS[chainId];
        if (!network)
            return null;
        const client = new sdk_1.StatisticsClient(network);
        const stats = await client.getPaymentStatistics();
        return {
            dailyPaymentsData: stats.dailyPaymentsData.map((d) => ({
                ...d,
                totalAmountPaid: ethers_1.utils.formatUnits(d.totalAmountPaid, constants_1.HM_TOKEN_DECIMALS),
                averageAmountPerWorker: ethers_1.utils.formatUnits(d.averageAmountPerWorker, constants_1.HM_TOKEN_DECIMALS),
                totalCount: d.totalCount,
            })),
        };
    });
}
exports.usePaymentStats = usePaymentStats;
