"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const sdk_1 = require("@human-protocol/sdk");
const wagmi_1 = require("wagmi");
const useHMTPrice_1 = require("./useHMTPrice");
function useWalletBalance() {
    const { address } = (0, wagmi_1.useAccount)();
    const chainId = (0, wagmi_1.useChainId)();
    const { data: balance } = (0, wagmi_1.useBalance)({
        address,
        chainId,
        token: sdk_1.NETWORKS[chainId]?.hmtAddress,
    });
    const price = (0, useHMTPrice_1.useHMTPrice)();
    return { ...balance, usdPrice: price };
}
exports.default = useWalletBalance;
