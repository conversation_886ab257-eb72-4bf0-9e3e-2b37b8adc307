"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.useLeaderboardData = void 0;
const Staking_json_1 = __importDefault(require("@human-protocol/core/abis/Staking.json"));
const ethers_1 = require("ethers");
const react_1 = require("react");
const constants_1 = require("src/constants");
const useLeaderboardData = () => {
    const [stakers, setStakers] = (0, react_1.useState)();
    // TODO: Refactor this to read data from the connected chain
    const fetchListOfStakers = async () => {
        const rpcUrl = import.meta.env.VITE_APP_RPC_URL_POLYGON_MUMBAI;
        const provider = new ethers_1.ethers.JsonRpcProvider(rpcUrl);
        const contract = new ethers_1.ethers.Contract(constants_1.STAKING_CONTRACT_ADDRESS, Staking_json_1.default, provider);
        const listOfStakersByRoles = await Promise.all([
            contract.getListOfStakers(1),
            contract.getListOfStakers(2),
            contract.getListOfStakers(3),
            contract.getListOfStakers(4),
            contract.getListOfStakers(5),
        ]);
        const allStakers = listOfStakersByRoles
            .map((listOfStakers) => {
            const stakerAddresses = listOfStakers[0];
            const stakerInfos = listOfStakers[1];
            return stakerAddresses.map((address, i) => ({
                address,
                role: stakerInfos[i].role,
                tokensAllocated: stakerInfos[i].tokensAllocated,
                tokensLocked: stakerInfos[i].tokensLocked,
                tokensLockedUntil: stakerInfos[i].tokensLockedUntil,
                tokensStaked: stakerInfos[i].tokensStaked,
            }));
        })
            .flat();
        setStakers(allStakers);
    };
    (0, react_1.useEffect)(() => {
        fetchListOfStakers();
    }, []);
    return stakers;
};
exports.useLeaderboardData = useLeaderboardData;
