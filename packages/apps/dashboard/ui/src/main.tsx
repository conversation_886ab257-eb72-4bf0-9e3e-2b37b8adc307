import React from 'react';
import { createRoot } from 'react-dom/client';
import TagManager from 'react-gtm-module';
import { Provider } from 'react-redux';
import './index.css';
import { App } from './components';
import reportWebVitals from './reportWebVitals';
import { store } from './state';

import 'react-loading-skeleton/dist/skeleton.css';

TagManager.initialize({
  gtmId: 'G-GQBK13YSRS',
});

const container = document.getElementById('root');

const root = createRoot(container!);

root.render(
  <React.StrictMode>
    <Provider store={store}>
      <App />
    </Provider>
  </React.StrictMode>
);

// If you want to start measuring performance in your app, pass a function
// to log results (for example: reportWebVitals(console.log))
// or send to an analytics endpoint. Learn more: https://bit.ly/CRA-vitals
reportWebVitals();
