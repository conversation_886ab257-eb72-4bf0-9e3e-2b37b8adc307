"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.useNotification = exports.NotificationProvider = void 0;
const material_1 = require("@mui/material");
const react_1 = require("react");
const NotificationContext = (0, react_1.createContext)({});
const NotificationProvider = ({ children }) => {
    const [title, setTitle] = (0, react_1.useState)('');
    const [content, setContent] = (0, react_1.useState)('');
    const [severity, setSeverity] = (0, react_1.useState)('info');
    const showMessage = (title, content, severity = 'info') => {
        setTitle(title);
        setContent(content);
        setSeverity(severity);
    };
    const hideMessage = () => {
        setTitle('');
        setContent('');
        setSeverity('info');
    };
    return (<NotificationContext.Provider value={{ showMessage, hideMessage }}>
      <material_1.Snackbar open={!!title.length} anchorOrigin={{ vertical: 'top', horizontal: 'right' }} autoHideDuration={10000} onClose={hideMessage}>
        <material_1.Alert variant="filled" severity={severity} sx={{ width: '100%' }} onClose={hideMessage}>
          <material_1.AlertTitle>{title}</material_1.AlertTitle>
          {content}
        </material_1.Alert>
      </material_1.Snackbar>
      {children}
    </NotificationContext.Provider>);
};
exports.NotificationProvider = NotificationProvider;
const useNotification = () => {
    const context = (0, react_1.useContext)(NotificationContext);
    if (!context) {
        throw new Error('useNotification must be used within an NotificationProvider');
    }
    return context;
};
exports.useNotification = useNotification;
