"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.QueryClientProvider = void 0;
const query_sync_storage_persister_1 = require("@tanstack/query-sync-storage-persister");
const react_query_1 = require("@tanstack/react-query");
const react_query_persist_client_1 = require("@tanstack/react-query-persist-client");
const wagmi_1 = require("wagmi");
const queryClient = new react_query_1.QueryClient({
    defaultOptions: {
        queries: {
            gcTime: 1000 * 60 * 60 * 24, // 24 hours
        },
    },
});
const persister = (0, query_sync_storage_persister_1.createSyncStoragePersister)({
    serialize: wagmi_1.serialize,
    storage: window.localStorage,
    deserialize: wagmi_1.deserialize,
});
const QueryClientProvider = ({ children }) => {
    return (<react_query_persist_client_1.PersistQueryClientProvider client={queryClient} persistOptions={{ persister }}>
      {children}
    </react_query_persist_client_1.PersistQueryClientProvider>);
};
exports.QueryClientProvider = QueryClientProvider;
