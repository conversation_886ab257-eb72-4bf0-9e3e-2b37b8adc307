"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.WagmiProvider = exports.wagmiConfig = void 0;
const sdk_1 = require("@human-protocol/sdk");
const wagmi_1 = require("wagmi");
const wagmiChains = __importStar(require("wagmi/chains"));
const connectors_1 = require("wagmi/connectors");
const constants_1 = require("../constants");
const projectId = import.meta.env.VITE_APP_WALLETCONNECT_PROJECT_ID;
exports.wagmiConfig = (0, wagmi_1.createConfig)({
    chains: [
        wagmiChains.mainnet,
        wagmiChains.sepolia,
        wagmiChains.bsc,
        wagmiChains.bscTestnet,
        wagmiChains.polygon,
        wagmiChains.polygonAmoy,
        wagmiChains.moonbeam,
        wagmiChains.moonbaseAlpha,
        wagmiChains.avalancheFuji,
        wagmiChains.avalanche,
        wagmiChains.xLayer,
        wagmiChains.xLayerTestnet,
    ],
    connectors: [
        (0, connectors_1.walletConnect)({
            showQrModal: true,
            projectId: projectId ?? '',
        }),
        (0, connectors_1.coinbaseWallet)({
            appName: 'human-dashboard-ui',
        }),
    ],
    transports: {
        [wagmiChains.mainnet.id]: (0, wagmi_1.http)(constants_1.RPC_URLS[sdk_1.ChainId.MAINNET]),
        [wagmiChains.sepolia.id]: (0, wagmi_1.http)(constants_1.RPC_URLS[sdk_1.ChainId.SEPOLIA]),
        [wagmiChains.bsc.id]: (0, wagmi_1.http)(constants_1.RPC_URLS[sdk_1.ChainId.BSC_MAINNET]),
        [wagmiChains.bscTestnet.id]: (0, wagmi_1.http)(constants_1.RPC_URLS[sdk_1.ChainId.BSC_TESTNET]),
        [wagmiChains.polygon.id]: (0, wagmi_1.http)(constants_1.RPC_URLS[sdk_1.ChainId.POLYGON]),
        [wagmiChains.polygonAmoy.id]: (0, wagmi_1.http)(constants_1.RPC_URLS[sdk_1.ChainId.POLYGON_AMOY]),
        [wagmiChains.moonbeam.id]: (0, wagmi_1.http)(constants_1.RPC_URLS[sdk_1.ChainId.MOONBEAM]),
        [wagmiChains.moonbaseAlpha.id]: (0, wagmi_1.http)(constants_1.RPC_URLS[sdk_1.ChainId.MOONBASE_ALPHA]),
        [wagmiChains.avalanche.id]: (0, wagmi_1.http)(constants_1.RPC_URLS[sdk_1.ChainId.AVALANCHE]),
        [wagmiChains.avalancheFuji.id]: (0, wagmi_1.http)(constants_1.RPC_URLS[sdk_1.ChainId.AVALANCHE_TESTNET]),
        [wagmiChains.xLayer.id]: (0, wagmi_1.http)(constants_1.RPC_URLS[sdk_1.ChainId.XLAYER]),
        [wagmiChains.xLayerTestnet.id]: (0, wagmi_1.http)(constants_1.RPC_URLS[sdk_1.ChainId.XLAYER_TESTNET]),
    },
});
const WagmiProvider = ({ children }) => {
    return <wagmi_1.WagmiProvider config={exports.wagmiConfig}>{children}</wagmi_1.WagmiProvider>;
};
exports.WagmiProvider = WagmiProvider;
