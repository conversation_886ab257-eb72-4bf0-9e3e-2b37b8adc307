"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const react_1 = __importDefault(require("react"));
const client_1 = require("react-dom/client");
const react_gtm_module_1 = __importDefault(require("react-gtm-module"));
const react_redux_1 = require("react-redux");
require("./index.css");
const components_1 = require("./components");
const reportWebVitals_1 = __importDefault(require("./reportWebVitals"));
const state_1 = require("./state");
require("react-loading-skeleton/dist/skeleton.css");
react_gtm_module_1.default.initialize({
    gtmId: 'G-GQBK13YSRS',
});
const container = document.getElementById('root');
const root = (0, client_1.createRoot)(container);
root.render(<react_1.default.StrictMode>
    <react_redux_1.Provider store={state_1.store}>
      <components_1.App />
    </react_redux_1.Provider>
  </react_1.default.StrictMode>);
// If you want to start measuring performance in your app, pass a function
// to log results (for example: reportWebVitals(console.log))
// or send to an analytics endpoint. Learn more: https://bit.ly/CRA-vitals
(0, reportWebVitals_1.default)();
