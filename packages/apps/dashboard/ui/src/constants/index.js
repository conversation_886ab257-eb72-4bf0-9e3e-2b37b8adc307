"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.BITFINEX_HOT_WALLET_ADDRESS = exports.BITFINEX_SUPPORTED_CHAIN_IDS = exports.STAKING_CONTRACT_ADDRESS = exports.HM_TOKEN_DECIMALS = exports.ROLES = exports.SLOW_INTERVAL = exports.FAST_INTERVAL = exports.RPC_URLS = exports.FAUCET_CHAIN_IDS = exports.V2_SUPPORTED_CHAIN_IDS = void 0;
const sdk_1 = require("@human-protocol/sdk");
exports.V2_SUPPORTED_CHAIN_IDS = [
    sdk_1.ChainId.MAINNET,
    sdk_1.ChainId.SEPOLIA,
    sdk_1.ChainId.BSC_MAINNET,
    sdk_1.ChainId.BSC_TESTNET,
    sdk_1.ChainId.POLYGON,
    sdk_1.ChainId.POLYGON_AMOY,
    sdk_1.ChainId.MOONBEAM,
    sdk_1.ChainId.MOONBASE_ALPHA,
    sdk_1.ChainId.CELO,
    sdk_1.ChainId.CELO_ALFAJORES,
    sdk_1.ChainId.XLAYER,
    sdk_1.ChainId.XLAYER_TESTNET,
    // ChainId.AVALANCHE,
    // ChainId.AVALANCHE_TESTNET,
];
exports.FAUCET_CHAIN_IDS = [
    sdk_1.ChainId.SEPOLIA,
    sdk_1.ChainId.BSC_TESTNET,
    sdk_1.ChainId.POLYGON_AMOY,
    sdk_1.ChainId.MOONBASE_ALPHA,
    sdk_1.ChainId.AVALANCHE_TESTNET,
    sdk_1.ChainId.CELO_ALFAJORES,
    sdk_1.ChainId.XLAYER_TESTNET,
];
exports.RPC_URLS = {
    [sdk_1.ChainId.MAINNET]: import.meta.env.VITE_APP_RPC_URL_MAINNET || '',
    [sdk_1.ChainId.SEPOLIA]: import.meta.env.VITE_APP_RPC_URL_SEPOLIA || '',
    [sdk_1.ChainId.BSC_MAINNET]: import.meta.env.VITE_APP_RPC_URL_BSC_MAINNET || '',
    [sdk_1.ChainId.BSC_TESTNET]: import.meta.env.VITE_APP_RPC_URL_BSC_TESTNET || '',
    [sdk_1.ChainId.POLYGON]: import.meta.env.VITE_APP_RPC_URL_POLYGON || '',
    [sdk_1.ChainId.POLYGON_AMOY]: import.meta.env.VITE_APP_RPC_URL_POLYGON_AMOY || '',
    [sdk_1.ChainId.MOONBEAM]: import.meta.env.VITE_APP_RPC_URL_MOONBEAM || '',
    [sdk_1.ChainId.MOONBASE_ALPHA]: import.meta.env.VITE_APP_RPC_URL_MOONBASE_ALPHA || '',
    [sdk_1.ChainId.AVALANCHE_TESTNET]: import.meta.env.VITE_APP_RPC_URL_AVALANCHE_TESTNET || '',
    [sdk_1.ChainId.AVALANCHE]: import.meta.env.VITE_APP_RPC_URL_AVALANCHE || '',
    [sdk_1.ChainId.CELO_ALFAJORES]: import.meta.env.VITE_APP_RPC_URL_CELO_ALFAJORES || '',
    [sdk_1.ChainId.CELO]: import.meta.env.VITE_APP_RPC_URL_CELO || '',
    [sdk_1.ChainId.XLAYER]: import.meta.env.VITE_APP_RPC_URL_XLAYER || '',
    [sdk_1.ChainId.XLAYER_TESTNET]: import.meta.env.VITE_APP_RPC_URL_XLAYER_TESTNET || '',
};
exports.FAST_INTERVAL = 10000;
exports.SLOW_INTERVAL = 60000;
exports.ROLES = [
    'Validator',
    'Operator (Job Launcher)',
    'Exchange Oracle',
    'Reputation Oracle',
    'Recording Oracle',
];
exports.HM_TOKEN_DECIMALS = 18;
exports.STAKING_CONTRACT_ADDRESS = '******************************************';
exports.BITFINEX_SUPPORTED_CHAIN_IDS = [sdk_1.ChainId.MAINNET, sdk_1.ChainId.POLYGON];
exports.BITFINEX_HOT_WALLET_ADDRESS = '******************************************';
