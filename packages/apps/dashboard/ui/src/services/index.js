"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.showGithubPGP = exports.showIPFS = void 0;
const Timeout = (time) => {
    let controller = new AbortController();
    setTimeout(() => controller.abort(), time * 1000);
    return controller;
};
const showIPFS = async (ipfs) => {
    const res = await fetch(`https://nftstorage.link/ipfs/${ipfs}`, {
        signal: Timeout(10).signal,
    });
    if (res.ok) {
        return res.text();
    }
    else {
        throw new Error('Error getting ipfs data');
    }
};
exports.showIPFS = showIPFS;
const showGithubPGP = async (username) => {
    const res = await fetch(`https://api.github.com/users/${username}/gpg_keys`, {
        signal: Timeout(10).signal,
    });
    if (res.ok) {
        return res.text();
    }
    else {
        throw new Error('Error getting github gpg data');
    }
};
exports.showGithubPGP = showGithubPGP;
