"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.setChainId = exports.fetchLeaderEscrowsAsync = exports.fetchLeaderAsync = exports.fetchLeadersAsync = void 0;
const sdk_1 = require("@human-protocol/sdk");
const toolkit_1 = require("@reduxjs/toolkit");
const fast_json_stable_stringify_1 = __importDefault(require("fast-json-stable-stringify"));
const constants_1 = require("src/constants");
const utils_1 = require("src/utils");
const initialState = {
    loadingKeys: {},
    chainId: sdk_1.ChainId.ALL,
    leaders: {},
    leadersLoaded: false,
};
exports.fetchLeadersAsync = (0, toolkit_1.createAsyncThunk)('leader/fetchLeadersAsync', async () => {
    const leaders = (await Promise.all(constants_1.V2_SUPPORTED_CHAIN_IDS.map(async (chainId) => {
        const leaders = await sdk_1.OperatorUtils.getLeaders({
            chainId,
        });
        return {
            chainId,
            leaders: leaders.map((leader) => ({
                chainId,
                address: leader.address,
                role: leader.role,
                amountStaked: Number(leader.amountStaked),
                amountAllocated: Number(leader.amountAllocated),
                amountLocked: Number(leader.amountLocked),
                amountSlashed: Number(leader.amountSlashed),
                amountWithdrawn: Number(leader.amountWithdrawn),
                lockedUntilTimestamp: Number(leader.lockedUntilTimestamp),
                reputation: Number(leader.reputation),
                amountJobsLaunched: Number(leader.amountJobsLaunched),
            })),
        };
    }))).reduce((leaders, { chainId, leaders: chainLeaders }) => {
        leaders[chainId] = chainLeaders;
        return leaders;
    }, {});
    return leaders;
});
exports.fetchLeaderAsync = (0, toolkit_1.createAsyncThunk)('leader/fetchLeaderAsync', async ({ chainId, address }) => {
    const leader = await sdk_1.OperatorUtils.getLeader(chainId, address);
    if (!leader) {
        throw new Error('Error fetching leader detail');
    }
    return {
        chainId: chainId,
        address: leader.address,
        role: leader.role,
        amountStaked: Number(leader.amountStaked),
        amountAllocated: Number(leader.amountAllocated),
        amountLocked: Number(leader.amountLocked),
        amountSlashed: Number(leader.amountSlashed),
        amountWithdrawn: Number(leader.amountWithdrawn),
        lockedUntilTimestamp: Number(leader.lockedUntilTimestamp),
        reputation: Number(leader.reputation),
        amountJobsLaunched: Number(leader.amountJobsLaunched),
        url: leader.url,
    };
});
exports.fetchLeaderEscrowsAsync = (0, toolkit_1.createAsyncThunk)('leader/fetchLeaderEscrowsAsync', async ({ chainId, address }) => {
    const launchedEscrows = await sdk_1.EscrowUtils.getEscrows({
        chainId: chainId,
        launcher: address,
    });
    return launchedEscrows.map((escrow) => ({
        address: escrow.address,
        amountAllocated: (0, utils_1.formatAmount)(escrow.totalFundedAmount),
        amountPayout: (0, utils_1.formatAmount)(escrow.amountPaid),
        status: escrow.status,
    }));
});
exports.setChainId = (0, toolkit_1.createAction)('leader/setChainId');
const serializeLoadingKey = (action, suffix) => {
    const type = action.type.split(`/${suffix}`)[0];
    return (0, fast_json_stable_stringify_1.default)({
        arg: action.meta.arg,
        type,
    });
};
exports.default = (0, toolkit_1.createReducer)(initialState, (builder) => {
    builder.addCase(exports.setChainId, (state, action) => {
        state.chainId = action.payload;
    });
    builder.addCase(exports.fetchLeadersAsync.fulfilled, (state, action) => {
        state.leaders = action.payload;
        state.leadersLoaded = true;
    });
    builder.addCase(exports.fetchLeaderAsync.fulfilled, (state, action) => {
        state.currentLeader = action.payload;
        state.currentLeaderLoaded = true;
    });
    builder.addCase(exports.fetchLeaderAsync.rejected, (state, action) => {
        state.currentLeaderLoaded = true;
    });
    builder.addCase(exports.fetchLeaderEscrowsAsync.fulfilled, (state, action) => {
        state.leaderEscrows = action.payload;
        state.leaderEscrowsLoaded = true;
    });
    builder.addCase(exports.fetchLeaderEscrowsAsync.rejected, (state, action) => {
        state.leaderEscrowsLoaded = true;
    });
    builder.addMatcher((0, toolkit_1.isAnyOf)(exports.fetchLeadersAsync.pending, exports.fetchLeaderAsync.pending, exports.fetchLeaderEscrowsAsync.pending), (state, action) => {
        state.loadingKeys[serializeLoadingKey(action, 'pending')] = true;
    });
    builder.addMatcher((0, toolkit_1.isAnyOf)(exports.fetchLeadersAsync.fulfilled, exports.fetchLeaderAsync.fulfilled, exports.fetchLeaderEscrowsAsync.fulfilled), (state, action) => {
        state.loadingKeys[serializeLoadingKey(action, 'fulfilled')] = false;
    });
    builder.addMatcher((0, toolkit_1.isAnyOf)(exports.fetchLeadersAsync.rejected, exports.fetchLeaderAsync.rejected, exports.fetchLeaderEscrowsAsync.rejected), (state, action) => {
        state.loadingKeys[serializeLoadingKey(action, 'rejected')] = false;
    });
});
