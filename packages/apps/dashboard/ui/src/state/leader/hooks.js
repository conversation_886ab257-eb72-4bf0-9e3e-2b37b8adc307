"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.useLeaderByAddress = exports.useLeadersByChainID = exports.useFetchLeaderData = exports.useLeadersData = void 0;
const sdk_1 = require("@human-protocol/sdk");
const react_redux_1 = require("react-redux");
const __1 = require("..");
const reducer_1 = require("./reducer");
const constants_1 = require("src/constants");
const useRefreshEffect_1 = require("src/hooks/useRefreshEffect");
const useLeadersData = () => {
    const dispatch = (0, __1.useAppDispatch)();
    (0, useRefreshEffect_1.useSlowRefreshEffect)(() => {
        dispatch((0, reducer_1.fetchLeadersAsync)());
    }, [dispatch]);
};
exports.useLeadersData = useLeadersData;
const useFetchLeaderData = (chainId, address) => {
    const dispatch = (0, __1.useAppDispatch)();
    (0, useRefreshEffect_1.useSlowRefreshEffect)(() => {
        if (chainId && address) {
            dispatch((0, reducer_1.fetchLeaderAsync)({ chainId: +chainId, address }));
            dispatch((0, reducer_1.fetchLeaderEscrowsAsync)({ chainId: +chainId, address }));
        }
    }, [dispatch, chainId, address]);
};
exports.useFetchLeaderData = useFetchLeaderData;
const useLeadersByChainID = () => {
    const { leader } = (0, react_redux_1.useSelector)((state) => state);
    const { leaders, chainId } = leader;
    if (chainId === sdk_1.ChainId.ALL) {
        const allLeaders = [];
        constants_1.V2_SUPPORTED_CHAIN_IDS.forEach((chainId) => {
            allLeaders.push(...(leaders[chainId] || []).map((leader) => ({ ...leader, chainId })));
        });
        return allLeaders;
    }
    return leaders[chainId]?.map((leader) => ({ ...leader, chainId })) || [];
};
exports.useLeadersByChainID = useLeadersByChainID;
const useLeaderByAddress = (chainId, address) => {
    const { leader } = (0, react_redux_1.useSelector)((state) => state);
    if (!chainId || !address) {
        throw new Error('chainId and address are required');
    }
    return leader.leaders[+chainId]?.find((leader) => leader.address === address);
};
exports.useLeaderByAddress = useLeaderByAddress;
