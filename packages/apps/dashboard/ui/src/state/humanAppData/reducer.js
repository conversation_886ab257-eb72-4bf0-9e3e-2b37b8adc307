"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.setDays = exports.setChainId = void 0;
const sdk_1 = require("@human-protocol/sdk");
const toolkit_1 = require("@reduxjs/toolkit");
const initialState = {
    loadingKeys: {},
    chainId: sdk_1.ChainId.ALL,
    days: 30,
};
exports.setChainId = (0, toolkit_1.createAction)('humanAppData/setChainId');
exports.setDays = (0, toolkit_1.createAction)('humanAppData/setDays');
exports.default = (0, toolkit_1.createReducer)(initialState, (builder) => {
    builder.addCase(exports.setChainId, (state, action) => {
        state.chainId = action.payload;
    });
    builder.addCase(exports.setDays, (state, action) => {
        state.days = action.payload;
    });
});
