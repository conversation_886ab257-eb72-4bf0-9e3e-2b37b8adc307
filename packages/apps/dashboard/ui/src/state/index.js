"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.useAppDispatch = exports.store = void 0;
const toolkit_1 = require("@reduxjs/toolkit");
const react_redux_1 = require("react-redux");
const reducer_1 = __importDefault(require("./humanAppData/reducer"));
const reducer_2 = __importDefault(require("./leader/reducer"));
// import token from './token/reducer';
exports.store = (0, toolkit_1.configureStore)({
    reducer: {
        humanAppData: reducer_1.default,
        // token,
        leader: reducer_2.default,
    },
    middleware: (getDefaultMiddleware) => getDefaultMiddleware({ thunk: true }),
});
const useAppDispatch = () => (0, react_redux_1.useDispatch)();
exports.useAppDispatch = useAppDispatch;
