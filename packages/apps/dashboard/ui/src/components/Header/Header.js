"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Header = void 0;
const icons_material_1 = require("@mui/icons-material");
const material_1 = require("@mui/material");
const react_1 = require("react");
const react_router_dom_1 = require("react-router-dom");
const SearchBox_1 = require("../SearchBox");
const logo_svg_1 = __importDefault(require("src/assets/logo.svg"));
const Header = () => {
    const [searchOpen, setSearchOpen] = (0, react_1.useState)(false);
    /**
     * @TODO: Remove the flag once it's implemented
     */
    const showSearchBox = false;
    const toggleSearchBox = () => setSearchOpen(!searchOpen);
    return (<material_1.Box sx={{ flexGrow: 1 }}>
      <material_1.AppBar position="fixed" sx={{
            background: 'rgba(255, 255, 255, 0.8)',
            boxShadow: 'none',
            backdropFilter: 'blur(9px)',
        }}>
        <material_1.Toolbar disableGutters>
          <material_1.Box sx={{ width: '100%' }}>
            <material_1.Box sx={{
            display: 'flex',
            flexDirection: 'row',
            justifyContent: 'space-between',
            alignItems: 'center',
            width: '100%',
            height: '88px',
            boxSizing: 'border-box',
            padding: {
                xs: '29px 24px',
                md: '29px 77px 20px 60px',
            },
        }}>
              {searchOpen ? (<material_1.Box display="flex" alignItems="center" width="100%">
                  {showSearchBox && <SearchBox_1.SearchBox />}
                  <material_1.IconButton color="primary" sx={{ ml: 2 }} onClick={toggleSearchBox}>
                    <icons_material_1.Close />
                  </material_1.IconButton>
                </material_1.Box>) : (<>
                  <react_router_dom_1.Link to="/" style={{
                display: 'flex',
                alignItems: 'center',
                textDecoration: 'none',
            }}>
                    <material_1.Box component="img" src={logo_svg_1.default} alt="logo" sx={{ width: { xs: '100px', md: '118px' } }}/>
                    <material_1.Typography sx={{
                fontSize: { xs: '14px', md: '16px' },
                lineHeight: { xs: 1, md: 1.5 },
                letterSpacing: '0.15px',
            }} color="primary" ml="10px">
                      Faucet
                    </material_1.Typography>
                  </react_router_dom_1.Link>
                </>)}
            </material_1.Box>
          </material_1.Box>
        </material_1.Toolbar>
      </material_1.AppBar>
    </material_1.Box>);
};
exports.Header = Header;
