"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Stake = void 0;
const material_1 = require("@mui/material");
const react_1 = require("react");
const Container_1 = require("../Cards/Container");
const CurrencyInput_1 = require("./CurrencyInput");
const Stake = () => {
    const [mode, setMode] = (0, react_1.useState)('stake');
    const [isSuccess, setIsSuccess] = (0, react_1.useState)(false);
    return (<Container_1.Container>
      {!isSuccess ? (<material_1.Box sx={{
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'space-between',
                height: '100%',
            }}>
          <material_1.Box>
            <material_1.ToggleButtonGroup value={mode} exclusive onChange={(e, newMode) => setMode(newMode)} fullWidth>
              <material_1.ToggleButton color="primary" value="stake">
                Stake HMT
              </material_1.ToggleButton>
              <material_1.ToggleButton color="primary" value="unstake">
                Unstake HMT
              </material_1.ToggleButton>
            </material_1.ToggleButtonGroup>
            <material_1.Box my={4}>
              <CurrencyInput_1.CurrencyInput placeholder="Enter amount to stake"/>
            </material_1.Box>
            <material_1.Box my={3}>
              <material_1.Typography color="primary" textAlign="center" mb={1.5} variant="caption" component="p">
                Network address: 0xa9fbD31cd4EAA938Fc90A31eFe231316444a1102
              </material_1.Typography>
              <material_1.Typography color="primary" textAlign="center" variant="caption" component="p">
                Staking amount: 0.00 HMT
              </material_1.Typography>
            </material_1.Box>
          </material_1.Box>
          <material_1.Button color="primary" variant="contained" fullWidth sx={{ p: 1, fontWeight: 600 }} onClick={() => setIsSuccess(true)}>
            Confirm
          </material_1.Button>
        </material_1.Box>) : (<material_1.Box sx={{
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                justifyContent: 'center',
                py: 10,
            }}>
          <material_1.Typography color="primary" variant="h6" fontWeight={500} gutterBottom>
            Success!
          </material_1.Typography>
          <material_1.Typography color="primary" variant="body2" textAlign="center" mt={2}>
            Congratulations, you have successfully staked 1000 HMT on the
            Network 1 to the role of Job Launcher.
          </material_1.Typography>
          <material_1.Typography color="primary" variant="body2" fontWeight={600} mt={4}>
            Check transaction{' '}
            <material_1.Link href="https://etherscan.io" target="_blank">
              here
            </material_1.Link>
          </material_1.Typography>
        </material_1.Box>)}
    </Container_1.Container>);
};
exports.Stake = Stake;
