"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.HMTStatusChart = void 0;
const material_1 = require("@mui/material");
const react_1 = __importDefault(require("react"));
const recharts_1 = require("recharts");
const Container_1 = require("../Cards/Container");
const data = [
    { name: 'Allocated', value: 11, fill: '#f9faff' },
    { name: 'Locked', value: 29, fill: '#858EC6' },
    { name: 'Avaliable', value: 48, fill: '#320A8D' },
];
const HMTStatusChart = () => {
    return (<Container_1.Container densed>
      <material_1.Box sx={{ width: '100%', height: '360px' }}>
        <recharts_1.ResponsiveContainer>
          <recharts_1.PieChart width={730} height={360}>
            <recharts_1.Pie data={data} dataKey="value" nameKey="name" cx="50%" cy="50%" outerRadius={120} label={({ cx, cy, midAngle, innerRadius, outerRadius, value, index, }) => {
            const RADIAN = Math.PI / 180;
            const radius = 50 + innerRadius + (outerRadius - innerRadius);
            const x = cx + radius * Math.cos(-midAngle * RADIAN);
            const y = Math.min(cy + radius * Math.sin(-midAngle * RADIAN), 310);
            return (<text x={x} y={y} fill="#320A8D" textAnchor={x > cx ? 'start' : 'end'} dominantBaseline="central">
                    <tspan x={x} dy="1em">
                      {value}%
                    </tspan>
                    <tspan x={x} dy="1em">
                      {data[index].name}
                    </tspan>
                  </text>);
        }} labelLine={false}/>
          </recharts_1.PieChart>
        </recharts_1.ResponsiveContainer>
      </material_1.Box>
    </Container_1.Container>);
};
exports.HMTStatusChart = HMTStatusChart;
