"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.HMTStatusTable = void 0;
const Box_1 = __importDefault(require("@mui/material/Box"));
const Button_1 = __importDefault(require("@mui/material/Button"));
const Paper_1 = __importDefault(require("@mui/material/Paper"));
const Table_1 = __importDefault(require("@mui/material/Table"));
const TableBody_1 = __importDefault(require("@mui/material/TableBody"));
const TableCell_1 = __importDefault(require("@mui/material/TableCell"));
const TableContainer_1 = __importDefault(require("@mui/material/TableContainer"));
const TableHead_1 = __importDefault(require("@mui/material/TableHead"));
const TableRow_1 = __importDefault(require("@mui/material/TableRow"));
const Typography_1 = __importDefault(require("@mui/material/Typography"));
const React = __importStar(require("react"));
const Icons_1 = require("../Icons");
const HMTStatusTable = () => {
    return (<Box_1.default sx={{ display: 'flex', width: '100%', height: '100%' }}>
      <Paper_1.default sx={{
            width: '100%',
            borderRadius: { xs: '8px', xl: '16px' },
            boxShadow: '0px 3px 1px -2px #E9EBFA, 0px 2px 2px rgba(233, 235, 250, 0.5), 0px 1px 5px rgba(233, 235, 250, 0.2)',
            pt: 2,
        }}>
        <TableContainer_1.default>
          <Table_1.default aria-labelledby="tableTitle" sx={{
            minWidth: 750,
            th: { fontWeight: 600, borderBottomColor: '#320A8D' },
            td: {
                ':nth-child(odd)': {
                    color: '#858EC6',
                },
            },
            'th,td': {
                py: 2,
                '&:first-of-type': {
                    paddingLeft: '38px',
                },
                '&:last-of-type': {
                    paddingLeft: '38px',
                },
            },
            'tr:last-child': {
                td: {
                    borderBottom: 'none',
                },
            },
        }}>
            <TableHead_1.default>
              <TableRow_1.default>
                <TableCell_1.default colSpan={4}>HMT Status</TableCell_1.default>
              </TableRow_1.default>
            </TableHead_1.default>
            <TableBody_1.default>
              <TableRow_1.default>
                <TableCell_1.default>Allocated:</TableCell_1.default>
                <TableCell_1.default>800 HMT</TableCell_1.default>
                <TableCell_1.default>$0.00</TableCell_1.default>
                <TableCell_1.default>
                  <Button_1.default variant="text" color="primary">
                    View escrows
                  </Button_1.default>
                </TableCell_1.default>
              </TableRow_1.default>
              <TableRow_1.default>
                <TableCell_1.default>Locked:</TableCell_1.default>
                <TableCell_1.default>200 HMT</TableCell_1.default>
                <TableCell_1.default>$0.00</TableCell_1.default>
                <TableCell_1.default>
                  <Button_1.default variant="text" color="primary">
                    View escrows
                  </Button_1.default>
                </TableCell_1.default>
              </TableRow_1.default>
              <TableRow_1.default>
                <TableCell_1.default>Available:</TableCell_1.default>
                <TableCell_1.default>2,300 HMT</TableCell_1.default>
                <TableCell_1.default>$0.00</TableCell_1.default>
                <TableCell_1.default>
                  <Button_1.default variant="text" color="primary">
                    Withdraw
                  </Button_1.default>
                </TableCell_1.default>
              </TableRow_1.default>
              <TableRow_1.default>
                <TableCell_1.default>Total Staked:</TableCell_1.default>
                <TableCell_1.default>5,400 HMT</TableCell_1.default>
                <TableCell_1.default>$0.00</TableCell_1.default>
                <TableCell_1.default>
                  <Box_1.default sx={{
            background: '#f9faff',
            borderRadius: '4px',
            padding: '9px',
            display: 'inline-flex',
            alignItems: 'center',
            ml: 1,
        }}>
                    <Icons_1.PolygonIcon color="primary" sx={{ fontSize: '1rem', mr: '4px' }}/>
                    <Typography_1.default color="primary" variant="caption" sx={{ textTransform: 'uppercase' }}>
                      Polygon
                    </Typography_1.default>
                  </Box_1.default>
                </TableCell_1.default>
              </TableRow_1.default>
            </TableBody_1.default>
          </Table_1.default>
        </TableContainer_1.default>
      </Paper_1.default>
    </Box_1.default>);
};
exports.HMTStatusTable = HMTStatusTable;
