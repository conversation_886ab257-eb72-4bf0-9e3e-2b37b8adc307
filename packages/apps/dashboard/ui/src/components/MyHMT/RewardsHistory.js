"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.RewardsHistory = void 0;
const material_1 = require("@mui/material");
const react_1 = __importDefault(require("react"));
const recharts_1 = require("recharts");
const Container_1 = require("../Cards/Container");
const data = [
    {
        name: 'Page A',
        uv: 4000,
        pv: 2400,
        amt: 2400,
    },
    {
        name: 'Page B',
        uv: 3000,
        pv: 1398,
        amt: 2210,
    },
    {
        name: 'Page C',
        uv: 2000,
        pv: 9800,
        amt: 2290,
    },
    {
        name: 'Page D',
        uv: 2780,
        pv: 3908,
        amt: 2000,
    },
    {
        name: '<PERSON> E',
        uv: 1890,
        pv: 4800,
        amt: 2181,
    },
    {
        name: 'Page F',
        uv: 2390,
        pv: 3800,
        amt: 2500,
    },
    {
        name: 'Page G',
        uv: 3490,
        pv: 4300,
        amt: 2100,
    },
];
const RewardCard = ({ title, hmtValue, usdValue, }) => {
    return (<material_1.Box sx={{
            border: '1px solid #CBCFE6',
            borderRadius: '16px',
            py: 2,
            px: 3,
            width: '100%',
            display: 'flex',
            flexDirection: 'column',
            justifyContent: 'space-between',
        }}>
      <material_1.Typography fontWeight={500} color="primary" gutterBottom>
        {title}
      </material_1.Typography>
      <material_1.Box>
        <material_1.Box sx={{ display: 'flex', alignItems: 'baseline', mb: 1 }}>
          <material_1.Typography variant="h4" fontWeight={600} color="primary">
            0.00
          </material_1.Typography>
          <material_1.Typography fontSize={14} fontWeight={600} color="primary">
            HMT
          </material_1.Typography>
        </material_1.Box>
        <material_1.Typography variant="body2" fontWeight={600} color="text.secondary">
          $0.00
        </material_1.Typography>
      </material_1.Box>
    </material_1.Box>);
};
const RewardsHistory = () => {
    return (<Container_1.Container>
      <material_1.Grid container spacing={{ xs: 4, xl: 8 }}>
        <material_1.Grid item xs={12} lg={7} xl={6}>
          <material_1.Box sx={{
            background: '#F6F7FE',
            borderRadius: '16px',
            py: 4,
            px: { xs: 4, md: 8, xl: 11 },
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
            gap: 2,
            flexWrap: 'wrap',
            mb: 3,
        }}>
            <material_1.Box>
              <material_1.Typography fontSize={16} fontWeight={500} color="primary" gutterBottom>
                Total Earned
              </material_1.Typography>
              <material_1.Box sx={{ display: 'flex', alignItems: 'baseline' }}>
                <material_1.Typography fontSize={48} lineHeight={1.1} fontWeight={600} color="primary">
                  0.00
                </material_1.Typography>
                <material_1.Typography fontSize={14} fontWeight={600} color="primary">
                  HMT
                </material_1.Typography>
              </material_1.Box>
              <material_1.Typography fontSize={14} fontWeight={600} color="text.secondary">
                $0.00
              </material_1.Typography>
            </material_1.Box>
            <material_1.Box>
              <material_1.Stack direction="row" spacing={2}>
                <material_1.Typography sx={{ minWidth: '120px' }} variant="body2" color="text.secondary">
                  Withdrawn
                </material_1.Typography>
                <material_1.Typography variant="body2" color="text.primary">
                  0000.00 HMT
                </material_1.Typography>
                <material_1.Typography variant="body2" color="text.secondary">
                  $0.00
                </material_1.Typography>
              </material_1.Stack>
              <material_1.Divider sx={{ my: 2, borderColor: '#320A8D' }}/>
              <material_1.Stack direction="row" spacing={2}>
                <material_1.Typography sx={{ minWidth: '120px' }} variant="body2" color="text.secondary">
                  Withdrawable
                </material_1.Typography>
                <material_1.Typography variant="body2" color="text.primary">
                  0000.00 HMT
                </material_1.Typography>
                <material_1.Typography variant="body2" color="text.secondary">
                  $0.00
                </material_1.Typography>
              </material_1.Stack>
            </material_1.Box>
          </material_1.Box>
          <material_1.Grid container spacing={3}>
            <material_1.Grid item xs={12} sm={4} sx={{ display: 'flex' }}>
              <RewardCard title="Worker Payouts" hmtValue={0} usdValue={0}/>
            </material_1.Grid>
            <material_1.Grid item xs={12} sm={4} sx={{ display: 'flex' }}>
              <RewardCard title="Oracle Fees" hmtValue={0} usdValue={0}/>
            </material_1.Grid>
            <material_1.Grid item xs={12} sm={4} sx={{ display: 'flex' }}>
              <RewardCard title="Slashing Rewards" hmtValue={0} usdValue={0}/>
            </material_1.Grid>
          </material_1.Grid>
        </material_1.Grid>
        <material_1.Grid item xs={12} lg={5} xl={6}>
          <material_1.Box sx={{ width: '100%', height: '400px' }}>
            <recharts_1.ResponsiveContainer>
              <recharts_1.AreaChart data={data} margin={{
            top: 10,
            right: 30,
            left: 0,
            bottom: 0,
        }}>
                <recharts_1.CartesianGrid strokeDasharray="3 3"/>
                <recharts_1.XAxis dataKey="name"/>
                <recharts_1.YAxis />
                <recharts_1.Tooltip />
                <recharts_1.Area type="monotone" dataKey="uv" stroke="#8884d8" fill="#8884d8"/>
              </recharts_1.AreaChart>
            </recharts_1.ResponsiveContainer>
          </material_1.Box>
        </material_1.Grid>
      </material_1.Grid>
    </Container_1.Container>);
};
exports.RewardsHistory = RewardsHistory;
