"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Rewards = void 0;
const material_1 = require("@mui/material");
const react_1 = require("react");
const Container_1 = require("../Cards/Container");
const CurrencyInput_1 = require("./CurrencyInput");
const Rewards = () => {
    const [mode, setMode] = (0, react_1.useState)('claim');
    const [isSuccess, setIsSuccess] = (0, react_1.useState)(false);
    return (<Container_1.Container>
      {!isSuccess ? (<material_1.Box sx={{
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'space-between',
                height: '100%',
            }}>
          <material_1.Box mb={3}>
            <material_1.ToggleButtonGroup value={mode} exclusive onChange={(e, newMode) => setMode(newMode)} fullWidth>
              <material_1.ToggleButton color="primary" value="claim">
                Claim Rewards
              </material_1.ToggleButton>
              <material_1.ToggleButton color="primary" value="reinvest">
                Reinvest Rewards
              </material_1.ToggleButton>
            </material_1.ToggleButtonGroup>
            <material_1.Box my={4}>
              <CurrencyInput_1.CurrencyInput placeholder="Enter amount to claim"/>
            </material_1.Box>
          </material_1.Box>
          <material_1.Button color="primary" variant="contained" fullWidth sx={{ mt: 'auto', p: 1, fontWeight: 600 }} onClick={() => setIsSuccess(true)}>
            Confirm
          </material_1.Button>
        </material_1.Box>) : (<material_1.Box sx={{
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                justifyContent: 'center',
                py: 10,
            }}>
          <material_1.Typography color="primary" variant="h6" fontWeight={500} gutterBottom>
            Success!
          </material_1.Typography>
          <material_1.Typography color="primary" variant="body2" textAlign="center" mt={2}>
            Congratulations, you have successfully claimed 1000 HMT on the
            Network 1.
          </material_1.Typography>
          <material_1.Typography color="primary" variant="body2" fontWeight={600} mt={4}>
            Check transaction{' '}
            <material_1.Link href="https://etherscan.io" target="_blank">
              here
            </material_1.Link>
          </material_1.Typography>
        </material_1.Box>)}
    </Container_1.Container>);
};
exports.Rewards = Rewards;
