"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CurrencyInput = void 0;
const material_1 = require("@mui/material");
const react_1 = __importDefault(require("react"));
const CurrencyInput = ({ placeholder }) => {
    return (<material_1.Box sx={{ position: 'relative' }}>
      <material_1.TextField fullWidth helperText="HMT Available: 0.000" placeholder={placeholder}/>
      <material_1.Button variant="text" sx={{
            position: 'absolute',
            right: '10px',
            top: '12px',
            color: '#858EC6',
        }}>
        MAX
      </material_1.Button>
    </material_1.Box>);
};
exports.CurrencyInput = CurrencyInput;
