"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.HMTTable = void 0;
const Box_1 = __importDefault(require("@mui/material/Box"));
const Paper_1 = __importDefault(require("@mui/material/Paper"));
const Table_1 = __importDefault(require("@mui/material/Table"));
const TableBody_1 = __importDefault(require("@mui/material/TableBody"));
const TableCell_1 = __importDefault(require("@mui/material/TableCell"));
const TableContainer_1 = __importDefault(require("@mui/material/TableContainer"));
const TableHead_1 = __importDefault(require("@mui/material/TableHead"));
const TablePagination_1 = __importDefault(require("@mui/material/TablePagination"));
const TableRow_1 = __importDefault(require("@mui/material/TableRow"));
const TableSortLabel_1 = __importDefault(require("@mui/material/TableSortLabel"));
const utils_1 = require("@mui/utils");
const React = __importStar(require("react"));
function createData(amount, role, escrowAddress) {
    return {
        amount,
        role,
        escrowAddress,
    };
}
const rows = [
    createData(2, 'Operator (Job Launcher)', '0x38Aa090f961f81de537AF268437FFA97C198668d'),
    createData(2, 'Operator (Job Launcher)', '0x38Aa090f961f81de537AF268437FFA97C198668d'),
    createData(2, 'Operator (Job Launcher)', '0x38Aa090f961f81de537AF268437FFA97C198668d'),
    createData(2, 'Operator (Job Launcher)', '0x38Aa090f961f81de537AF268437FFA97C198668d'),
    createData(2, 'Operator (Job Launcher)', '0x38Aa090f961f81de537AF268437FFA97C198668d'),
    createData(2, 'Operator (Job Launcher)', '0x38Aa090f961f81de537AF268437FFA97C198668d'),
    createData(2, 'Operator (Job Launcher)', '0x38Aa090f961f81de537AF268437FFA97C198668d'),
];
function descendingComparator(a, b, orderBy) {
    if (b[orderBy] < a[orderBy]) {
        return -1;
    }
    if (b[orderBy] > a[orderBy]) {
        return 1;
    }
    return 0;
}
function getComparator(order, orderBy) {
    return order === 'desc'
        ? (a, b) => descendingComparator(a, b, orderBy)
        : (a, b) => -descendingComparator(a, b, orderBy);
}
// Since 2020 all major browsers ensure sort stability with Array.prototype.sort().
// stableSort() brings sort stability to non-modern browsers (notably IE11). If you
// only support modern browsers you can replace stableSort(exampleArray, exampleComparator)
// with exampleArray.slice().sort(exampleComparator)
function stableSort(array, comparator) {
    const stabilizedThis = array.map((el, index) => [el, index]);
    stabilizedThis.sort((a, b) => {
        const order = comparator(a[0], b[0]);
        if (order !== 0) {
            return order;
        }
        return a[1] - b[1];
    });
    return stabilizedThis.map((el) => el[0]);
}
const headCells = [
    {
        id: 'amount',
        disablePadding: false,
        label: 'Amount Allocated',
    },
    {
        id: 'role',
        disablePadding: false,
        label: 'Role',
    },
    {
        id: 'escrowAddress',
        disablePadding: false,
        label: 'Escrow',
    },
];
const DEFAULT_ORDER = 'asc';
const DEFAULT_ORDER_BY = 'amount';
const DEFAULT_ROWS_PER_PAGE = 5;
function EnhancedTableHead(props) {
    const { order, orderBy, onRequestSort } = props;
    const createSortHandler = (newOrderBy) => (event) => {
        onRequestSort(event, newOrderBy);
    };
    return (<TableHead_1.default>
      <TableRow_1.default>
        {headCells.map((headCell) => (<TableCell_1.default key={headCell.id} padding={headCell.disablePadding ? 'none' : 'normal'} sortDirection={orderBy === headCell.id ? order : false}>
            <TableSortLabel_1.default active={orderBy === headCell.id} direction={orderBy === headCell.id ? order : 'asc'} onClick={createSortHandler(headCell.id)}>
              {headCell.label}
              {orderBy === headCell.id ? (<Box_1.default component="span" sx={utils_1.visuallyHidden}>
                  {order === 'desc' ? 'sorted descending' : 'sorted ascending'}
                </Box_1.default>) : null}
            </TableSortLabel_1.default>
          </TableCell_1.default>))}
      </TableRow_1.default>
    </TableHead_1.default>);
}
const HMTTable = () => {
    const [order, setOrder] = React.useState(DEFAULT_ORDER);
    const [orderBy, setOrderBy] = React.useState(DEFAULT_ORDER_BY);
    const [page, setPage] = React.useState(0);
    const [visibleRows, setVisibleRows] = React.useState(null);
    const [rowsPerPage, setRowsPerPage] = React.useState(DEFAULT_ROWS_PER_PAGE);
    const [paddingHeight, setPaddingHeight] = React.useState(0);
    React.useEffect(() => {
        let rowsOnMount = stableSort(rows, getComparator(DEFAULT_ORDER, DEFAULT_ORDER_BY));
        rowsOnMount = rowsOnMount.slice(0 * DEFAULT_ROWS_PER_PAGE, 0 * DEFAULT_ROWS_PER_PAGE + DEFAULT_ROWS_PER_PAGE);
        setVisibleRows(rowsOnMount);
    }, []);
    const handleRequestSort = React.useCallback((event, newOrderBy) => {
        const isAsc = orderBy === newOrderBy && order === 'asc';
        const toggledOrder = isAsc ? 'desc' : 'asc';
        setOrder(toggledOrder);
        setOrderBy(newOrderBy);
        const sortedRows = stableSort(rows, getComparator(toggledOrder, newOrderBy));
        const updatedRows = sortedRows.slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage);
        setVisibleRows(updatedRows);
    }, [order, orderBy, page, rowsPerPage]);
    const handleChangePage = React.useCallback((event, newPage) => {
        setPage(newPage);
        const sortedRows = stableSort(rows, getComparator(order, orderBy));
        const updatedRows = sortedRows.slice(newPage * rowsPerPage, newPage * rowsPerPage + rowsPerPage);
        setVisibleRows(updatedRows);
        // Avoid a layout jump when reaching the last page with empty rows.
        const numEmptyRows = newPage > 0
            ? Math.max(0, (1 + newPage) * rowsPerPage - rows.length)
            : 0;
        const newPaddingHeight = 53 * numEmptyRows;
        setPaddingHeight(newPaddingHeight);
    }, [order, orderBy, rowsPerPage]);
    const handleChangeRowsPerPage = React.useCallback((event) => {
        const updatedRowsPerPage = parseInt(event.target.value, 10);
        setRowsPerPage(updatedRowsPerPage);
        setPage(0);
        const sortedRows = stableSort(rows, getComparator(order, orderBy));
        const updatedRows = sortedRows.slice(0 * updatedRowsPerPage, 0 * updatedRowsPerPage + updatedRowsPerPage);
        setVisibleRows(updatedRows);
        // There is no layout jump to handle on the first page.
        setPaddingHeight(0);
    }, [order, orderBy]);
    return (<Box_1.default sx={{ width: '100%' }}>
      <Paper_1.default sx={{ width: '100%', mb: 2, borderRadius: '8px', boxShadow: 'none' }}>
        <TableContainer_1.default>
          <Table_1.default sx={{ minWidth: 750 }} aria-labelledby="tableTitle" size="medium">
            <EnhancedTableHead order={order} orderBy={orderBy} onRequestSort={handleRequestSort}/>
            <TableBody_1.default>
              {visibleRows
            ? visibleRows.map((row, index) => {
                return (<TableRow_1.default hover role="checkbox" tabIndex={-1} key={index} sx={{ cursor: 'pointer' }}>
                        <TableCell_1.default component="th" scope="row">
                          {row.amount} HMT
                        </TableCell_1.default>
                        <TableCell_1.default>{row.role}</TableCell_1.default>
                        <TableCell_1.default>{row.escrowAddress}</TableCell_1.default>
                      </TableRow_1.default>);
            })
            : null}
              {paddingHeight > 0 && (<TableRow_1.default style={{
                height: paddingHeight,
            }}>
                  <TableCell_1.default colSpan={6}/>
                </TableRow_1.default>)}
            </TableBody_1.default>
          </Table_1.default>
        </TableContainer_1.default>
        <TablePagination_1.default rowsPerPageOptions={[5, 10, 25]} component="div" count={rows.length} rowsPerPage={rowsPerPage} page={page} onPageChange={handleChangePage} onRowsPerPageChange={handleChangeRowsPerPage}/>
      </Paper_1.default>
    </Box_1.default>);
};
exports.HMTTable = HMTTable;
