"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SearchBox = void 0;
const icons_material_1 = require("@mui/icons-material");
const material_1 = require("@mui/material");
const react_1 = require("react");
const SearchBox = () => {
    const theme = (0, material_1.useTheme)();
    const [anchorEl, setAnchorEl] = (0, react_1.useState)(null);
    const open = Boolean(anchorEl);
    const handleClick = (event) => {
        setAnchorEl(event.currentTarget);
    };
    const handleClose = () => {
        setAnchorEl(null);
    };
    return (<material_1.Box display="flex" alignItems="center" position="relative" width="100%">
      <>
        <material_1.Button variant="contained" color="inherit" onClick={handleClick} sx={{
            backgroundColor: '#e9e9f8',
            boxShadow: '0px 7px 8px -4px #E9EBFA, 0px 13px 19px 2px rgba(233, 235, 250, 0.5), 0px 5px 24px 4px rgba(233, 235, 250, 0.2)',
            color: theme.palette.primary.main,
            borderTopRightRadius: 0,
            borderBottomRightRadius: 0,
            borderTopLeftRadius: 6,
            borderBottomLeftRadius: 6,
        }} endIcon={<icons_material_1.ArrowDropUp />}>
          Filters
        </material_1.Button>
        <material_1.Menu anchorEl={anchorEl} open={open} onClose={handleClose} MenuListProps={{
            'aria-labelledby': 'basic-button',
        }}>
          <material_1.MenuItem onClick={handleClose}>All</material_1.MenuItem>
          <material_1.MenuItem onClick={handleClose}>Escrows</material_1.MenuItem>
          <material_1.MenuItem onClick={handleClose}>Stakers</material_1.MenuItem>
        </material_1.Menu>
      </>
      <material_1.TextField fullWidth variant="filled" InputProps={{
            sx: {
                borderTopRightRadius: 6,
                borderBottomRightRadius: 6,
                borderTopLeftRadius: 0,
                borderBottomLeftRadius: 0,
                '&:after': {
                    display: 'none',
                },
                '&:before': {
                    display: 'none',
                },
            },
        }} inputProps={{
            sx: {
                padding: '7px 26px',
                background: '#f5f7fe',
                borderTopRightRadius: 6,
                borderBottomRightRadius: 6,
                borderTopLeftRadius: 0,
                borderBottomLeftRadius: 0,
            },
        }} placeholder="Search by Escrow/Staker"/>
      <icons_material_1.Search color="primary" sx={{ position: 'absolute', right: '6px', top: '6px' }}/>
    </material_1.Box>);
};
exports.SearchBox = SearchBox;
