"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PageWrapper = void 0;
const Box_1 = __importDefault(require("@mui/material/Box"));
const PageWrapper = ({ children }) => (<Box_1.default sx={{ px: { xs: 0, md: 3, lg: 5, xl: 7 } }}>
    <Box_1.default sx={{
        minHeight: 'calc(100vh - 180px)',
        background: '#f6f7fe',
        boxSizing: 'border-box',
        borderRadius: { xs: '0px', md: '24px' },
        padding: {
            xs: '36px 32px',
            lg: '60px 67px 69px',
            xl: '58px 104px 104px 104px',
        },
    }}>
      {children}
    </Box_1.default>
  </Box_1.default>);
exports.PageWrapper = PageWrapper;
