"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.WalletModal = void 0;
const icons_material_1 = require("@mui/icons-material");
const material_1 = require("@mui/material");
const react_1 = require("react");
const wagmi_1 = require("wagmi");
const coinbase_svg_1 = __importDefault(require("src/assets/coinbase.svg"));
const metamask_svg_1 = __importDefault(require("src/assets/metamask.svg"));
const walletconnect_svg_1 = __importDefault(require("src/assets/walletconnect.svg"));
const NotificationProvider_1 = require("src/providers/NotificationProvider");
const WALLET_ICONS = {
    metaMask: metamask_svg_1.default,
    coinbaseWalletSDK: coinbase_svg_1.default,
    walletConnect: walletconnect_svg_1.default,
};
const WalletModal = ({ open, onClose }) => {
    const { connect, connectors, error: errorConnect } = (0, wagmi_1.useConnect)();
    const { showMessage } = (0, NotificationProvider_1.useNotification)();
    (0, react_1.useEffect)(() => {
        if (errorConnect) {
            showMessage(errorConnect.message, 'error');
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [errorConnect]);
    const theme = (0, material_1.useTheme)();
    return (<material_1.Dialog open={open} onClose={onClose} maxWidth={false} PaperProps={{ sx: { mx: 2, maxWidth: 'calc(100% - 32px)' } }}>
      <material_1.Box display="flex" maxWidth="784px">
        <material_1.Box width={{ xs: '0', md: '50%' }} display={{ xs: 'none', md: 'flex' }} sx={{
            background: theme.palette.primary.main,
            boxSizing: 'border-box',
            flexDirection: 'column',
            justifyContent: 'space-between',
        }} px={9} py={6}>
          <material_1.Typography variant="h4" fontWeight={600} color="#fff">
            Connect
            <br /> your wallet
          </material_1.Typography>
          <material_1.Typography color="text.secondary" variant="caption">
            By connecting a wallet, you agree to HUMAN Protocol Terms of Service
            and consent to its Privacy Policy.
          </material_1.Typography>
        </material_1.Box>
        <material_1.Box sx={{ boxSizing: 'border-box' }} width={{ xs: '100%', md: '50%' }} minWidth={{ xs: '340px', sm: '392px' }} display="flex" flexDirection="column" p={{ xs: 2, sm: 4 }}>
          <material_1.IconButton sx={{ ml: 'auto', mb: 3 }} onClick={onClose}>
            <icons_material_1.Close color="primary"/>
          </material_1.IconButton>
          <material_1.Box width="100%" display="flex" flexDirection="column" gap={3}>
            {connectors.map((connector) => (<material_1.Button sx={{
                display: 'flex',
                justifyContent: 'space-between',
                px: 2,
                py: 3,
                background: '#f6f7fe',
                color: theme.palette.text.secondary,
                border: `1px solid transparent`,
                '&:hover': {
                    color: theme.palette.text.primary,
                    border: `1px solid ${theme.palette.primary.main}`,
                },
            }} key={connector.id} onClick={() => {
                connect({ connector });
                if (connector.id === 'walletConnect') {
                    onClose();
                }
            }}>
                <img src={connector.icon || WALLET_ICONS[connector.id]} alt={connector.id}/>
                <span>{connector.name}</span>
              </material_1.Button>))}
          </material_1.Box>
        </material_1.Box>
      </material_1.Box>
    </material_1.Dialog>);
};
exports.WalletModal = WalletModal;
