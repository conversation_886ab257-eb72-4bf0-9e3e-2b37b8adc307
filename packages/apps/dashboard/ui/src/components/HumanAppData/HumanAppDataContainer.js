"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.HumanAppDataContainer = void 0;
const material_1 = require("@mui/material");
const NetworkSelect_1 = require("../NetworkSelect");
const TimeRangeButtons_1 = __importDefault(require("../TimeRangeButtons"));
const ViewTitle_1 = require("../ViewTitle");
const HumanAppDataView_1 = require("./HumanAppDataView");
const network_svg_1 = __importDefault(require("src/assets/network.svg"));
const constants_1 = require("src/constants");
const state_1 = require("src/state");
const hooks_1 = require("src/state/humanAppData/hooks");
const reducer_1 = require("src/state/humanAppData/reducer");
const HumanAppDataContainer = () => {
    const dispatch = (0, state_1.useAppDispatch)();
    const chainId = (0, hooks_1.useChainId)();
    const handleNetworkChange = (e) => {
        dispatch((0, reducer_1.setChainId)(e.target.value));
    };
    return (<material_1.Box id="human-app-data" mt={{ xs: '44px', md: '51px' }}>
      <material_1.Box sx={{
            display: 'flex',
            flexDirection: { xs: 'column', lg: 'row' },
            justifyContent: { xs: 'flex-start', lg: 'space-between' },
            alignItems: { xs: 'flex-start', lg: 'center' },
            gap: 4,
            mb: '40px',
            px: { xs: 0, xl: '12px' },
        }}>
        <ViewTitle_1.ViewTitle title="HUMAN App data" iconUrl={network_svg_1.default}/>
        <material_1.Box sx={{ width: '100%', maxWidth: { xs: '100%', lg: '513px' } }}>
          <NetworkSelect_1.NetworkSelect value={chainId} showAllNetwork supportedChainIds={constants_1.V2_SUPPORTED_CHAIN_IDS} width="100%" onChange={handleNetworkChange}/>
        </material_1.Box>
        <material_1.Box sx={{ width: '100%', maxWidth: { xs: '100%', lg: '368px' } }}>
          <TimeRangeButtons_1.default fullWidth/>
        </material_1.Box>
      </material_1.Box>
      <HumanAppDataView_1.HumanAppDataView />
    </material_1.Box>);
};
exports.HumanAppDataContainer = HumanAppDataContainer;
