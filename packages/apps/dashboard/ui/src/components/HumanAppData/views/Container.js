"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ChartContainer = void 0;
const material_1 = require("@mui/material");
const react_1 = __importDefault(require("react"));
const chart_1 = require("../chart");
const ChartContainer = ({ data, title, children, isLoading, isNotSupportedChain = false, }) => {
    const theme = (0, material_1.useTheme)();
    const isMobile = (0, material_1.useMediaQuery)(theme.breakpoints.down(600));
    if (isMobile) {
        return (<material_1.Box sx={{
                borderRadius: '16px',
                background: '#FFF',
                boxShadow: '0px 1px 5px 0px rgba(233, 235, 250, 0.20), 0px 2px 2px 0px rgba(233, 235, 250, 0.50), 0px 3px 1px -2px #E9EBFA;',
                p: 4,
                pb: 6,
                position: 'relative',
            }}>
        <material_1.Typography fontSize={24} color="primary" mb={2}>
          {title}
        </material_1.Typography>
        {isNotSupportedChain ? (<material_1.Box sx={{
                    minHeight: 250,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                }}>
            <material_1.Typography color="textPrimary" sx={{
                    fontSize: '20px',
                    lineHeight: 1.6,
                    textAlign: 'center',
                    fontWeight: 500,
                }}>
              At the moment there is no data available.
            </material_1.Typography>
          </material_1.Box>) : isLoading ? (<material_1.Box sx={{
                    minHeight: 250,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                }}>
            <material_1.CircularProgress />
          </material_1.Box>) : data ? (<chart_1.HumanAppDataChart data={data} minHeight={250}/>) : (<></>)}
        {children}
      </material_1.Box>);
    }
    return (<material_1.Box sx={{
            width: '100%',
            bgcolor: 'background.paper',
            display: 'flex',
        }}>
      <material_1.Box sx={{
            width: '100%',
            borderRadius: '8px',
            background: '#F6F7FE',
            pt: { xs: 5, md: 10 },
            pb: { xs: 5, md: 7 },
            px: { xs: 2, md: 5 },
            minHeight: 400,
        }}>
        {isNotSupportedChain ? (<material_1.Box sx={{
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                height: '100%',
            }}>
            <material_1.Typography color="textPrimary" sx={{ fontSize: '24px', lineHeight: 1.5, textAlign: 'center' }}>
              At the moment there is no data available.
            </material_1.Typography>
          </material_1.Box>) : isLoading ? (<material_1.Box sx={{
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                height: '100%',
            }}>
            <material_1.CircularProgress />
          </material_1.Box>) : data ? (<chart_1.HumanAppDataChart data={data} minHeight={300} minWidth={800}/>) : (<></>)}
      </material_1.Box>
    </material_1.Box>);
};
exports.ChartContainer = ChartContainer;
