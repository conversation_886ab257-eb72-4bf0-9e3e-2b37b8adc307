"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TransactionsView = void 0;
const react_1 = __importDefault(require("react"));
const Container_1 = require("./Container");
const TooltipIcon_1 = require("src/components/TooltipIcon");
const tooltips_1 = require("src/constants/tooltips");
const TransactionsView = ({ isLoading, data, }) => {
    return (<Container_1.ChartContainer isLoading={isLoading} data={data} title="Transactions">
      <TooltipIcon_1.TooltipIcon title={tooltips_1.TOOLTIPS.TRANSACTIONS}/>
    </Container_1.ChartContainer>);
};
exports.TransactionsView = TransactionsView;
