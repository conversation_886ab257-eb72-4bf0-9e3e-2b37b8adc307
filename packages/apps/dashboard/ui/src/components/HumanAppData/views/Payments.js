"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PaymentsView = void 0;
const react_1 = __importDefault(require("react"));
const Container_1 = require("./Container");
const TooltipIcon_1 = require("src/components/TooltipIcon");
const tooltips_1 = require("src/constants/tooltips");
const PaymentsView = ({ isLoading, data, }) => {
    return (<Container_1.ChartContainer isLoading={isLoading} data={data} title="Payments">
      <TooltipIcon_1.TooltipIcon title={tooltips_1.TOOLTIPS.PAYMENTS}/>
    </Container_1.ChartContainer>);
};
exports.PaymentsView = PaymentsView;
