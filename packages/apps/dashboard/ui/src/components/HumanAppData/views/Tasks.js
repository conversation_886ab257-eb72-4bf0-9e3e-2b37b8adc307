"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TasksView = void 0;
const sdk_1 = require("@human-protocol/sdk");
const react_1 = __importDefault(require("react"));
const Container_1 = require("./Container");
const TooltipIcon_1 = require("src/components/TooltipIcon");
const tooltips_1 = require("src/constants/tooltips");
const hooks_1 = require("src/state/humanAppData/hooks");
const TasksView = ({ isLoading, data, }) => {
    const chainId = (0, hooks_1.useChainId)();
    return (<Container_1.ChartContainer isLoading={isLoading} data={data} title="Tasks" isNotSupportedChain={chainId !== sdk_1.ChainId.POLYGON && chainId !== sdk_1.ChainId.ALL}>
      <TooltipIcon_1.TooltipIcon title={tooltips_1.TOOLTIPS.SOLVED_TASKS}/>
    </Container_1.ChartContainer>);
};
exports.TasksView = TasksView;
