"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.WorkersView = void 0;
const sdk_1 = require("@human-protocol/sdk");
const react_1 = __importStar(require("react"));
const Container_1 = require("./Container");
const TooltipIcon_1 = require("src/components/TooltipIcon");
const tooltips_1 = require("src/constants/tooltips");
const useWorkerStats_1 = require("src/hooks/useWorkerStats");
const hooks_1 = require("src/state/humanAppData/hooks");
const WorkersView = () => {
    const days = (0, hooks_1.useDays)();
    const chainId = (0, hooks_1.useChainId)();
    const { data, isLoading } = (0, useWorkerStats_1.useWorkerStats)();
    const seriesData = (0, react_1.useMemo)(() => {
        if (data) {
            return [...data.dailyWorkersData].slice(-days).map((d) => ({
                date: d.timestamp,
                value: Number(d.activeWorkers),
            }));
        }
        return [];
    }, [data, days]);
    return (<Container_1.ChartContainer isLoading={isLoading} data={seriesData} title="Workers" isNotSupportedChain={chainId !== sdk_1.ChainId.POLYGON}>
      <TooltipIcon_1.TooltipIcon title={tooltips_1.TOOLTIPS.WORKERS}/>
    </Container_1.ChartContainer>);
};
exports.WorkersView = WorkersView;
