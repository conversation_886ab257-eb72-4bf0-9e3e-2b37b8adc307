"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.HumanAppDataChart = void 0;
const material_1 = require("@mui/material");
const dayjs_1 = __importDefault(require("dayjs"));
const numeral_1 = __importDefault(require("numeral"));
const react_1 = __importDefault(require("react"));
const recharts_1 = require("recharts");
const CustomTooltip = ({ active, payload, label }) => {
    if (active && payload && payload.length) {
        return (<material_1.Box sx={{
                background: '#fff',
                border: '1px solid #CBCFE6',
                borderRadius: '10px',
                padding: '30px',
            }}>
        <material_1.Typography color="text.primary" variant="body2" fontWeight={600} mb={2}>
          {(0, dayjs_1.default)(label).format('MMM D, YYYY')}
        </material_1.Typography>
        {/* <Typography color="text.primary" variant="caption">
              {tab Value} Tasks
            </Typography> */}
        <material_1.Typography color="text.primary" variant="h6" fontWeight={500}>
          {(0, numeral_1.default)(payload[0].value).format('0.0a').toUpperCase()}
        </material_1.Typography>
      </material_1.Box>);
    }
    return null;
};
const HumanAppDataChart = ({ data, minWidth, minHeight, }) => {
    return (<recharts_1.ResponsiveContainer width="100%" height="100%" minWidth={minWidth} minHeight={minHeight}>
      <recharts_1.AreaChart data={data}>
        <defs>
          <linearGradient id="paint0_linear_4037_63345" x1="257" y1="0" x2="257" y2="276.5" gradientUnits="userSpaceOnUse">
            <stop offset="0.290598" stopColor="#CACFE8" stopOpacity="0.3"/>
            <stop offset="1" stopColor="#E9ECFF" stopOpacity="0"/>
          </linearGradient>
        </defs>
        <recharts_1.CartesianGrid vertical={false} strokeDasharray={3}/>
        <recharts_1.XAxis dataKey="date" axisLine={false} tickLine={false} tick={{
            fill: '#320A8D',
            fontSize: '10px',
            fontFamily: 'Inter',
            fontWeight: 500,
        }} tickFormatter={(value) => (0, dayjs_1.default)(value).format('MMM D')} tickMargin={12} padding={{ left: 10, right: 10 }}/>
        <recharts_1.YAxis axisLine={false} tickLine={false} tick={{
            fill: '#320A8D',
            fontSize: '10px',
            fontFamily: 'Inter',
            fontWeight: 500,
        }} tickFormatter={(value) => (0, numeral_1.default)(value).format('0a').toUpperCase()} width={40}/>
        <recharts_1.Tooltip cursor={{ fill: '#dadef0' }} content={<CustomTooltip />}/>
        <recharts_1.Area type="monotone" dataKey="value" stroke="#320A8D" fill="url(#paint0_linear_4037_63345)"/>
      </recharts_1.AreaChart>
    </recharts_1.ResponsiveContainer>);
};
exports.HumanAppDataChart = HumanAppDataChart;
