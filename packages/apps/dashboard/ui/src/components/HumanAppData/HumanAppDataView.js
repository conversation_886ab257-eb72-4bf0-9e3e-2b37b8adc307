"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.HumanAppDataView = void 0;
const material_1 = require("@mui/material");
const react_1 = require("react");
const TooltipIcon_1 = require("../TooltipIcon");
const Payments_1 = require("./views/Payments");
const Tasks_1 = require("./views/Tasks");
const Transactions_1 = require("./views/Transactions");
const tooltips_1 = require("src/constants/tooltips");
const useHumanAppData_1 = require("src/hooks/useHumanAppData");
const hooks_1 = require("src/state/humanAppData/hooks");
var ViewButton;
(function (ViewButton) {
    ViewButton["Transactions"] = "Transactions";
    ViewButton["Tasks"] = "Tasks";
    ViewButton["Payments"] = "Payments";
})(ViewButton || (ViewButton = {}));
const VIEW_BUTTONS = [
    { label: 'Transactions', value: ViewButton.Transactions },
    { label: 'Tasks', value: ViewButton.Tasks },
    { label: 'Payments', value: ViewButton.Payments },
];
const HumanAppDataView = () => {
    const theme = (0, material_1.useTheme)();
    const isMobile = (0, material_1.useMediaQuery)(theme.breakpoints.down(600));
    const [viewButton, setViewButton] = (0, react_1.useState)(ViewButton.Transactions);
    const chainId = (0, hooks_1.useChainId)();
    const days = (0, hooks_1.useDays)();
    const { data, isLoading } = (0, useHumanAppData_1.useHumanAppData)(chainId);
    const transactionsSeries = (0, react_1.useMemo)(() => {
        if (data) {
            const cumulativeData = [...data[0].data[0].attributes.dailyHMTData]
                .map((d) => ({
                date: d.timestamp,
                value: Number(d.totalTransactionCount),
            }))
                .reverse()
                .reduce((acc, d) => {
                acc.push({
                    date: d.date,
                    value: acc.length ? acc[acc.length - 1].value + d.value : d.value,
                });
                return acc;
            }, []);
            return cumulativeData.reverse().slice(0, days).reverse();
        }
        return [];
    }, [data, days]);
    const paymentsSeries = (0, react_1.useMemo)(() => {
        if (data) {
            const cumulativeData = [...data[0].data[0].attributes.dailyPaymentsData]
                .map((d) => ({
                date: d.timestamp,
                value: Number(d.totalAmountPaid),
            }))
                .reverse()
                .reduce((acc, d) => {
                acc.push({
                    date: d.date,
                    value: acc.length ? acc[acc.length - 1].value + d.value : d.value,
                });
                return acc;
            }, []);
            return cumulativeData.reverse().slice(0, days).reverse();
        }
        return [];
    }, [data, days]);
    const taskSeries = (0, react_1.useMemo)(() => {
        if (data) {
            const cumulativeData = data[1].data
                .map((d) => {
                const multiplier = d.attributes.date <= '2022-11-30' ? 18 : 9;
                return {
                    date: d.attributes.date,
                    value: d.attributes.solved_count * multiplier,
                };
            })
                .reduce((acc, d) => {
                acc.push({
                    date: d.date,
                    value: acc.length ? acc[acc.length - 1].value + d.value : d.value,
                });
                return acc;
            }, []);
            return cumulativeData.reverse().slice(0, days).reverse();
        }
        return [];
    }, [data, days]);
    const getTooltipTitle = (button) => {
        switch (button) {
            case ViewButton.Tasks:
                return tooltips_1.TOOLTIPS.SOLVED_TASKS;
            case ViewButton.Payments:
                return tooltips_1.TOOLTIPS.PAYMENTS;
            case ViewButton.Transactions:
                return tooltips_1.TOOLTIPS.TRANSACTIONS;
        }
    };
    if (isMobile) {
        return (<material_1.Stack spacing={4}>
        <Transactions_1.TransactionsView isLoading={isLoading} data={transactionsSeries}/>
        <Tasks_1.TasksView isLoading={isLoading} data={taskSeries}/>
        <Payments_1.PaymentsView isLoading={isLoading} data={paymentsSeries}/>
      </material_1.Stack>);
    }
    return (<material_1.Box sx={{
            borderRadius: '16px',
            background: '#FFF',
            boxShadow: '0px 1px 5px 0px rgba(233, 235, 250, 0.20), 0px 2px 2px 0px rgba(233, 235, 250, 0.50), 0px 3px 1px -2px #E9EBFA;',
            padding: '42px 32px 32px',
            position: 'relative',
        }}>
      <material_1.ToggleButtonGroup exclusive fullWidth value={viewButton} onChange={(e, newButton) => {
            if (newButton === null)
                return;
            setViewButton(newButton);
        }}>
        {VIEW_BUTTONS.map(({ label, value }) => (<material_1.ToggleButton key={label} value={value} aria-label={label} sx={{
                color: 'text.secondary',
                fontWeight: 500,
                borderColor: 'text.secondary',
                minWidth: 50,
            }} size="small">
            {label}
          </material_1.ToggleButton>))}
      </material_1.ToggleButtonGroup>
      <material_1.Box mt={3} mb={3} sx={{ overflow: 'auto' }}>
        {viewButton === ViewButton.Transactions && (<Transactions_1.TransactionsView isLoading={isLoading} data={transactionsSeries}/>)}
        {viewButton === ViewButton.Tasks && (<Tasks_1.TasksView isLoading={isLoading} data={taskSeries}/>)}
        {viewButton === ViewButton.Payments && (<Payments_1.PaymentsView isLoading={isLoading} data={paymentsSeries}/>)}
      </material_1.Box>
      <TooltipIcon_1.TooltipIcon title={getTooltipTitle(viewButton)}/>
    </material_1.Box>);
};
exports.HumanAppDataView = HumanAppDataView;
