"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Footer = void 0;
const material_1 = require("@mui/material");
const SocialIcons_1 = require("../SocialIcons");
const Footer = () => {
    const theme = (0, material_1.useTheme)();
    const isMobile = (0, material_1.useMediaQuery)(theme.breakpoints.down('md'));
    return isMobile ? (<material_1.Box px="28px" py={6}>
      <SocialIcons_1.SocialIcons />
      <material_1.Box display="flex" flexDirection="column" ml={2} mt={4}>
        <material_1.Typography color="text.secondary" variant="caption" lineHeight={1}>
          Terms and conditions
        </material_1.Typography>
        <material_1.Typography color="text.secondary" variant="caption" mt={1}>
          © {new Date().getFullYear()} HPF. HUMAN Protocol® is a registered
          trademark
        </material_1.Typography>
      </material_1.Box>
    </material_1.Box>) : (<material_1.Box sx={{
            px: 12,
            py: 5,
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            flexWrap: 'wrap',
        }}>
      <material_1.Box sx={{ display: 'flex', alignItems: 'center' }}>
        <material_1.Link href="https://www.humanprotocol.org/privacy-policy" sx={{ textDecoration: 'none' }}>
          <material_1.Typography color="text.secondary" variant="caption" lineHeight={1}>
            Terms and conditions
          </material_1.Typography>
        </material_1.Link>
      </material_1.Box>
      <material_1.Typography color="text.secondary" variant="caption">
        © {new Date().getFullYear()} HPF. HUMAN Protocol® is a registered
        trademark
      </material_1.Typography>
      <SocialIcons_1.SocialIcons />
    </material_1.Box>);
};
exports.Footer = Footer;
