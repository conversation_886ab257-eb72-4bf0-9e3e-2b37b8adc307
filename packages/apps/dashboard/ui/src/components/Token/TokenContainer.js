"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TokenContainer = void 0;
const material_1 = require("@mui/material");
const ViewTitle_1 = require("../ViewTitle");
const TokenView_1 = require("./TokenView");
const token_svg_1 = __importDefault(require("src/assets/token.svg"));
const TokenContainer = () => {
    return (<material_1.Box mt={{ xs: 4, md: '51px' }} id="token">
      <ViewTitle_1.ViewTitle title="Token" iconUrl={token_svg_1.default}/>
      <material_1.Box mt={{ xs: 4, md: 8 }}>
        <TokenView_1.TokenView />
      </material_1.Box>
    </material_1.Box>);
};
exports.TokenContainer = TokenContainer;
