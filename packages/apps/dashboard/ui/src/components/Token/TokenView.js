"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TokenView = void 0;
const sdk_1 = require("@human-protocol/sdk");
const material_1 = require("@mui/material");
const react_1 = require("react");
const Cards_1 = require("../Cards");
// import bingXIcon from 'src/assets/exchanges/bingx.png';
const bitfinex_png_1 = __importDefault(require("src/assets/exchanges/bitfinex.png"));
const bitmart_png_1 = __importDefault(require("src/assets/exchanges/bitmart.png"));
const coinlist_pro_png_1 = __importDefault(require("src/assets/exchanges/coinlist-pro.png"));
const gate_io_png_1 = __importDefault(require("src/assets/exchanges/gate-io.png"));
const lbank_svg_1 = __importDefault(require("src/assets/exchanges/lbank.svg"));
const mexc_png_1 = __importDefault(require("src/assets/exchanges/mexc.png"));
const probit_global_png_1 = __importDefault(require("src/assets/exchanges/probit-global.png"));
const tooltips_1 = require("src/constants/tooltips");
const useHumanAppData_1 = require("src/hooks/useHumanAppData");
const hooks_1 = require("src/state/humanAppData/hooks");
const EXCHANGES = [
    {
        icon: bitfinex_png_1.default,
        href: 'https://trading.bitfinex.com/t/HMT:USD?type=exchange',
        name: 'Bitfinex',
    },
    {
        icon: probit_global_png_1.default,
        href: 'https://www.probit.com/app/exchange/HMT-USDT',
        name: 'Probit Global',
    },
    {
        icon: gate_io_png_1.default,
        href: 'https://gate.io/trade/hmt_usdt',
        name: 'Gate.io',
    },
    // { icon: bingXIcon, href: 'https://www.bingx.com/', name: 'BingX' },
    {
        icon: coinlist_pro_png_1.default,
        href: 'https://pro.coinlist.co/trader/HMT-USDT',
        name: 'Coinlist Pro',
    },
    {
        icon: lbank_svg_1.default,
        href: 'https://www.lbank.com/en-US/trade/hmt_usdt/',
        name: 'LBank',
    },
    {
        icon: mexc_png_1.default,
        href: 'https://www.mexc.com/exchange/HMT_USDT',
        name: 'MEXC',
    },
    {
        icon: bitmart_png_1.default,
        href: 'https://www.bitmart.com/trade/en?symbol=HMT_USDT',
        name: 'Bitmart',
    },
];
const TotalSupplyComponent = ({ value }) => {
    const formatter = new Intl.NumberFormat('en-US', {
        notation: 'compact',
        compactDisplay: 'long',
    });
    const compactNumber = formatter.format(value);
    const [number, unit] = compactNumber.split(' ');
    return (<material_1.Box sx={{
            display: 'flex',
            alignItems: 'baseline',
            mt: 3,
            overflow: 'hidden',
        }}>
      <material_1.Typography variant="h2" color="primary" lineHeight={1} sx={{ fontSize: { xs: 40, xl: 60 } }}>
        {number}
      </material_1.Typography>
      <material_1.Typography variant="h4" color="primary" sx={{
            fontSize: { xs: 28, xl: 34 },
            textTransform: 'capitalize',
            ml: 2,
        }} lineHeight={1}>
        {unit}
      </material_1.Typography>
    </material_1.Box>);
};
const TokenView = () => {
    const chainId = (0, hooks_1.useChainId)();
    const days = (0, hooks_1.useDays)();
    const { data } = (0, useHumanAppData_1.useHumanAppData)(chainId);
    const transferCount = (0, react_1.useMemo)(() => {
        if (data) {
            return data[0].data[0].attributes.dailyHMTData
                .slice(0, days)
                .reverse()
                .reduce((acc, d) => acc + Number(d.totalTransactionCount), 0);
        }
    }, [data, days]);
    return (<material_1.Box>
      <material_1.Grid container spacing={{ xs: 2, sm: 2, md: 3, lg: 4, xl: 5 }}>
        <material_1.Grid item xs={12} md={4}>
          <Cards_1.CardTextBlock title="Total transactions" value={transferCount} tooltipTitle={tooltips_1.TOOLTIPS.TOTAL_TRANSACTIONS}/>
        </material_1.Grid>
        <material_1.Grid item xs={12} md={4}>
          <Cards_1.CardTextBlock title="Holders" value={data?.[0].data?.[0]?.attributes?.totalHolders} tooltipTitle={tooltips_1.TOOLTIPS.HOLDERS}/>
        </material_1.Grid>
        <material_1.Grid item xs={12} md={4}>
          <Cards_1.CardTextBlock title="Total Supply" value={chainId === sdk_1.ChainId.ALL
            ? 1000000000
            : data?.[0].data?.[0]?.attributes?.totalSupply} component={TotalSupplyComponent} tooltipTitle={tooltips_1.TOOLTIPS.TOTAL_SUPPLY}/>
        </material_1.Grid>
      </material_1.Grid>
      <material_1.Box sx={{
            display: 'flex',
            flexDirection: { xs: 'column', md: 'row ' },
            alignItems: { xs: 'flex-start', md: 'center' },
            justifyContent: 'flex-end',
            mt: '52px',
            px: { xs: 4, md: 0 },
        }}>
        <material_1.Typography color="primary" variant="body2" fontWeight={600} sx={{
            mr: { xs: 0, md: 4 },
            mb: { xs: 3, md: 0 },
            whiteSpace: 'nowrap',
        }}>
          Find HMT at
        </material_1.Typography>
        <material_1.Box sx={{
            display: 'flex',
            alignItems: 'center',
            gap: 4,
            flexWrap: 'wrap',
        }}>
          {EXCHANGES.map(({ icon, href, name }) => (<material_1.Link key={name} component="a" sx={{
                display: 'flex',
                alignItems: 'center',
                textDecoration: 'none',
                minWidth: { xs: '110px', md: 'auto' },
            }} href={href} target="_blank">
              <img src={icon} alt={name} style={{ width: '32px', height: '32px', borderRadius: '100%' }}/>
              <material_1.Typography color="primary" sx={{
                fontSize: '12px',
                letterSpacing: '0.4px',
                lineHeight: 1.6,
                ml: 1,
            }}>
                {name}
              </material_1.Typography>
            </material_1.Link>))}
        </material_1.Box>
      </material_1.Box>
    </material_1.Box>);
};
exports.TokenView = TokenView;
