"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CopyAddressButton = void 0;
const material_1 = require("@mui/material");
const copy_to_clipboard_1 = __importDefault(require("copy-to-clipboard"));
const Icons_1 = require("../Icons");
const CopyAddressButton = ({ address, ...rest }) => {
    const handleClickCopy = () => {
        (0, copy_to_clipboard_1.default)(address);
    };
    return (<material_1.Box display="flex" alignItems="center" justifyContent="space-between" sx={{
            background: '#fff',
            boxShadow: '0px 3px 1px -2px #E9EBFA, 0px 2px 2px rgba(233, 235, 250, 0.5), 0px 1px 5px rgba(233, 235, 250, 0.2)',
            borderRadius: '16px',
            px: 3,
            py: 1.5,
        }} {...rest}>
      <material_1.Typography color="primary" variant="body2" fontWeight={600} sx={{ maxWidth: '376px', overflow: 'hidden', textOverflow: 'ellipsis' }}>
        {address}
      </material_1.Typography>
      <material_1.IconButton color="primary" onClick={handleClickCopy}>
        <Icons_1.CopyLinkIcon />
      </material_1.IconButton>
    </material_1.Box>);
};
exports.CopyAddressButton = CopyAddressButton;
