"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const ToggleButton_1 = __importDefault(require("@mui/material/ToggleButton"));
const ToggleButtonGroup_1 = __importDefault(require("@mui/material/ToggleButtonGroup"));
const React = __importStar(require("react"));
const react_redux_1 = require("react-redux");
const state_1 = require("src/state");
const reducer_1 = require("src/state/humanAppData/reducer");
const RANGE_BUTTONS = [
    { label: '1W', value: 7 },
    { label: '1M', value: 30 },
    { label: '3M', value: 90 },
    { label: '6M', value: 180 },
    { label: '1Y', value: 365 },
    { label: 'Max', value: 1000 },
];
function TimeRangeButtons({ fullWidth, }) {
    const { days } = (0, react_redux_1.useSelector)((state) => state.humanAppData);
    const dispatch = (0, state_1.useAppDispatch)();
    const handleChange = (event, newValue) => {
        if (newValue === null)
            return;
        dispatch((0, reducer_1.setDays)(newValue));
    };
    return (<ToggleButtonGroup_1.default value={days} exclusive onChange={handleChange} aria-label="time range" fullWidth={fullWidth}>
      {RANGE_BUTTONS.map(({ label, value }) => (<ToggleButton_1.default key={label} value={value} aria-label={label} sx={{
                color: 'text.secondary',
                fontWeight: 500,
                borderColor: 'text.secondary',
                minWidth: 50,
                padding: '10px 11px',
            }}>
          {label}
        </ToggleButton_1.default>))}
    </ToggleButtonGroup_1.default>);
}
exports.default = TimeRangeButtons;
