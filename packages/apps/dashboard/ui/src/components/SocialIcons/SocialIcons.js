"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SocialIcons = void 0;
const material_1 = require("@mui/material");
const Icons_1 = require("../Icons");
const SocialIcons = ({ direction = 'row' }) => (<material_1.Box sx={{ display: 'flex', alignItems: 'center', flexDirection: direction }}>
    <material_1.Link href="http://hmt.ai/github" target="_blank" sx={{ m: direction === 'row' ? '0px 15px' : '15px 0px' }}>
      <Icons_1.GithubIcon />
    </material_1.Link>
    <material_1.Link href="http://hmt.ai/discord" target="_blank" sx={{ m: direction === 'row' ? '0px 15px' : '15px 0px' }}>
      <Icons_1.DiscordIcon />
    </material_1.Link>
    <material_1.Link href="http://hmt.ai/twitter" target="_blank" sx={{ m: direction === 'row' ? '0px 15px' : '15px 0px' }}>
      <Icons_1.XIcon />
    </material_1.Link>
    <material_1.Link href="http://hmt.ai/linkedin" target="_blank" sx={{ m: direction === 'row' ? '0px 15px' : '15px 0px' }}>
      <Icons_1.LinkedinIcon />
    </material_1.Link>
  </material_1.Box>);
exports.SocialIcons = SocialIcons;
