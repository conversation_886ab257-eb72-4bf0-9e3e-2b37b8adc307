"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SolvedTasksContainer = void 0;
const material_1 = require("@mui/material");
const ViewTitle_1 = require("../ViewTitle");
const NewsView_1 = require("./NewsView");
const SolvedTasksView_1 = require("./SolvedTasksView");
const tasks_svg_1 = __importDefault(require("src/assets/tasks.svg"));
const SolvedTasksContainer = () => {
    return (<material_1.Box id="solved-tasks-container">
      <material_1.Box display="flex" alignItems="center" flexWrap="wrap">
        <ViewTitle_1.ViewTitle title="Solved Tasks" iconUrl={tasks_svg_1.default}/>
      </material_1.Box>
      <material_1.Box mt={{ xs: '26px', md: '51px' }}>
        <material_1.Grid container spacing={{ xs: 5, lg: 3, xl: 4 }}>
          <material_1.Grid item xs={12} md={8}>
            <SolvedTasksView_1.SolvedTasksView />
          </material_1.Grid>
          <material_1.Grid item xs={12} md={4}>
            <NewsView_1.NewsView />
          </material_1.Grid>
        </material_1.Grid>
      </material_1.Box>
    </material_1.Box>);
};
exports.SolvedTasksContainer = SolvedTasksContainer;
