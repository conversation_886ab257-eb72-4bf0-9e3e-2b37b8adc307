"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SolvedTasksView = void 0;
const material_1 = require("@mui/material");
const dayjs_1 = __importDefault(require("dayjs"));
const numeral_1 = __importDefault(require("numeral"));
const react_1 = require("react");
const react_loading_skeleton_1 = __importStar(require("react-loading-skeleton"));
const recharts_1 = require("recharts");
const Cards_1 = require("../Cards");
const TooltipIcon_1 = require("../TooltipIcon");
const tooltips_1 = require("src/constants/tooltips");
const useMonthlyTaskSummaries_1 = require("src/hooks/useMonthlyTaskSummaries");
const CustomTooltip = ({ active, payload, label }) => {
    if (active && payload && payload.length) {
        return (<material_1.Box sx={{
                background: '#fff',
                border: '1px solid #CBCFE6',
                borderRadius: '10px',
                padding: '8px 16px',
            }}>
        <material_1.Typography color="text.primary" variant="h6" fontWeight={500}>
          {(0, numeral_1.default)(payload[0].value).format('0.[00] a').toUpperCase()}
        </material_1.Typography>
      </material_1.Box>);
    }
    return null;
};
const SolvedTasksView = () => {
    const theme = (0, material_1.useTheme)();
    const isMobile = (0, material_1.useMediaQuery)(theme.breakpoints.down('sm'));
    const { data, isLoading } = (0, useMonthlyTaskSummaries_1.useMonthlyTaskSummaries)();
    const cumulativeSolvedTasks = (0, react_1.useMemo)(() => {
        if (!data)
            return [];
        return data.reduce((acc, d) => {
            acc.push({
                date: d.date,
                value: acc.length ? acc[acc.length - 1].value + d.value : d.value,
            });
            return acc;
        }, []);
    }, [data]);
    const solvedTasksCount = (0, react_1.useMemo)(() => {
        if (!data)
            return 0;
        return data.reduce((acc, d) => acc + d.value, 0);
    }, [data]);
    return (<Cards_1.CardContainer sxProps={{ padding: { xs: '42px 32px 32px', md: '74px 64px 64px' } }}>
      {isLoading ? (<react_loading_skeleton_1.SkeletonTheme baseColor="rgba(0, 0, 0, 0.1)" highlightColor="rgba(0, 0, 0, 0.18)">
          <react_loading_skeleton_1.default count={1} width="100%" height="320px"/>
        </react_loading_skeleton_1.SkeletonTheme>) : (<>
          <material_1.Grid container sx={{ height: '100%' }} spacing={{ xs: 4, md: 2 }}>
            <material_1.Grid item xs={12} md={5} xl={4}>
              <material_1.Box mb={2}>
                <material_1.Typography variant="body2" color="primary" fontWeight={600} mb="14px">
                  {`Total number of tasks till ${(0, dayjs_1.default)(data?.[(data?.length || 1) - 1].date).format('MMM D, YYYY')}`}
                </material_1.Typography>
                <material_1.Typography variant="h2" color="primary" fontWeight={800} lineHeight={1.125} sx={{ whiteSpace: 'nowrap' }} fontSize={{ xs: '40px', lg: '55px' }}>
                  {(0, numeral_1.default)(solvedTasksCount).format('0.[00] a').toUpperCase()}
                </material_1.Typography>
              </material_1.Box>
            </material_1.Grid>
            <material_1.Grid item xs={12} md={7} xl={8}>
              <recharts_1.ResponsiveContainer width="100%" height="100%" minHeight={250}>
                <recharts_1.AreaChart data={cumulativeSolvedTasks} margin={{ bottom: 10 }}>
                  <defs>
                    <linearGradient id="paint0_linear_4037_63345" x1="257" y1="0" x2="257" y2="276.5" gradientUnits="userSpaceOnUse">
                      <stop offset="0.290598" stopColor="#CACFE8" stopOpacity="0.3"/>
                      <stop offset="1" stopColor="#E9ECFF" stopOpacity="0"/>
                    </linearGradient>
                  </defs>
                  <recharts_1.CartesianGrid vertical={false} strokeDasharray={3}/>
                  <recharts_1.XAxis dataKey="date" axisLine={false} tickLine={false} tick={{
                fill: '#320A8D',
                fontSize: '10px',
                fontFamily: 'Inter',
                fontWeight: 500,
            }} tickFormatter={(value) => (0, dayjs_1.default)(value).format('MMM')} tickMargin={12} padding={{ left: 10, right: 10 }}/>
                  <recharts_1.YAxis axisLine={false} tickLine={false} width={48} tick={{
                fill: '#320A8D',
                fontSize: '10px',
                fontFamily: 'Inter',
                fontWeight: 500,
            }} tickFormatter={(value) => (0, numeral_1.default)(value).format('0.[00] a').toUpperCase()}/>
                  <recharts_1.Tooltip cursor={{ fill: '#dadef0' }} content={<CustomTooltip />}/>
                  <recharts_1.Area type="monotone" dataKey="value" stroke="#320A8D" fill="url(#paint0_linear_4037_63345)"/>
                </recharts_1.AreaChart>
              </recharts_1.ResponsiveContainer>
            </material_1.Grid>
          </material_1.Grid>
          <TooltipIcon_1.TooltipIcon position={isMobile ? 'topRight' : 'bottomLeft'} title={tooltips_1.TOOLTIPS.SOLVED_TASKS}/>
        </>)}
    </Cards_1.CardContainer>);
};
exports.SolvedTasksView = SolvedTasksView;
