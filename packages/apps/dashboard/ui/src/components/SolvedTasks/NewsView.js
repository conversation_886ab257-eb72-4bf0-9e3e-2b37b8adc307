"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.NewsView = void 0;
const material_1 = require("@mui/material");
const react_loading_skeleton_1 = __importStar(require("react-loading-skeleton"));
const Cards_1 = require("../Cards");
const useHumanProtocolNews_1 = require("src/hooks/useHumanProtocolNews");
const NewsView = () => {
    const { data, isLoading } = (0, useHumanProtocolNews_1.useHumanProtocolNews)();
    return (<Cards_1.CardContainer sxProps={{ padding: 0 }}>
      {isLoading ? (<material_1.Box sx={{ p: 2 }}>
          <material_1.Box sx={{ px: 2 }}>
            <material_1.Box sx={{ mb: '20px' }}>
              <react_loading_skeleton_1.SkeletonTheme baseColor="rgba(0, 0, 0, 0.1)" highlightColor="rgba(0, 0, 0, 0.18)">
                <react_loading_skeleton_1.default count={1} width="72px" height="32px"/>
              </react_loading_skeleton_1.SkeletonTheme>
            </material_1.Box>
            <material_1.Box sx={{ mb: '14px' }}>
              <react_loading_skeleton_1.SkeletonTheme baseColor="rgba(0, 0, 0, 0.1)" highlightColor="rgba(0, 0, 0, 0.18)">
                <react_loading_skeleton_1.default count={1} width="100%" height="72px"/>
              </react_loading_skeleton_1.SkeletonTheme>
            </material_1.Box>
            <material_1.Box sx={{ mb: '24px' }}>
              <react_loading_skeleton_1.SkeletonTheme baseColor="rgba(0, 0, 0, 0.1)" highlightColor="rgba(0, 0, 0, 0.18)">
                <react_loading_skeleton_1.default count={1} width="100%" height="40px"/>
              </react_loading_skeleton_1.SkeletonTheme>
            </material_1.Box>
          </material_1.Box>
          <material_1.Box>
            <react_loading_skeleton_1.SkeletonTheme baseColor="rgba(0, 0, 0, 0.1)" highlightColor="rgba(0, 0, 0, 0.18)">
              <react_loading_skeleton_1.default count={1} width="100%" height="200px"/>
            </react_loading_skeleton_1.SkeletonTheme>
          </material_1.Box>
        </material_1.Box>) : (<material_1.CardActionArea href={data?.link ?? ''} target="_blank" sx={{ height: '100%' }}>
          <material_1.Box sx={{
                padding: '16px',
                display: 'flex',
                flexDirection: 'column',
                justifyContent: 'space-between',
                height: '100%',
                boxSizing: 'border-box',
            }}>
            <material_1.Box sx={{
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'flex-start',
                px: 2,
            }}>
              <material_1.Box sx={{
                borderRadius: '8px',
                border: '1px solid rgba(203, 207, 232, 0.80)',
                background: '#fff',
                boxShadow: '0px 1px 5px 0px rgba(233, 235, 250, 0.20), 0px 2px 2px 0px rgba(233, 235, 250, 0.50), 0px 3px 1px -2px #E9EBFA',
                color: '#320A8D',
                fontSize: '12px',
                letterSpacing: '0.4px',
                lineHeight: '266%',
                mb: '20px',
                width: '72px',
                height: '32px',
                textAlign: 'center',
                boxSizing: 'border-box',
            }}>
                NEWS
              </material_1.Box>
              <material_1.Typography sx={{
                lineHeight: '150%',
                fontSize: '24px',
                maxWidth: '375px',
            }} color="primary" mb="14px">
                {data?.title}
              </material_1.Typography>
              <material_1.Typography sx={{
                lineHeight: '143%',
                fontSize: '14px',
                maxWidth: '375px',
                letterSpacing: '0.17px',
            }} color="primary" mb="24px">
                {data?.description}
              </material_1.Typography>
            </material_1.Box>
            {data?.image && (<material_1.Box component="img" src={data?.image} alt="news" sx={{
                    borderRadius: '8px',
                    overflow: 'hidden',
                    width: '100%',
                    mx: 0,
                }}/>)}
          </material_1.Box>
        </material_1.CardActionArea>)}
    </Cards_1.CardContainer>);
};
exports.NewsView = NewsView;
