import SvgIcon, { SvgIconProps } from '@mui/material/SvgIcon';
import { FC } from 'react';

export const OkxIcon: FC<SvgIconProps> = (props) => {
  return (
    <SvgIcon {...props} viewBox="0 0 832 316" fill="none">
      <path
        d="M261.67 46H47.6308C46.3108 46 45.0448 46.524 44.1118 47.458C43.1778 48.391 42.6538 49.657 42.6538 50.978V265.017C42.6538 266.337 43.1778 267.603 44.1118 268.536C45.0448 269.47 46.3108 269.994 47.6308 269.994H261.67C262.991 269.994 264.257 269.47 265.19 268.536C266.124 267.603 266.648 266.337 266.648 265.017V50.978C266.648 49.657 266.124 48.391 265.19 47.458C264.257 46.524 262.991 46 261.67 46ZM191.983 190.352C191.983 191.672 191.459 192.938 190.525 193.872C189.592 194.805 188.326 195.33 187.006 195.33H122.296C120.976 195.33 119.71 194.805 118.776 193.872C117.843 192.938 117.319 191.672 117.319 190.352V125.642C117.319 124.322 117.843 123.056 118.776 122.123C119.71 121.189 120.976 120.665 122.296 120.665H187.006C188.326 120.665 189.592 121.189 190.525 122.123C191.459 123.056 191.983 124.322 191.983 125.642V190.352Z"
        fill="black"
      />
      <path
        d="M709.783 120.673H645.074C642.325 120.673 640.096 122.901 640.096 125.65V190.36C640.096 193.109 642.325 195.338 645.074 195.338H709.783C712.532 195.338 714.761 193.109 714.761 190.36V125.65C714.761 122.901 712.532 120.673 709.783 120.673Z"
        fill="black"
      />
      <path
        d="M635.088 46.0059H570.379C567.63 46.0059 565.401 48.2339 565.401 50.9839V115.693C565.401 118.442 567.63 120.671 570.379 120.671H635.088C637.838 120.671 640.066 118.442 640.066 115.693V50.9839C640.066 48.2339 637.838 46.0059 635.088 46.0059Z"
        fill="black"
      />
      <path
        d="M784.418 46.0059H719.708C716.959 46.0059 714.73 48.2339 714.73 50.9839V115.693C714.73 118.442 716.959 120.671 719.708 120.671H784.418C787.167 120.671 789.395 118.442 789.395 115.693V50.9839C789.395 48.2339 787.167 46.0059 784.418 46.0059Z"
        fill="black"
      />
      <path
        d="M635.088 195.334H570.379C567.63 195.334 565.401 197.562 565.401 200.311V265.021C565.401 267.77 567.63 269.999 570.379 269.999H635.088C637.838 269.999 640.066 267.77 640.066 265.021V200.311C640.066 197.562 637.838 195.334 635.088 195.334Z"
        fill="black"
      />
      <path
        d="M784.418 195.334H719.708C716.959 195.334 714.73 197.562 714.73 200.311V265.021C714.73 267.77 716.959 269.999 719.708 269.999H784.418C787.167 269.999 789.395 267.77 789.395 265.021V200.311C789.395 197.562 787.167 195.334 784.418 195.334Z"
        fill="black"
      />
      <path
        d="M522.997 46.0059H458.288C455.539 46.0059 453.31 48.2339 453.31 50.9839V115.693C453.31 118.442 455.539 120.671 458.288 120.671H522.997C525.746 120.671 527.975 118.442 527.975 115.693V50.9839C527.975 48.2339 525.746 46.0059 522.997 46.0059Z"
        fill="black"
      />
      <path
        d="M522.997 195.334H458.288C455.539 195.334 453.31 197.562 453.31 200.311V265.021C453.31 267.77 455.539 269.999 458.288 269.999H522.997C525.746 269.999 527.975 267.77 527.975 265.021V200.311C527.975 197.562 525.746 195.334 522.997 195.334Z"
        fill="black"
      />
      <path
        d="M453.31 125.586C453.31 124.266 452.785 123 451.852 122.067C450.918 121.133 449.652 120.609 448.332 120.609H378.645V50.978C378.645 49.657 378.12 48.391 377.187 47.458C376.253 46.524 374.987 46 373.667 46H308.958C307.637 46 306.371 46.524 305.438 47.458C304.504 48.391 303.98 49.657 303.98 50.978V264.905C303.98 266.225 304.504 267.491 305.438 268.424C306.371 269.358 307.637 269.882 308.958 269.882H373.667C374.987 269.882 376.253 269.358 377.187 268.424C378.12 267.491 378.645 266.225 378.645 264.905V195.274H448.332C449.652 195.274 450.918 194.749 451.852 193.816C452.785 192.882 453.31 191.616 453.31 190.296V125.586Z"
        fill="black"
      />
    </SvgIcon>
  );
};
