import SvgIcon, { SvgIconProps } from '@mui/material/SvgIcon';
import { FC } from 'react';

export const DiscordIcon: FC<SvgIconProps> = (props) => {
  return (
    <SvgIcon width="25" height="20" viewBox="0 0 25 20" fill="none" {...props}>
      <path
        opacity="0.8"
        d="M21.1774 1.675C19.5591 0.8925 17.8287 0.323815 16.0197 0C15.7975 0.415119 15.538 0.973462 15.359 1.41762C13.4359 1.11874 11.5306 1.11874 9.64287 1.41762C9.46395 0.973462 9.1985 0.415119 8.97434 0C7.16334 0.323815 5.431 0.894589 3.8127 1.67914C0.548586 6.77669 -0.336262 11.7476 0.106163 16.648C2.2711 18.3188 4.36918 19.3338 6.43185 19.9979C6.94113 19.2736 7.39535 18.5035 7.78664 17.692C7.04141 17.3994 6.32763 17.0382 5.6532 16.6189C5.83213 16.482 6.00714 16.3387 6.17623 16.1914C10.2898 18.1798 14.7593 18.1798 18.8237 16.1914C18.9948 16.3387 19.1697 16.482 19.3467 16.6189C18.6703 17.0403 17.9546 17.4014 17.2093 17.6941C17.6006 18.5035 18.0529 19.2757 18.5641 20C20.6288 19.3358 22.7288 18.3209 24.8937 16.648C25.4129 10.9672 24.0069 6.04194 21.1774 1.675ZM8.34706 13.6343C7.11221 13.6343 6.09953 12.4429 6.09953 10.9921C6.09953 9.54132 7.09059 8.34788 8.34706 8.34788C9.60357 8.34788 10.6162 9.53923 10.5946 10.9921C10.5965 12.4429 9.60357 13.6343 8.34706 13.6343ZM16.6529 13.6343C15.418 13.6343 14.4053 12.4429 14.4053 10.9921C14.4053 9.54132 15.3963 8.34788 16.6529 8.34788C17.9093 8.34788 18.922 9.53923 18.9004 10.9921C18.9004 12.4429 17.9093 13.6343 16.6529 13.6343Z"
        fill="#858EC6"
      />
    </SvgIcon>
  );
};
