import SvgIcon, { SvgIconProps } from '@mui/material/SvgIcon';
import { FC } from 'react';

export const BitfinexIcon: FC<SvgIconProps> = (props) => {
  return (
    <SvgIcon width="19" height="19" viewBox="0 0 19 19" fill="none" {...props}>
      <path
        d="M0.0153167 11.8521C-0.143713 9.27857 0.937693 6.41785 3.1632 4.19234C8.00316 -0.647615 18.0829 0.0303066 18.1402 0.0348503C18.1129 0.0748349 10.7439 10.758 2.01183 11.7576C1.33754 11.8349 0.670519 11.8658 0.0153167 11.8521Z"
        fill="#320A8D"
      />
      <path
        d="M1.0376 15.1417C1.2875 15.5397 1.58195 15.9114 1.92272 16.2522C4.91067 19.2401 10.3104 18.6849 13.9826 15.0127C18.8371 10.1582 18.1401 0.03479 18.1401 0.03479C18.1138 0.094767 12.8312 11.9348 4.43629 14.5174C3.29673 14.8682 2.15444 15.0717 1.0376 15.1417Z"
        fill="#320A8D"
      />
    </SvgIcon>
  );
};
