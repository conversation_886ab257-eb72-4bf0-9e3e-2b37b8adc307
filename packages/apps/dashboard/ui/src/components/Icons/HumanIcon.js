"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.HumanIcon = void 0;
const SvgIcon_1 = __importDefault(require("@mui/material/SvgIcon"));
const HumanIcon = (props) => {
    return (<SvgIcon_1.default width="12" height="16" viewBox="0 0 12 16" fill="none" {...props}>
      <path fillRule="evenodd" clipRule="evenodd" d="M9.5177 0.394193C9.15722 0.273865 8.79674 0.153536 8.44605 0.0332067C8.00538 -0.127185 7.57465 0.324013 7.5948 0.915572C7.63483 2.03846 7.67485 3.15125 7.71487 4.27414C7.72495 4.64507 7.54457 4.98602 7.26413 5.12638C6.70339 5.39702 6.13244 5.65771 5.56149 5.9184C4.99054 5.66779 4.41973 5.39702 3.85886 5.12638C3.56835 4.98602 3.39818 4.64507 3.40826 4.27414C3.44828 3.15125 3.4883 2.03846 3.52832 0.915572C3.54847 0.313927 3.11774 -0.127185 2.67708 0.0332067C2.31645 0.153536 1.96591 0.28395 1.60543 0.394193C1.31491 0.494491 1.09465 0.815275 1.06456 1.20624C0.964366 2.52972 0.854234 3.85305 0.754038 5.16645C0.723951 5.53738 0.894256 5.87833 1.17469 6.02864C1.80568 6.35951 1.74564 7.44233 1.05449 7.69293C0.744102 7.80332 0.513623 8.11401 0.483536 8.48495C0.323446 10.5001 0.163076 12.5254 0.0028464 14.5406C-0.0272404 14.9316 0.183088 15.2926 0.54371 15.4329C0.974301 15.6033 1.41511 15.7737 1.85578 15.9442C2.40671 16.1548 2.99754 15.7436 3.01755 15.1421C3.07758 13.3275 3.14769 11.5128 3.20786 9.6881C3.2178 9.31716 3.43834 8.99638 3.74873 8.87605C4.3597 8.63539 4.96074 8.39474 5.56149 8.13405C6.16239 8.38479 6.76343 8.63539 7.3744 8.87605C7.68478 8.99638 7.90533 9.31716 7.91526 9.6881C7.97544 11.5028 8.04541 13.3174 8.10558 15.1421C8.12559 15.7436 8.71641 16.1447 9.26735 15.9442C9.70802 15.7737 10.1488 15.6033 10.5794 15.4329C10.93 15.2926 11.1504 14.9316 11.1203 14.5406C10.9601 12.5254 10.7997 10.5001 10.6396 8.48495C10.6095 8.11401 10.3892 7.80332 10.0686 7.69293C9.37748 7.45242 9.31745 6.35951 9.94843 6.02864C10.2388 5.87833 10.3992 5.52743 10.3691 5.16645C10.2689 3.84311 10.1588 2.51963 10.0586 1.20624C10.0285 0.815275 9.80822 0.484405 9.5177 0.394193Z" fill="currentColor"/>
    </SvgIcon_1.default>);
};
exports.HumanIcon = HumanIcon;
