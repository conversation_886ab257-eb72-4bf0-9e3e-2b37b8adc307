"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CHAIN_ICONS = void 0;
const sdk_1 = require("@human-protocol/sdk");
const AvalancheIcon_1 = require("./AvalancheIcon");
const BinanceSmartChainIcon_1 = require("./BinanceSmartChainIcon");
const CeloIcon_1 = require("./CeloIcon");
const EthereumIcon_1 = require("./EthereumIcon");
const HumanIcon_1 = require("./HumanIcon");
const MoonbaseAlphaIcon_1 = require("./MoonbaseAlphaIcon");
const MoonbeamIcon_1 = require("./MoonbeamIcon");
const OkxIcon_1 = require("./OkxIcon");
const PolygonIcon_1 = require("./PolygonIcon");
const XLayerIcon_1 = require("./XLayerIcon");
exports.CHAIN_ICONS = {
    [sdk_1.ChainId.ALL]: <HumanIcon_1.HumanIcon />,
    [sdk_1.ChainId.MAINNET]: <EthereumIcon_1.EthereumIcon />,
    [sdk_1.ChainId.RINKEBY]: <EthereumIcon_1.EthereumIcon />,
    [sdk_1.ChainId.GOERLI]: <EthereumIcon_1.EthereumIcon />,
    [sdk_1.ChainId.SEPOLIA]: <EthereumIcon_1.EthereumIcon />,
    [sdk_1.ChainId.POLYGON]: <PolygonIcon_1.PolygonIcon />,
    [sdk_1.ChainId.POLYGON_MUMBAI]: <PolygonIcon_1.PolygonIcon />,
    [sdk_1.ChainId.POLYGON_AMOY]: <PolygonIcon_1.PolygonIcon />,
    [sdk_1.ChainId.BSC_MAINNET]: <BinanceSmartChainIcon_1.BinanceSmartChainIcon />,
    [sdk_1.ChainId.BSC_TESTNET]: <BinanceSmartChainIcon_1.BinanceSmartChainIcon />,
    [sdk_1.ChainId.MOONBEAM]: <MoonbeamIcon_1.MoonbeamIcon />,
    [sdk_1.ChainId.MOONBASE_ALPHA]: <MoonbaseAlphaIcon_1.MoonbaseAlphaIcon />,
    [sdk_1.ChainId.AVALANCHE]: <AvalancheIcon_1.AvalancheIcon />,
    [sdk_1.ChainId.AVALANCHE_TESTNET]: <AvalancheIcon_1.AvalancheIcon />,
    [sdk_1.ChainId.CELO]: <CeloIcon_1.CeloIcon />,
    [sdk_1.ChainId.CELO_ALFAJORES]: <CeloIcon_1.CeloIcon />,
    [sdk_1.ChainId.XLAYER_TESTNET]: <OkxIcon_1.OkxIcon />,
    [sdk_1.ChainId.XLAYER]: <XLayerIcon_1.XLayerIcon />,
};
