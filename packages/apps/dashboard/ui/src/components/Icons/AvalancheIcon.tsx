import SvgIcon, { SvgIconProps } from '@mui/material/SvgIcon';
import { FC } from 'react';

export const AvalancheIcon: FC<SvgIconProps> = (props) => {
  return (
    <SvgIcon width="16" height="14" viewBox="0 0 16 14" fill="none" {...props}>
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M3.95284 13.9997H1.30457C0.748105 13.9997 0.473205 13.9997 0.305573 13.8923C0.12457 13.7747 0.0139269 13.58 0.000512986 13.3652C-0.00952634 13.1671 0.127903 12.9254 0.402803 12.4419L6.94173 0.898892C7.21997 0.408722 7.36077 0.163658 7.53844 0.0729996C7.72953 -0.0243332 7.95748 -0.0243332 8.14856 0.0729996C8.32623 0.163658 8.46704 0.408722 8.74527 0.898892L10.0895 3.24899L10.0964 3.26099C10.3969 3.78686 10.5493 4.05351 10.6158 4.33338C10.6896 4.6389 10.6896 4.96119 10.6158 5.2667C10.5488 5.54873 10.3979 5.81733 10.0929 6.35114L6.65814 12.4319L6.64924 12.4474C6.34672 12.9776 6.19343 13.2463 5.98095 13.4491C5.74967 13.6707 5.47144 13.8318 5.16638 13.9225C4.88814 13.9997 4.57638 13.9997 3.95284 13.9997ZM10.6406 14H14.4354C14.9953 14 15.2768 14 15.4445 13.8892C15.6255 13.7717 15.7394 13.5736 15.7495 13.3588C15.7592 13.1672 15.6247 12.9348 15.3612 12.4795C15.3522 12.464 15.3431 12.4482 15.3338 12.4322L13.4331 9.1756L13.4114 9.13898C13.1443 8.68657 13.0094 8.45816 12.8364 8.36986C12.6453 8.27249 12.4207 8.27249 12.2296 8.36986C12.0553 8.46048 11.9145 8.69887 11.6363 9.17894L9.74224 12.4355L9.73575 12.4467C9.45848 12.926 9.31991 13.1656 9.32991 13.3621C9.34333 13.577 9.45393 13.7751 9.63497 13.8926C9.79923 14 10.0808 14 10.6406 14Z"
        fill="#320A8D"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M3.95284 13.9997H1.30457C0.748105 13.9997 0.473205 13.9997 0.305573 13.8923C0.12457 13.7747 0.0139269 13.58 0.000512986 13.3652C-0.00952634 13.1671 0.127903 12.9254 0.402803 12.4419L6.94173 0.898892C7.21997 0.408722 7.36077 0.163658 7.53844 0.0729996C7.72953 -0.0243332 7.95748 -0.0243332 8.14856 0.0729996C8.32623 0.163658 8.46704 0.408722 8.74527 0.898892L10.0895 3.24899L10.0964 3.26099C10.3969 3.78686 10.5493 4.05351 10.6158 4.33338C10.6896 4.6389 10.6896 4.96119 10.6158 5.2667C10.5488 5.54873 10.3979 5.81733 10.0929 6.35114L6.65814 12.4319L6.64924 12.4474C6.34672 12.9776 6.19343 13.2463 5.98095 13.4491C5.74967 13.6707 5.47144 13.8318 5.16638 13.9225C4.88814 13.9997 4.57638 13.9997 3.95284 13.9997ZM10.6406 14H14.4354C14.9953 14 15.2768 14 15.4445 13.8892C15.6255 13.7717 15.7394 13.5736 15.7495 13.3588C15.7592 13.1672 15.6247 12.9348 15.3612 12.4795C15.3522 12.464 15.3431 12.4482 15.3338 12.4322L13.4331 9.1756L13.4114 9.13898C13.1443 8.68657 13.0094 8.45816 12.8364 8.36986C12.6453 8.27249 12.4207 8.27249 12.2296 8.36986C12.0553 8.46048 11.9145 8.69887 11.6363 9.17894L9.74224 12.4355L9.73575 12.4467C9.45848 12.926 9.31991 13.1656 9.32991 13.3621C9.34333 13.577 9.45393 13.7751 9.63497 13.8926C9.79923 14 10.0808 14 10.6406 14Z"
        fill="#320A8D"
      />
    </SvgIcon>
  );
};
