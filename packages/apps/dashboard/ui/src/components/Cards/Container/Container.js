"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Container = void 0;
const Box_1 = __importDefault(require("@mui/material/Box"));
const Container = ({ children, densed = false, sxProps = {}, ...rest }) => {
    return (<Box_1.default sx={{
            background: '#fff',
            boxSizing: 'border-box',
            boxShadow: '0px 3px 1px -2px #E9EBFA, 0px 2px 2px rgba(233, 235, 250, 0.5), 0px 1px 5px rgba(233, 235, 250, 0.2)',
            borderRadius: '16px',
            padding: densed
                ? { xs: '18px 24px', xl: '32px 40px' }
                : { xs: '26px 32px', xl: '52px 64px' },
            height: '100%',
            position: 'relative',
            ...sxProps,
        }} {...rest}>
      {children}
    </Box_1.default>);
};
exports.Container = Container;
