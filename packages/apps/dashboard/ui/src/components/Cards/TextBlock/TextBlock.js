"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TextBlock = void 0;
const material_1 = require("@mui/material");
const numeral_1 = __importDefault(require("numeral"));
const react_loading_skeleton_1 = __importStar(require("react-loading-skeleton"));
const Container_1 = require("../Container");
const TooltipIcon_1 = require("src/components/TooltipIcon");
const TextBlock = ({ title, value, component: ValueComponent, format = '0,0', changes, tooltipTitle, }) => {
    return (<Container_1.Container>
      <material_1.Typography variant="body2" color="primary" fontWeight={600}>
        {title}
      </material_1.Typography>
      {value === undefined ? (<material_1.Box sx={{ marginTop: { xs: '4px', md: 2 }, height: { xs: 50, md: 72 } }}>
          <react_loading_skeleton_1.SkeletonTheme baseColor="rgba(0, 0, 0, 0.1)" highlightColor="rgba(0, 0, 0, 0.18)">
            <react_loading_skeleton_1.default count={1} width="100%" height="100%"/>
          </react_loading_skeleton_1.SkeletonTheme>
        </material_1.Box>) : (<material_1.Box display="flex" alignItems="baseline" overflow="hidden">
          {ValueComponent ? (<ValueComponent value={value}/>) : (<material_1.Typography variant="h2" color="primary" lineHeight={1.2} marginTop={{ xs: '4px', md: 2 }} sx={{ fontSize: { xs: 40, xl: 60 } }}>
              {Number.isNaN(Number(value))
                    ? value
                    : (0, numeral_1.default)(value).format(format)}
            </material_1.Typography>)}

          {changes === undefined ? (<></>) : changes >= 0 ? (<material_1.Typography variant="caption" color="success.light" ml="4px">
              (+{Math.abs(changes).toFixed(2)}%)
            </material_1.Typography>) : (<material_1.Typography variant="caption" color="error.light" ml="4px">
              (-{Math.abs(changes).toFixed(2)}%)
            </material_1.Typography>)}
        </material_1.Box>)}
      {tooltipTitle && <TooltipIcon_1.TooltipIcon position="topRight" title={tooltipTitle}/>}
    </Container_1.Container>);
};
exports.TextBlock = TextBlock;
