"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const react_1 = require("@testing-library/react");
const react_test_renderer_1 = require("react-test-renderer");
const TextBlock_1 = require("./TextBlock");
const mock = {
    value: 'Value',
    title: 'Title',
};
describe('when rendered CardBarChart component', () => {
    it('should render passed prop `value`', () => {
        (0, react_1.render)(<TextBlock_1.TextBlock value={mock.value} title={mock.title}/>);
        expect(react_1.screen.findByLabelText(mock.value)).toBeTruthy();
    });
    it('should render passed prop `title`', () => {
        (0, react_1.render)(<TextBlock_1.TextBlock value={mock.value} title={mock.title}/>);
        expect(react_1.screen.findByLabelText(mock.title)).toBeTruthy();
    });
});
it('TextBlock component renders correctly, corresponds to the snapshot', () => {
    const tree = (0, react_test_renderer_1.create)(<TextBlock_1.TextBlock value={mock.value} title={mock.title}/>).toJSON();
    expect(tree).toMatchSnapshot();
});
