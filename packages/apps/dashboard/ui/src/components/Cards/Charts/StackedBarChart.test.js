"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const react_1 = require("@testing-library/react");
const test_utils_1 = require("react-dom/test-utils");
const react_test_renderer_1 = require("react-test-renderer");
const StackedBarChart_1 = require("./StackedBarChart");
const mock = {
    allEscrowAmount: 100,
    pendingEventCount: 100,
    series: [
        { date: '23/09', dailyEscrowAmounts: 1, dailyPendingEvents: 1 },
        { date: '24/09', dailyEscrowAmounts: 2, dailyPendingEvents: 2 },
        { date: '25/09', dailyEscrowAmounts: 3, dailyPendingEvents: 3 },
    ],
};
describe('when rendered StackedBarChart component', () => {
    it('should render passed prop `allEscrowAmount`', async () => {
        await (0, test_utils_1.act)(() => {
            (0, react_1.render)(<StackedBarChart_1.StackedBarChart series={mock.series} allEscrowAmount={mock.allEscrowAmount} pendingEventCount={mock.pendingEventCount}/>);
        });
        expect(react_1.screen.findByLabelText(mock.allEscrowAmount)).toBeTruthy();
    });
    it('should render passed prop `pendingEventCount`', async () => {
        await (0, test_utils_1.act)(() => {
            (0, react_1.render)(<StackedBarChart_1.StackedBarChart series={mock.series} allEscrowAmount={mock.allEscrowAmount} pendingEventCount={mock.pendingEventCount}/>);
        });
        expect(react_1.screen.findByLabelText(mock.pendingEventCount)).toBeTruthy();
    });
});
it('CardBarChart component renders correctly, corresponds to the snapshot', () => {
    const tree = (0, react_test_renderer_1.create)(<StackedBarChart_1.StackedBarChart series={mock.series} allEscrowAmount={mock.allEscrowAmount} pendingEventCount={mock.pendingEventCount}/>).toJSON();
    expect(tree).toMatchSnapshot();
});
