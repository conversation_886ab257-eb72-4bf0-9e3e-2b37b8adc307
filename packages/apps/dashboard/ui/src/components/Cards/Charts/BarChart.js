"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.BarChart = void 0;
const material_1 = require("@mui/material");
const numeral_1 = __importDefault(require("numeral"));
const recharts_1 = require("recharts");
const Container_1 = require("../Container");
const BarChart = ({ title, totalValue, series }) => {
    const theme = (0, material_1.useTheme)();
    return (<Container_1.Container>
      <material_1.Typography variant="body2" color="primary" fontWeight={600} mb="4px">
        {title}
      </material_1.Typography>
      {totalValue !== undefined && (<material_1.Typography variant="h2" color="primary" sx={{ fontSize: { xs: 32, md: 48, lg: 64, xl: 80 } }}>
          {(0, numeral_1.default)(totalValue).format('0,0')}
        </material_1.Typography>)}
      <material_1.Box sx={{ width: '100%', height: 190 }}>
        <recharts_1.ResponsiveContainer>
          <recharts_1.BarChart data={series} margin={{ top: 30, left: 4, right: 4 }}>
            <recharts_1.XAxis dataKey="date" type="category" axisLine={false} tickLine={false} interval="preserveStartEnd" ticks={[
            series[0]?.date,
            series[Math.ceil(series.length / 2) - 1]?.date,
            series[series.length - 1]?.date,
        ]} tick={{ fill: '#320A8D', fontSize: '12px', fontFamily: 'Inter' }} tickMargin={12} padding={{ left: 10, right: 10 }}/>
            <recharts_1.Tooltip />
            <recharts_1.Bar dataKey="value" fill={theme.palette.primary.main}/>
          </recharts_1.BarChart>
        </recharts_1.ResponsiveContainer>
      </material_1.Box>
    </Container_1.Container>);
};
exports.BarChart = BarChart;
