"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const react_1 = require("@testing-library/react");
const test_utils_1 = require("react-dom/test-utils");
const react_test_renderer_1 = require("react-test-renderer");
const BarChart_1 = require("./BarChart");
const mock = {
    title: 'Title',
    totalValue: 100,
    series: [
        { date: '23/09', value: 1 },
        { date: '24/09', value: 2 },
        { date: '25/09', value: 3 },
    ],
};
describe('when rendered BarChart component', () => {
    it('should render passed prop `title`', async () => {
        await (0, test_utils_1.act)(() => {
            (0, react_1.render)(<BarChart_1.BarChart title={mock.title} totalValue={mock.totalValue} series={mock.series}/>);
        });
        expect(react_1.screen.findByLabelText(mock.title)).toBeTruthy();
    });
    it('should render passed prop `totalValue`', async () => {
        await (0, test_utils_1.act)(() => {
            (0, react_1.render)(<BarChart_1.BarChart title={mock.title} totalValue={mock.totalValue} series={mock.series}/>);
        });
        expect(react_1.screen.findByLabelText(mock.totalValue)).toBeTruthy();
    });
});
it('BarChart component renders correctly, corresponds to the snapshot', () => {
    const tree = (0, react_test_renderer_1.create)(<BarChart_1.BarChart title={mock.title} totalValue={mock.totalValue} series={mock.series}/>).toJSON();
    expect(tree).toMatchSnapshot();
});
