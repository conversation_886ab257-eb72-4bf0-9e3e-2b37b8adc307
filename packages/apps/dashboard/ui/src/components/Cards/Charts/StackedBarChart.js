"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.StackedBarChart = void 0;
const material_1 = require("@mui/material");
const numeral_1 = __importDefault(require("numeral"));
const recharts_1 = require("recharts");
const Container_1 = require("../Container");
const CustomTooltip = ({ active, payload, label }) => {
    if (active && payload && payload.length) {
        return (<material_1.Box sx={{
                background: '#fff',
                border: '1px solid rgba(218, 222, 240, 0.8)',
                borderRadius: '16px',
                padding: '30px 38px',
            }}>
        <material_1.Typography color="text.primary" variant="body2" fontWeight={600}>
          {label}
        </material_1.Typography>
        <material_1.Box mt={2}>
          <material_1.Typography color="text.primary" variant="caption" component="p">
            Amount of Escrows
          </material_1.Typography>
          <material_1.Typography color="text.primary" variant="h6" fontWeight={500}>
            {payload[1].value}
          </material_1.Typography>
        </material_1.Box>
        <material_1.Box mt={2}>
          <material_1.Typography color="secondary.main" variant="caption" component="p">
            Escrows Pending Events
          </material_1.Typography>
          <material_1.Typography color="text.primary" variant="h6" fontWeight={500}>
            {payload[0].value}
          </material_1.Typography>
        </material_1.Box>
      </material_1.Box>);
    }
    return null;
};
const StackedBarChart = ({ series, allEscrowAmount, pendingEventCount, }) => {
    const theme = (0, material_1.useTheme)();
    return (<Container_1.Container>
      <material_1.Grid container>
        <material_1.Grid item container justifyContent="center" xs={12} sm={12} md={4}>
          <material_1.Grid item xs={12} sm={6} md={12}>
            <material_1.Typography variant="body2" color="primary" fontWeight={600}>
              Amount of Escrows
            </material_1.Typography>
            <material_1.Typography color="primary" variant="h2" lineHeight={1} mt={3} sx={{ fontSize: { xs: 32, md: 48, lg: 64, xl: 80 } }}>
              {(0, numeral_1.default)(allEscrowAmount).format('0,0')}
            </material_1.Typography>
          </material_1.Grid>
          <material_1.Grid item xs={12} sm={6} md={12}>
            <material_1.Typography variant="body2" color="secondary" fontWeight={600}>
              All Escrows Pending Events
            </material_1.Typography>
            <material_1.Typography color="primary" variant="h2" lineHeight={1} mt={3} sx={{ fontSize: { xs: 32, md: 48, lg: 64, xl: 80 } }}>
              {(0, numeral_1.default)(pendingEventCount).format('0,0')}
            </material_1.Typography>
          </material_1.Grid>
        </material_1.Grid>
        <material_1.Grid item xs={12} sm={12} md={8}>
          <material_1.Box sx={{ width: '100%', height: 362 }}>
            <recharts_1.ResponsiveContainer>
              <recharts_1.BarChart data={series} margin={{ top: 30, left: 4, right: 4 }}>
                <recharts_1.XAxis dataKey="date" type="category" axisLine={false} tickLine={false} interval="preserveStartEnd" ticks={[series[0]?.date, series[series.length - 1]?.date]} tick={{
            fill: '#320A8D',
            fontSize: '12px',
            fontFamily: 'Inter',
        }} tickMargin={12} padding={{ left: 10, right: 10 }}/>
                <recharts_1.Tooltip cursor={{ fill: '#dadef0' }} content={<CustomTooltip />}/>
                <recharts_1.Bar dataKey="dailyPendingEvents" stackId="a" fill={theme.palette.secondary.main}/>
                <recharts_1.Bar dataKey="dailyEscrowAmounts" stackId="a" fill={theme.palette.primary.main}/>
              </recharts_1.BarChart>
            </recharts_1.ResponsiveContainer>
          </material_1.Box>
        </material_1.Grid>
      </material_1.Grid>
    </Container_1.Container>);
};
exports.StackedBarChart = StackedBarChart;
