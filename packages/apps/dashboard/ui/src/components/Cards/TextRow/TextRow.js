"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.TextRow = void 0;
const material_1 = require("@mui/material");
const TextRow = ({ label, value, minWidth = 130 }) => {
    return (<material_1.Stack direction="row" spacing={3}>
      <material_1.Typography variant="body2" color="text.secondary" sx={{ minWidth }}>
        {label ? `${label} :` : ''}
      </material_1.Typography>
      <material_1.Typography variant="body2" color="primary" sx={{ overflow: 'hidden', textOverflow: 'ellipsis' }}>
        {value}
      </material_1.Typography>
    </material_1.Stack>);
};
exports.TextRow = TextRow;
