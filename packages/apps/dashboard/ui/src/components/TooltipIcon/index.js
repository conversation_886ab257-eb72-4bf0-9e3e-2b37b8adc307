"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TooltipIcon = void 0;
const material_1 = require("@mui/material");
const styles_1 = require("@mui/material/styles");
const Tooltip_1 = __importStar(require("@mui/material/Tooltip"));
const react_1 = __importDefault(require("react"));
const BootstrapTooltip = (0, styles_1.styled)(({ className, ...props }) => (<Tooltip_1.default {...props} arrow classes={{ popper: className }}/>))(({ theme }) => ({
    [`& .${Tooltip_1.tooltipClasses.arrow}`]: {
        color: '#320a8d',
    },
    [`& .${Tooltip_1.tooltipClasses.tooltip}`]: {
        backgroundColor: '#320a8d',
        boxShadow: '0px 1px 5px 0px rgba(233, 235, 250, 0.20), 0px 2px 2px 0px rgba(233, 235, 250, 0.50), 0px 3px 1px -2px #E9EBFA',
        borderRadius: '8px',
        color: '#fff',
        fontSize: '14px',
        lineHeight: '140%',
        padding: '22px 28px',
    },
}));
const TooltipIcon = ({ title, position = 'bottomLeft', }) => {
    return (<BootstrapTooltip arrow title={title}>
      <material_1.Box sx={{
            width: { xs: '24px', md: '32px' },
            height: { xs: '24px', md: '32px' },
            borderRadius: '50%',
            border: '1px solid rgba(203, 207, 232, 0.80)',
            boxSizing: 'border-box',
            boxShadow: '0px 1px 5px 0px rgba(233, 235, 250, 0.20), 0px 2px 2px 0px rgba(233, 235, 250, 0.50), 0px 3px 1px -2px #E9EBFA',
            background: '#fff',
            color: '#858EC6',
            fontSize: 14,
            lineHeight: '157%',
            fontWeight: 600,
            letterSpacing: '0.1px',
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            cursor: 'pointer',
            position: 'absolute',
            top: position === 'topRight' ? '24px' : 'auto',
            right: position === 'topRight' ? { xs: '22px', md: '32px' } : 'auto',
            bottom: position === 'bottomLeft' ? '16px' : 'auto',
            left: position === 'bottomLeft' ? { xs: '24px', md: '30px' } : 'auto',
        }}>
        i
      </material_1.Box>
    </BootstrapTooltip>);
};
exports.TooltipIcon = TooltipIcon;
