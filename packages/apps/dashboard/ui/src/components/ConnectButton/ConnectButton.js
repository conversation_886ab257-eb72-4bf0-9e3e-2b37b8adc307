"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ConnectButton = void 0;
const sdk_1 = require("@human-protocol/sdk");
const icons_material_1 = require("@mui/icons-material");
const material_1 = require("@mui/material");
const styles_1 = require("@mui/material/styles");
const react_1 = require("react");
const wagmi_1 = require("wagmi");
const Icons_1 = require("../Icons");
const WalletModal_1 = require("../WalletModal");
const profile_svg_1 = __importDefault(require("src/assets/profile.svg"));
const useWalletBalance_1 = __importDefault(require("src/hooks/useWalletBalance"));
const utils_1 = require("src/utils");
const ProfileMenu = (0, styles_1.styled)((props) => (<material_1.Menu elevation={0} anchorOrigin={{
        vertical: 'bottom',
        horizontal: 'right',
    }} transformOrigin={{
        vertical: 'top',
        horizontal: 'right',
    }} {...props}/>))(({ theme }) => ({
    '& .MuiPaper-root': {
        borderRadius: '16px',
        boxShadow: '0px 5px 5px -3px rgba(203, 207, 232, 0.5), 0px 8px 20px 1px rgba(133, 142, 198, 0.1), 0px 3px 64px 2px rgba(233, 235, 250, 0.2)',
        padding: 24,
    },
}));
const ConnectButton = () => {
    const { address } = (0, wagmi_1.useAccount)();
    const chainId = (0, wagmi_1.useChainId)();
    const [anchorEl, setAnchorEl] = (0, react_1.useState)(null);
    const open = Boolean(anchorEl);
    const handleClick = (event) => {
        setAnchorEl(event.currentTarget);
    };
    const handleClose = () => {
        setAnchorEl(null);
    };
    const [modalOpen, setModalOpen] = (0, react_1.useState)(false);
    const toggleWalletModal = () => setModalOpen(!modalOpen);
    const walletBalance = (0, useWalletBalance_1.default)();
    const network = sdk_1.NETWORKS[chainId];
    const accountUrl = `${network?.scanUrl}/address/${address}`;
    const tokenUrl = `${network?.scanUrl}/token/${network?.hmtAddress}`;
    if (!address) {
        return (<>
        <material_1.Button sx={{
                borderRadius: '4px',
                padding: '6px 16px',
                lineHeight: '24px',
            }} variant="outlined" color="primary" onClick={toggleWalletModal}>
          Connect Wallet
        </material_1.Button>
        <WalletModal_1.WalletModal open={modalOpen} onClose={toggleWalletModal}/>
      </>);
    }
    return (<>
      <material_1.Button id="profile-button" sx={{ background: '#E1E2F7', py: 1, borderRadius: '40px', pr: 2 }} onClick={handleClick} aria-controls={open ? 'profile-menu' : undefined} aria-haspopup="true" aria-expanded={open ? 'true' : undefined} endIcon={<icons_material_1.ArrowDropDown />}>
        <img src={profile_svg_1.default} alt="profile"/>
        <material_1.Typography variant="body2" fontWeight={600} sx={{ ml: 1 }}>
          {(0, utils_1.shortenAddress)(address)}
        </material_1.Typography>
      </material_1.Button>
      <ProfileMenu id="profile-menu" anchorEl={anchorEl} open={open} onClose={handleClose} MenuListProps={{
            'aria-labelledby': 'profile-button',
        }}>
        <material_1.Box sx={{ display: 'flex', alignItems: 'center' }}>
          <material_1.Box sx={{ display: 'flex', alignItems: 'center' }}>
            <img src={profile_svg_1.default} alt="profile"/>
            <material_1.Typography variant="body2" fontWeight={600} sx={{ ml: 1 }}>
              {(0, utils_1.shortenAddress)(address)}
            </material_1.Typography>
          </material_1.Box>
          <material_1.Stack direction="row" sx={{ ml: 3 }} spacing="10px">
            <material_1.IconButton color="primary" sx={{ background: '#F6F7FE' }} onClick={() => navigator.clipboard.writeText(address)}>
              <Icons_1.CopyLinkIcon />
            </material_1.IconButton>
            <material_1.IconButton color="primary" sx={{ background: '#F6F7FE' }} onClick={() => window.open(accountUrl)}>
              <Icons_1.OpenInNewIcon />
            </material_1.IconButton>
            <material_1.IconButton color="primary" sx={{ background: '#F6F7FE' }} onClick={() => window.open(tokenUrl)}>
              <Icons_1.OpenInNewIcon />
            </material_1.IconButton>
          </material_1.Stack>
        </material_1.Box>
        <material_1.Typography variant="h4" fontWeight={600} textAlign="center" mt={4}>
          {walletBalance?.formatted} HMT
        </material_1.Typography>
        <material_1.Typography color="text.secondary" variant="h5" textAlign="center" mt={1}>
          {walletBalance?.formatted && walletBalance?.usdPrice
            ? `$ ${Number(walletBalance.formatted) * walletBalance.usdPrice}`
            : ''}
        </material_1.Typography>
        {/* <Stack sx={{ mt: 8 }} spacing={1.5}>
          <Button variant="outlined">Profile</Button>
          <Button variant="outlined">Another Option</Button>
        </Stack> */}
      </ProfileMenu>
    </>);
};
exports.ConnectButton = ConnectButton;
