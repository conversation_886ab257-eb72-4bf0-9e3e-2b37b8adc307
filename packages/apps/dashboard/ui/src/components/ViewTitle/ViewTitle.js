"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ViewTitle = void 0;
const material_1 = require("@mui/material");
const ViewTitle = ({ title, iconUrl }) => (<material_1.Box display="flex" alignItems="center" height={{ xs: '50px', md: '92px' }} marginLeft={{ xs: '-16px', md: '-32px' }}>
    <material_1.Box height="100%">
      <material_1.Box component="img" src={iconUrl} alt={title} sx={{ width: { xs: '80px', md: '149px' } }}/>
    </material_1.Box>
    <material_1.Typography variant="h4" color="primary" whiteSpace="nowrap" sx={{ ml: '-1px' }}>
      {title}
    </material_1.Typography>
  </material_1.Box>);
exports.ViewTitle = ViewTitle;
