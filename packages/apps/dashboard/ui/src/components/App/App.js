"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.App = void 0;
const styles_1 = require("@mui/material/styles");
const react_router_dom_1 = require("react-router-dom");
const Layout_1 = require("../Layout");
const providers_1 = require("src/providers");
const routes_1 = require("src/routes");
const theme_1 = __importDefault(require("src/theme"));
const App = () => {
    return (<providers_1.WagmiProvider>
      <providers_1.QueryClientProvider>
        <styles_1.ThemeProvider theme={theme_1.default}>
          <providers_1.NotificationProvider>
            <react_router_dom_1.BrowserRouter>
              <Layout_1.Layout>
                <react_router_dom_1.Routes>
                  {routes_1.routes.map((route) => (<react_router_dom_1.Route key={route.key} path={route.path} element={<route.component />}/>))}
                </react_router_dom_1.Routes>
              </Layout_1.Layout>
            </react_router_dom_1.BrowserRouter>
          </providers_1.NotificationProvider>
        </styles_1.ThemeProvider>
      </providers_1.QueryClientProvider>
    </providers_1.WagmiProvider>);
};
exports.App = App;
