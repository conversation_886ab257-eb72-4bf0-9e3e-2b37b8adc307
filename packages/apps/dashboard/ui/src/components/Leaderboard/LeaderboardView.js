"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.LeaderboardView = void 0;
const sdk_1 = require("@human-protocol/sdk");
const icons_material_1 = require("@mui/icons-material");
const material_1 = require("@mui/material");
const react_1 = require("react");
const react_router_dom_1 = require("react-router-dom");
const constants_1 = require("src/constants");
const hooks_1 = require("src/state/leader/hooks");
const utils_1 = require("src/utils");
const LeaderboardView = ({ showAll = true, filterOpen = false, openFilter, closeFilter, }) => {
    const theme = (0, material_1.useTheme)();
    const isMobile = (0, material_1.useMediaQuery)(theme.breakpoints.down('md'));
    const [selectedRoles, setSelectedRoles] = (0, react_1.useState)(constants_1.ROLES);
    const [selectedNetworks, setSelectedNetworks] = (0, react_1.useState)(constants_1.V2_SUPPORTED_CHAIN_IDS);
    const leaders = (0, hooks_1.useLeadersByChainID)();
    const navigate = (0, react_router_dom_1.useNavigate)();
    const displayRows = (0, react_1.useMemo)(() => {
        if (!leaders)
            return [];
        if (!showAll)
            return leaders.slice(0, 5);
        return leaders.filter((s) => selectedNetworks.includes(s.chainId) &&
            (selectedRoles.includes(s.role) || !s.role));
    }, [showAll, selectedRoles, selectedNetworks, leaders]);
    const handleRoleCheckbox = (role) => (e) => {
        if (e.target.checked) {
            setSelectedRoles([...selectedRoles, role]);
        }
        else {
            setSelectedRoles(selectedRoles.filter((r) => r !== role));
        }
    };
    const handleNetworkCheckbox = (chainId) => (e) => {
        if (e.target.checked) {
            setSelectedNetworks([...selectedNetworks, chainId]);
        }
        else {
            setSelectedNetworks(selectedNetworks.filter((id) => id !== chainId));
        }
    };
    const handleClickLeader = (chainId, address) => {
        navigate(`/leader/${chainId}/${address}`);
    };
    const renderFilter = (isMobile = false) => {
        return (<>
        <material_1.Box>
          <material_1.Typography color="textSecondary" variant="body2" sx={{ pl: isMobile ? 0 : 5, py: 1 }}>
            Network
          </material_1.Typography>
          <material_1.FormGroup>
            {constants_1.V2_SUPPORTED_CHAIN_IDS.map((chainId, i) => (<material_1.FormControlLabel componentsProps={{ typography: { color: 'textPrimary' } }} control={<material_1.Checkbox onChange={handleNetworkCheckbox(chainId)} checked={selectedNetworks.includes(chainId)}/>} label={sdk_1.NETWORKS[chainId]?.title} key={chainId}/>))}
          </material_1.FormGroup>
        </material_1.Box>
        <material_1.Box>
          <material_1.Typography color="textSecondary" variant="body2" sx={{ pl: isMobile ? 0 : 5, py: 1 }}>
            Role
          </material_1.Typography>
          <material_1.FormGroup>
            {constants_1.ROLES.map((role) => (<material_1.FormControlLabel key={role} componentsProps={{ typography: { color: 'textPrimary' } }} control={<material_1.Checkbox onChange={handleRoleCheckbox(role)} checked={selectedRoles.includes(role)}/>} label={role}/>))}
          </material_1.FormGroup>
        </material_1.Box>
      </>);
    };
    return (<>
      <material_1.Grid container>
        {showAll && !isMobile && (<material_1.Grid item xs={12} md={3} lg={2}>
            {renderFilter()}
          </material_1.Grid>)}
        <material_1.Grid item xs={12} md={showAll ? 9 : 12} lg={showAll ? 10 : 12}>
          <material_1.TableContainer component={material_1.Paper} sx={{
            borderRadius: '16px',
            boxShadow: '0px 3px 1px -2px #E9EBFA, 0px 2px 2px rgba(233, 235, 250, 0.5), 0px 1px 5px rgba(233, 235, 250, 0.2);',
        }}>
            <material_1.Table sx={{
            minWidth: 650,
            th: { borderWidth: '2px', borderColor: '#cacfe8' },
            td: { borderColor: '#cacfe8' },
        }} aria-label="simple table">
              <material_1.TableHead>
                <material_1.TableRow>
                  <material_1.TableCell></material_1.TableCell>
                  <material_1.TableCell>Address</material_1.TableCell>
                  <material_1.TableCell align="left">Role</material_1.TableCell>
                  <material_1.TableCell align="left">Stake</material_1.TableCell>
                  <material_1.TableCell align="left">Reputation</material_1.TableCell>
                  <material_1.TableCell align="left">Reward</material_1.TableCell>
                </material_1.TableRow>
              </material_1.TableHead>
              <material_1.TableBody>
                {displayRows.map((staker, i) => (<material_1.TableRow key={`${staker.chainId}-${staker.address}`} sx={{
                '&:last-child td, &:last-child th': { border: 0 },
                cursor: 'pointer',
            }} onClick={() => handleClickLeader(staker.chainId, staker.address)}>
                    <material_1.TableCell>{i + 1}</material_1.TableCell>
                    <material_1.TableCell>{(0, utils_1.shortenAddress)(staker.address)}</material_1.TableCell>
                    <material_1.TableCell align="left">{staker.role}</material_1.TableCell>
                    <material_1.TableCell align="left">
                      {staker.amountStaked} HMT
                    </material_1.TableCell>
                    <material_1.TableCell align="left">{staker.reputation}</material_1.TableCell>
                    <material_1.TableCell align="left">0 HMT</material_1.TableCell>
                  </material_1.TableRow>))}
              </material_1.TableBody>
            </material_1.Table>
          </material_1.TableContainer>
        </material_1.Grid>
      </material_1.Grid>
      <material_1.Drawer anchor="right" open={filterOpen} onClose={closeFilter}>
        <material_1.Box px={2} pt={8}>
          <material_1.Box mb={10} textAlign="right">
            <material_1.IconButton onClick={closeFilter}>
              <icons_material_1.Close color="primary"/>
            </material_1.IconButton>
          </material_1.Box>
          {renderFilter(true)}
        </material_1.Box>
      </material_1.Drawer>
    </>);
};
exports.LeaderboardView = LeaderboardView;
