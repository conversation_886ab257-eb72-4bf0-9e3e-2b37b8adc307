"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.LeaderboardContainer = void 0;
const material_1 = require("@mui/material");
const react_1 = require("react");
const react_redux_1 = require("react-redux");
const Icons_1 = require("../Icons");
const ViewTitle_1 = require("../ViewTitle");
const LeaderboardView_1 = require("./LeaderboardView");
const user_svg_1 = __importDefault(require("src/assets/user.svg"));
const hooks_1 = require("src/state/leader/hooks");
const LeaderboardContainer = ({ showAll = true, }) => {
    const theme = (0, material_1.useTheme)();
    const isMobile = (0, material_1.useMediaQuery)(theme.breakpoints.down('md'));
    const [mobileFilterOpen, setMobileFilterOpen] = (0, react_1.useState)(false);
    const { leadersLoaded } = (0, react_redux_1.useSelector)((state) => state.leader);
    (0, hooks_1.useLeadersData)();
    const openMobileFilter = () => setMobileFilterOpen(true);
    const closeMobileFilter = () => setMobileFilterOpen(false);
    return (<material_1.Box mt={{ xs: 4, md: 8 }} id="leaderboard">
      <material_1.Box display="flex" alignItems="center" flexWrap="wrap">
        <ViewTitle_1.ViewTitle title="Leaderboard" iconUrl={user_svg_1.default}/>
        {!showAll && (<material_1.Button variant="outlined" sx={{ ml: { xs: 'auto', sm: 3 }, mr: { xs: 'auto', sm: 0 } }} href="/leaderboard">
            See More
          </material_1.Button>)}
        {showAll && isMobile && (<material_1.IconButton sx={{ ml: 'auto' }} onClick={openMobileFilter}>
            <Icons_1.FilterListFilledIcon />
          </material_1.IconButton>)}
      </material_1.Box>
      <material_1.Box mt={{ xs: 4, md: 8 }}>
        {leadersLoaded ? (<LeaderboardView_1.LeaderboardView showAll={showAll} filterOpen={mobileFilterOpen} openFilter={openMobileFilter} closeFilter={closeMobileFilter}/>) : (<material_1.Box display="flex" justifyContent="center" py={10}>
            <material_1.CircularProgress size={36}/>
          </material_1.Box>)}
      </material_1.Box>
    </material_1.Box>);
};
exports.LeaderboardContainer = LeaderboardContainer;
