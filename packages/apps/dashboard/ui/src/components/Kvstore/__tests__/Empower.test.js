"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const react_1 = require("@testing-library/react");
const test_utils_1 = require("react-dom/test-utils");
const react_test_renderer_1 = require("react-test-renderer");
const Empower_1 = require("../Empower");
const utils_1 = require("tests/utils");
describe('when rendered Empower component', () => {
    it('should render `text` prop', async () => {
        await (0, test_utils_1.act)(async () => {
            (0, react_1.render)(<Empower_1.Empower {...{}}/>, {
                wrapper: ({ children }) => (<utils_1.Providers>{children}</utils_1.Providers>),
            });
        });
        expect(react_1.screen.getByText(/Continue/)).toBeInTheDocument();
    });
});
it('Empower component renders correctly, corresponds to the snapshot', () => {
    const tree = (0, react_test_renderer_1.create)(<utils_1.Providers>
      <Empower_1.Empower {...{}}/>
    </utils_1.Providers>).toJSON();
    expect(tree).toMatchSnapshot();
});
