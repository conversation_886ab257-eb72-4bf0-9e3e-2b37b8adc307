"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const react_1 = require("@testing-library/react");
const test_utils_1 = require("react-dom/test-utils");
const react_test_renderer_1 = require("react-test-renderer");
const Dashboard_1 = require("../Dashboard");
describe('when rendered Dashboard component', () => {
    it('should render `text` prop', async () => {
        await (0, test_utils_1.act)(async () => {
            (0, react_1.render)(<Dashboard_1.Dashboard {...{ publicKey: '' }}/>);
        });
        expect(react_1.screen.getByText(/ETH KV Store/)).toBeInTheDocument();
    });
});
it('Dashboard component renders correctly, corresponds to the snapshot', () => {
    const tree = (0, react_test_renderer_1.create)(<Dashboard_1.Dashboard {...{ publicKey: '' }}/>).toJSON();
    expect(tree).toMatchSnapshot();
});
