"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const react_1 = require("@testing-library/react");
const test_utils_1 = require("react-dom/test-utils");
const react_test_renderer_1 = require("react-test-renderer");
const AfterConnect_1 = require("../AfterConnect");
describe('when rendered AfterConnect component', () => {
    it('should render `text` prop', async () => {
        await (0, test_utils_1.act)(async () => {
            (0, react_1.render)(<AfterConnect_1.AfterConnect {...{}}/>);
        });
        expect(react_1.screen.getByText(/ETH KV Store/)).toBeInTheDocument();
    });
});
it('AfterConnect component renders correctly, corresponds to the snapshot', () => {
    const tree = (0, react_test_renderer_1.create)(<AfterConnect_1.AfterConnect {...{}}/>).toJSON();
    expect(tree).toMatchSnapshot();
});
