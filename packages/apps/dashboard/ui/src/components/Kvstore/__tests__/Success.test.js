"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const react_1 = require("@testing-library/react");
const test_utils_1 = require("react-dom/test-utils");
const react_test_renderer_1 = require("react-test-renderer");
const Success_1 = require("../Success");
const utils_1 = require("tests/utils");
describe('when rendered Success component', () => {
    it('should render `text` prop', async () => {
        await (0, test_utils_1.act)(async () => {
            (0, react_1.render)(<Success_1.Success {...{ keys: { publicKey: '', privateKey: '' } }}/>, {
                wrapper: ({ children }) => (<utils_1.Providers>{children}</utils_1.Providers>),
            });
        });
        expect(react_1.screen.getByText(/Success!/)).toBeInTheDocument();
    });
});
it('Success component renders correctly, corresponds to the snapshot', () => {
    const tree = (0, react_test_renderer_1.create)(<utils_1.Providers>
      <Success_1.Success {...{ keys: { publicKey: '', privateKey: '' } }}/>
    </utils_1.Providers>).toJSON();
    expect(tree).toMatchSnapshot();
});
