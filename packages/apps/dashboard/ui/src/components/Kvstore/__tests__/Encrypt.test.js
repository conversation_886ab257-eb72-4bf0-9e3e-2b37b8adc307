"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const react_1 = require("@testing-library/react");
const test_utils_1 = require("react-dom/test-utils");
const react_test_renderer_1 = require("react-test-renderer");
const Encrypt_1 = require("../Encrypt");
const utils_1 = require("tests/utils");
describe('when rendered Encrypt component', () => {
    it('should render `text` prop', async () => {
        await (0, test_utils_1.act)(async () => {
            (0, react_1.render)(<Encrypt_1.Encrypt publicKey=""/>, {
                wrapper: ({ children }) => (<utils_1.Providers>{children}</utils_1.Providers>),
            });
        });
        expect(react_1.screen.getByText(/Store/)).toBeInTheDocument();
    });
});
it('Encrypt component renders correctly, corresponds to the snapshot', () => {
    const tree = (0, react_test_renderer_1.create)(<utils_1.Providers>
      <Encrypt_1.Encrypt publicKey=""/>
    </utils_1.Providers>).toJSON();
    expect(tree).toMatchSnapshot();
});
