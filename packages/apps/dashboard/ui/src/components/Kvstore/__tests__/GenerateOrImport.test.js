"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const react_1 = require("@testing-library/react");
const test_utils_1 = require("react-dom/test-utils");
const react_test_renderer_1 = require("react-test-renderer");
const GenerateOrImport_1 = require("../GenerateOrImport");
const utils_1 = require("tests/utils");
describe('when rendered GenerateOrImport component', () => {
    it('should render `text` prop', async () => {
        await (0, test_utils_1.act)(async () => {
            (0, react_1.render)(<GenerateOrImport_1.GenerateOrImport {...{}}/>, {
                wrapper: ({ children }) => (<utils_1.Providers>{children}</utils_1.Providers>),
            });
        });
        expect(react_1.screen.getByText(/Import/)).toBeInTheDocument();
    });
});
it('GenerateOrImport component renders correctly, corresponds to the snapshot', () => {
    const tree = (0, react_test_renderer_1.create)(<utils_1.Providers>
      <GenerateOrImport_1.GenerateOrImport {...{}}/>
    </utils_1.Providers>).toJSON();
    expect(tree).toMatchSnapshot();
});
