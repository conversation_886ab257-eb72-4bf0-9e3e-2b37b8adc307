"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const react_1 = require("@testing-library/react");
const react_2 = __importDefault(require("react"));
const test_utils_1 = require("react-dom/test-utils");
const react_test_renderer_1 = require("react-test-renderer");
const MainPage_1 = require("../MainPage");
const utils_1 = require("tests/utils");
describe('when rendered MainPage component', () => {
    it('should render `text` prop', async () => {
        await (0, test_utils_1.act)(async () => {
            (0, react_1.render)(<MainPage_1.MainPage />, {
                wrapper: ({ children }) => (<utils_1.Providers>{children}</utils_1.Providers>),
            });
        });
        expect(react_1.screen.getByText(/Empower HUMAN Scan/)).toBeInTheDocument();
    });
});
it('MainPage component renders correctly, corresponds to the snapshot', () => {
    const tree = (0, react_test_renderer_1.create)(<utils_1.Providers>
      <MainPage_1.MainPage />
    </utils_1.Providers>).toJSON();
    expect(tree).toMatchSnapshot();
});
