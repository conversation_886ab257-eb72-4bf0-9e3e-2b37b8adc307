"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.StoredPubkey = void 0;
const material_1 = require("@mui/material");
const react_1 = require("react");
const Alert_1 = require("../Alert");
const services_1 = require("src/services");
const StoredPubkey = ({ publicKey, setPublicKey, refetch, setStep, setPage, }) => {
    const [copy, setCopy] = (0, react_1.useState)(false);
    async function refresh() {
        try {
            const { data } = await refetch();
            setPublicKey(await (0, services_1.showIPFS)(data));
        }
        catch (e) {
            if (e instanceof Error) {
            }
        }
    }
    function importNewKey() {
        setPublicKey('');
        setStep(1);
        setPage(2);
    }
    function generateNewKey() {
        setPublicKey('');
        setStep(1);
        setPage(1);
    }
    return (<material_1.Grid item xs={12} sm={12} md={12} container direction="column" justifyContent="center" alignItems="center">
      <material_1.Snackbar anchorOrigin={{ vertical: 'top', horizontal: 'center' }} open={copy} autoHideDuration={3000} onClose={() => setCopy(false)}>
        <Alert_1.Alert onClose={() => setCopy(false)} severity="info" sx={{ width: '100%' }}>
          copied
        </Alert_1.Alert>
      </material_1.Snackbar>
      <material_1.Box sx={{ width: { xs: 1, md: '50%', lg: '50%', xl: '40%' } }}>
        <material_1.Paper>
          <material_1.Box sx={{ borderBottom: '1px solid #CBCFE6' }}>
            <material_1.Grid container direction="row" justifyContent={'space-between'} flexDirection={{ xs: 'column-reverse', md: 'row' }}>
              <material_1.Grid item>
                <material_1.Typography fontWeight={`500`} padding={`10px`}>
                  Stored Public key:
                </material_1.Typography>
              </material_1.Grid>
              <material_1.Grid item>
                <material_1.Button onClick={importNewKey} sx={{ margin: '10px' }} variant="outlined" size="small">
                  Import New Key
                </material_1.Button>
                <material_1.Button onClick={generateNewKey} sx={{ marginRight: '10px' }} variant="contained" size="small">
                  Generate New Key
                </material_1.Button>
              </material_1.Grid>
            </material_1.Grid>
          </material_1.Box>
          <material_1.Grid container direction="column" sx={{
            padding: {
                xs: '0px 80px 0px 80px',
                md: '0px 80px 0px 80px',
                lg: '0px 80px 0px 80px',
                xl: '0px 80px 0px 80px',
            },
        }}>
            <material_1.Grid item container direction="column" sx={{
            marginBottom: { lg: 10 },
        }}>
              <material_1.Box sx={{ width: { xs: 1 } }}>
                <material_1.Box className="pubkey" sx={{
            backgroundColor: '#f6f7fe',
            height: 200,
            marginTop: 2,
            overflowY: 'scroll',
            overflowWrap: 'break-word',
            padding: 4,
            borderRadius: 3,
        }}>
                  <material_1.Typography align="justify" variant="body2" color="primary">
                    {publicKey}
                  </material_1.Typography>
                </material_1.Box>
              </material_1.Box>
              <material_1.Box sx={{
            marginTop: 2,
            marginBottom: { xs: 2, sm: 2, md: 2, lg: 0 },
        }}>
                <material_1.Button onClick={() => {
            setCopy(true);
            navigator.clipboard.writeText(publicKey);
        }} size="small">
                  Copy
                </material_1.Button>
                <material_1.Button size="small" onClick={refresh}>
                  Refresh
                </material_1.Button>
              </material_1.Box>
            </material_1.Grid>
          </material_1.Grid>
        </material_1.Paper>
      </material_1.Box>
    </material_1.Grid>);
};
exports.StoredPubkey = StoredPubkey;
