"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.MainPage = void 0;
const material_1 = require("@mui/material");
const ConnectButton_1 = require("../ConnectButton");
const constants_1 = require("./constants");
const key_svg_1 = __importDefault(require("src/assets/key.svg"));
const wallet_svg_1 = __importDefault(require("src/assets/wallet.svg"));
const MainPage = () => (<material_1.Grid container>
    <material_1.Grid item xs={12} sm={6} md={5} container direction="row" justifyContent="flex-start" alignItems="center">
      <material_1.Grid item xs={12} sm={12} md={12} container direction="column" justifyContent="flex-start" alignItems="flex-start">
        <img width="100" src={key_svg_1.default} alt="lbank"/>{' '}
        <div>
          <material_1.Typography variant="h4" color="primary">
            Empower HUMAN Scan
          </material_1.Typography>
        </div>
        <div>
          <material_1.Typography variant="h4" color="primary">
            with ETH KV Store
          </material_1.Typography>
        </div>
        <material_1.Box sx={{ marginTop: 2 }}>
          <material_1.Typography variant="body2" color="primary">
            Store your public key in the blockchain, use your public key to
            encrypt or decrypt data.
          </material_1.Typography>
        </material_1.Box>
      </material_1.Grid>
    </material_1.Grid>
    <material_1.Grid item xs={12} sm={6} md={7} container direction="row" justifyContent="center">
      <material_1.Grid item xs={12} sm={12} md={12} container direction="column" justifyContent="center" alignItems="center">
        <material_1.Box>
          <material_1.Paper sx={{
        padding: { md: 2 },
        width: { xl: '46em' },
        marginBottom: 2,
    }}>
            {' '}
            <material_1.Box sx={{ width: '100%' }}>
              <material_1.Stepper sx={{ opacity: 0.2 }} activeStep={-1}>
                {constants_1.STEPS.map((label) => (<material_1.Step key={label}>
                    <material_1.StepLabel>{label}</material_1.StepLabel>
                  </material_1.Step>))}
              </material_1.Stepper>
            </material_1.Box>
          </material_1.Paper>
          <material_1.Paper>
            <material_1.Grid container direction="column">
              <material_1.Grid item container direction="column" alignItems="center" sx={{
        marginTop: { xs: 1, sm: 1, md: 10, lg: 10 },
    }}>
                {' '}
                <img width="100" src={wallet_svg_1.default} alt="lbank"/>{' '}
                <material_1.Typography sx={{ marginTop: 3 }} variant="body2" color="primary">
                  Connect your wallet to continue
                </material_1.Typography>
              </material_1.Grid>
              <material_1.Grid item container direction="row" justifyContent="center" sx={{
        marginTop: { xs: 1, sm: 1, lg: 3 },
        marginBottom: { xs: 1, sm: 1, md: 10, lg: 7 },
    }}>
                <ConnectButton_1.ConnectButton />
              </material_1.Grid>
            </material_1.Grid>
          </material_1.Paper>
        </material_1.Box>
      </material_1.Grid>
    </material_1.Grid>
  </material_1.Grid>);
exports.MainPage = MainPage;
