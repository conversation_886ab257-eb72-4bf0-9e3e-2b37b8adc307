"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Empower = void 0;
const icons_material_1 = require("@mui/icons-material");
const material_1 = require("@mui/material");
const Empower = ({ refetch, setPub<PERSON><PERSON>ey }) => {
    async function finish() {
        try {
            const { data } = await refetch();
            setPublicKey(data);
        }
        catch (e) {
            if (e instanceof Error) {
            }
        }
    }
    return (<material_1.Grid container>
      <material_1.Grid item container direction="row" justifyContent="center">
        <material_1.Grid item xs={12} sm={12} md={12} container direction="column" justifyContent="center" alignItems="center">
          <material_1.Box>
            <material_1.Paper>
              <material_1.Grid container direction="column">
                <material_1.Grid item container direction="column" alignItems="center" sx={{
            marginTop: { xs: 1, sm: 1, md: 10, lg: 10 },
            paddingLeft: 10,
            paddingRight: 10,
            marginBottom: { md: 10, lg: 10 },
        }}>
                  <icons_material_1.Done />
                  <material_1.Typography sx={{ marginTop: 3 }} variant="h6" color="primary">
                    Success!
                  </material_1.Typography>
                  <material_1.Typography sx={{ marginTop: 1 }} variant="body2" color="primary">
                    Your public key has been stored on the blockchain now, click
                    on continue to encrypt or decrypt
                  </material_1.Typography>
                  <material_1.Button onClick={finish} variant="contained" sx={{ marginTop: 2 }}>
                    Continue
                  </material_1.Button>
                </material_1.Grid>
              </material_1.Grid>
            </material_1.Paper>
          </material_1.Box>
        </material_1.Grid>
      </material_1.Grid>
    </material_1.Grid>);
};
exports.Empower = Empower;
