"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.GeneratePubkey = void 0;
const material_1 = require("@mui/material");
const lightweight_1 = require("openpgp/lightweight");
const react_1 = require("react");
const Alert_1 = require("../Alert");
const constants_1 = require("./constants");
const GeneratePubkey = ({ setStep, setPage, setKey, pubkeyExist, refetch, setPublicKey, }) => {
    const [name, setName] = (0, react_1.useState)('');
    const [email, setEmail] = (0, react_1.useState)('');
    const [passphrase, setPassphrase] = (0, react_1.useState)('');
    const [error, setError] = (0, react_1.useState)(false);
    const [errorMessage, setErrorMessage] = (0, react_1.useState)('');
    const [emailError, setEmailError] = (0, react_1.useState)(false);
    const [emailErrorMessage, setEmailErrorMessage] = (0, react_1.useState)('');
    async function goBack() {
        try {
            if (pubkeyExist) {
                const { data } = await refetch();
                setPublicKey(data);
            }
            else {
                setStep(0);
                setPage(0);
            }
        }
        catch (e) {
            if (e instanceof Error) {
                setError(true);
                setErrorMessage(e.message);
            }
        }
    }
    async function generate() {
        try {
            if (name.length === 0 || email.length === 0 || passphrase.length === 0) {
                setError(true);
                setErrorMessage('Please fill name, email, and passphrase');
                return;
            }
            if (!constants_1.PUBKEY_REGEX.test(email)) {
                setEmailError(true);
                setEmailErrorMessage("email doesn't use correct format");
                return;
            }
            setEmailError(false);
            setEmailErrorMessage('');
            setError(false);
            setErrorMessage('');
            const { publicKey, privateKey } = await (0, lightweight_1.generateKey)({
                userIDs: [{ name, email }],
                passphrase,
            });
            setKey({ publicKey, privateKey });
            setPage(1.5);
            setStep(1);
        }
        catch (e) {
            setError(true);
            if (e instanceof Error)
                setErrorMessage(e.message);
        }
    }
    return (<material_1.Paper>
      <material_1.Stack spacing={7}>
        <material_1.Snackbar anchorOrigin={{ vertical: 'top', horizontal: 'center' }} open={error} autoHideDuration={6000} onClose={() => setError(false)}>
          <Alert_1.Alert onClose={() => setError(false)} severity="error" sx={{ width: '100%' }}>
            {errorMessage}
          </Alert_1.Alert>
        </material_1.Snackbar>
        <material_1.Snackbar anchorOrigin={{ vertical: 'top', horizontal: 'center' }} open={emailError} autoHideDuration={6000} onClose={() => setEmailError(false)}>
          <Alert_1.Alert onClose={() => setEmailError(false)} severity="error" sx={{ width: '100%' }}>
            {emailErrorMessage}
          </Alert_1.Alert>
        </material_1.Snackbar>
      </material_1.Stack>

      <material_1.Grid container direction="column">
        <material_1.Grid item container direction="column" alignItems="flex-start" justifyContent="center" sx={{ paddingLeft: 5, paddingTop: 5 }}>
          {' '}
          <material_1.Typography sx={{ marginTop: 3 }} variant="body2" color="primary">
            Fill the fields to generate public key
          </material_1.Typography>
          <material_1.Grid container sx={{
            my: { md: 2, lg: 2, xl: 2 },
            paddingRight: { xs: 1, sm: 1, md: 10, lg: 12 },
        }}>
            <material_1.TextField sx={{ width: { md: 200, lg: 200, xl: 200 }, my: { xs: 2 } }} id="outlined-basic" label="Name" variant="outlined" value={name} onChange={(e) => setName(e.target.value)}/>
            <material_1.TextField sx={{
            mx: { md: 2, lg: 2, xl: 2 },
            width: { md: 200, lg: 200, xl: 200 },
            my: { xs: 2 },
        }} id="Email" label="Email" variant="outlined" value={email} onChange={(e) => setEmail(e.target.value)}/>
            <material_1.TextField sx={{ width: { md: 200, lg: 200, xl: 200 }, my: { xs: 2 } }} id="outlined-basic" label="Passphrase" variant="outlined" type="password" value={passphrase} onChange={(e) => setPassphrase(e.target.value)}/>
          </material_1.Grid>
        </material_1.Grid>

        <material_1.Grid item container direction="row" justifyContent="flex-end" alignItems="flex-end" sx={{
            marginTop: { xs: 1, sm: 1, md: 10, lg: 10 },
            paddingRight: { xs: 1, sm: 1, md: 10, lg: 10 },
            marginBottom: { xs: 1, sm: 1, md: 7, lg: 7 },
        }}>
          <material_1.Button variant="outlined" sx={{ mr: 2 }} onClick={goBack}>
            Back
          </material_1.Button>
          <material_1.Button variant="contained" sx={{ marginRight: 2 }} onClick={generate}>
            Generate Public Key
          </material_1.Button>
        </material_1.Grid>
      </material_1.Grid>
    </material_1.Paper>);
};
exports.GeneratePubkey = GeneratePubkey;
