"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AfterConnect = void 0;
const material_1 = require("@mui/material");
const react_1 = require("react");
const constants_1 = require("./constants");
const Empower_1 = require("./Empower");
const GenerateOrImport_1 = require("./GenerateOrImport");
const GeneratePubkey_1 = require("./GeneratePubkey");
const ImportPubkey_1 = require("./ImportPubkey");
const Success_1 = require("./Success");
const AfterConnect = ({ step, setStep, page, setPage, pubkeyExist, refetch, setPublicKey, }) => {
    const [key, setKey] = (0, react_1.useState)({ publicKey: '', privateKey: '' });
    return (<material_1.Grid container>
      <material_1.Grid item xs={12} sm={12} md={12} container direction="row" justifyContent="center">
        <material_1.Grid item xs={12} sm={12} md={12} container direction="column" justifyContent="center" alignItems="center">
          <material_1.Box>
            <material_1.Typography variant="h4" sx={{ marginBottom: 2 }} color="primary">
              ETH KV Store
            </material_1.Typography>
            <material_1.Paper sx={{ padding: { md: 2 }, marginBottom: 2 }}>
              {' '}
              <material_1.Box sx={{ width: '100%' }}>
                <material_1.Stepper activeStep={step}>
                  {constants_1.STEPS.map((step) => (<material_1.Step key={step}>
                      <material_1.StepLabel>{step}</material_1.StepLabel>
                    </material_1.Step>))}
                </material_1.Stepper>
              </material_1.Box>
            </material_1.Paper>

            {page === 0 && (<GenerateOrImport_1.GenerateOrImport setStep={setStep} setPage={setPage}/>)}
            {page === 1 && (<GeneratePubkey_1.GeneratePubkey refetch={refetch} setPublicKey={setPublicKey} pubkeyExist={pubkeyExist} setKey={setKey} setStep={setStep} setPage={setPage}/>)}
            {page === 1.5 && (<Success_1.Success what="generated" keys={key} setStep={setStep} setPage={setPage}/>)}
            {page === 2 && (<ImportPubkey_1.ImportPubkey refetch={refetch} setPublicKey={setPublicKey} pubkeyExist={pubkeyExist} setKey={setKey} setStep={setStep} setPage={setPage}/>)}
            {page === 2.5 && (<Success_1.Success what="imported" keys={key} setStep={setStep} setPage={setPage}/>)}
            {page === 3 && (<Empower_1.Empower refetch={refetch} setPublicKey={setPublicKey}/>)}
          </material_1.Box>
        </material_1.Grid>
      </material_1.Grid>
    </material_1.Grid>);
};
exports.AfterConnect = AfterConnect;
