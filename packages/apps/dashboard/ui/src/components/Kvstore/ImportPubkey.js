"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ImportPubkey = void 0;
const icons_material_1 = require("@mui/icons-material");
const material_1 = require("@mui/material");
const lightweight_1 = require("openpgp/lightweight");
const react_1 = require("react");
const Alert_1 = require("../Alert");
const services_1 = require("src/services");
const handleFile = async (e, setError, setKey, setPage, setStep) => {
    try {
        const pubkey = e.target.result;
        const a = await (0, lightweight_1.readKey)({ armoredKey: pubkey });
        if (a.isPrivate()) {
            setError('Error, file is not public key');
            return;
        }
        setKey({ publicKey: pubkey, privateKey: '' });
        setPage(2.5);
        setStep(1);
    }
    catch (e) {
        if (e instanceof Error)
            setError("Error, file doesn't contain public key");
    }
};
const handleChange = (ev, setError, setKey, setPage, setStep) => {
    const reader = new FileReader();
    reader.addEventListener('load', (e) => handleFile(e, setError, setKey, setPage, setStep));
    reader.readAsText(ev.target.files[0]);
};
function pickPublicKey(gpg, setKey, setPage, setStep) {
    setKey({ publicKey: gpg, privateKey: '' });
    setPage(2.5);
    setStep(1);
}
async function getGithubGPGFile(username, setError, setPublicKeys) {
    try {
        const gpg = await (0, services_1.showGithubPGP)(username);
        const parsedGPG = JSON.parse(gpg);
        if (parsedGPG.length === 0) {
            setError("Error, user doesn't have any public key");
            return;
        }
        setPublicKeys(parsedGPG);
    }
    catch (e) {
        if (e instanceof Error)
            setError("Error, can't get github GPG file");
    }
}
const ImportPubkey = ({ setStep, setPage, setKey, pubkeyExist, refetch, setPublicKey, }) => {
    const [choose, setChoose] = (0, react_1.useState)('');
    const [error, setError] = (0, react_1.useState)('');
    const [username, setUsername] = (0, react_1.useState)('');
    const [publicKeys, setPublicKeys] = (0, react_1.useState)([]);
    async function goBack() {
        try {
            if (pubkeyExist) {
                const { data } = await refetch();
                setPublicKey(data);
            }
            else {
                if (choose.trim().length !== 0) {
                    setChoose('');
                }
                else {
                    setStep(0);
                    setPage(0);
                }
            }
        }
        catch (e) {
            if (e instanceof Error) {
                setError(e.message);
            }
        }
    }
    return (<material_1.Paper>
      <material_1.Snackbar anchorOrigin={{ vertical: 'top', horizontal: 'center' }} open={error.length > 0} autoHideDuration={6000} onClose={() => setError('')}>
        <Alert_1.Alert onClose={() => setError('')} severity="error" sx={{ width: '100%' }}>
          {error}
        </Alert_1.Alert>
      </material_1.Snackbar>
      <material_1.Grid container direction="column">
        <material_1.Grid item container direction="column" alignItems="flex-start" justifyContent="center" sx={{ paddingLeft: 5, paddingTop: 5 }}>
          {choose === 'file' && (<>
              <material_1.Typography variant="body2" color="primary">
                Add your public key file
              </material_1.Typography>
              <material_1.Grid container sx={{
                my: { lg: 1, xl: 1 },
                paddingRight: { xs: 1, sm: 1, md: 5, lg: 5 },
            }}>
                <material_1.Paper sx={{
                backgroundColor: '#f6f7fe',
                width: { xs: 220, md: 600, lg: 600, xl: 600 },
                padding: 2,
                marginTop: 2,
            }}>
                  <material_1.Grid container justifyContent="center">
                    <material_1.Button variant="outlined" component="label" startIcon={<icons_material_1.UploadFile />}>
                      Select or drop file
                      <input type="file" aria-label="add files" hidden onChange={(e) => handleChange(e, setError, setKey, setPage, setStep)}/>
                    </material_1.Button>
                  </material_1.Grid>
                </material_1.Paper>
              </material_1.Grid>
            </>)}
          {choose === 'github' && (<>
              <material_1.Typography variant="body2" color="primary">
                Add your public key file from github
              </material_1.Typography>
              <material_1.Grid container sx={{
                my: { lg: 1, xl: 1 },
                paddingRight: { xs: 1, sm: 1, md: 5, lg: 5 },
            }}>
                <material_1.Paper sx={{
                backgroundColor: '#f6f7fe',
                width: { xs: 220, md: 600, lg: 600, xl: 600 },
                padding: 2,
                marginTop: 2,
            }}>
                  <material_1.Grid container justifyContent="center">
                    {publicKeys.length > 0 && (<material_1.Box sx={{
                    width: '100%',
                    maxWidth: 360,
                    bgcolor: 'background.paper',
                }}>
                        <material_1.List subheader={<material_1.ListSubheader component="div" id="nested-list-subheader">
                              Pick your public key
                            </material_1.ListSubheader>}>
                          {publicKeys.map((a) => (<material_1.ListItem key={a.raw_key} disablePadding>
                              <material_1.ListItemButton onClick={() => pickPublicKey(a.raw_key, setKey, setPage, setStep)}>
                                <material_1.ListItemIcon>
                                  <icons_material_1.Key />
                                </material_1.ListItemIcon>
                                <material_1.Grid justifyContent={`center`}>
                                  <material_1.ListItemText primary={a?.name}/>
                                  <material_1.ListItemText primary={`Key ID : ${a.key_id}`}/>
                                </material_1.Grid>
                              </material_1.ListItemButton>
                            </material_1.ListItem>))}
                        </material_1.List>
                      </material_1.Box>)}

                    {publicKeys.length === 0 && (<material_1.TextField sx={{ width: { lg: 200, xl: 200 } }} id="outlined-basic" label="Github username" variant="outlined" value={username} onChange={(e) => setUsername(e.target.value)}/>)}
                  </material_1.Grid>
                  <material_1.Grid container justifyContent="center">
                    {publicKeys.length === 0 ? (<material_1.Button variant="outlined" onClick={() => getGithubGPGFile(username, setError, setPublicKeys)} sx={{ mx: 2, mt: 2 }}>
                        Get public key
                      </material_1.Button>) : (<material_1.Button variant="outlined" onClick={() => {
                    setChoose('github');
                    setPublicKeys([]);
                }} sx={{ mx: 2, mt: 2 }}>
                        Try another username
                      </material_1.Button>)}
                  </material_1.Grid>
                </material_1.Paper>
              </material_1.Grid>
            </>)}
          {choose.length === 0 && (<>
              {' '}
              <material_1.Typography variant="body2" color="primary">
                Import your public key
              </material_1.Typography>
              <material_1.Grid container sx={{
                width: { md: 650, lg: 650, xl: 650 },
                my: { lg: 1, xl: 1 },
                paddingRight: { xs: 1, sm: 1, md: 5, lg: 5 },
            }}>
                <material_1.Grid container justifyContent="center">
                  <material_1.Button onClick={() => setChoose('file')} variant="contained" sx={{ mx: 2, mt: 2 }}>
                    Using file
                  </material_1.Button>
                </material_1.Grid>
                <material_1.Grid container justifyContent="center">
                  <material_1.Button onClick={() => setChoose('github')} variant="contained" sx={{ mx: 2, mt: 2 }}>
                    Using github
                  </material_1.Button>
                </material_1.Grid>
              </material_1.Grid>
            </>)}
        </material_1.Grid>

        <material_1.Grid item container direction="row" justifyContent="flex-end" alignItems="flex-end" sx={{
            marginTop: { xs: 1, sm: 1, md: 10, lg: 10 },
            paddingRight: { xs: 1, sm: 1, md: 5, lg: 5 },
            marginBottom: { xs: 1, sm: 1, md: 7, lg: 7 },
        }}>
          <material_1.Button variant="outlined" sx={{ mr: 2 }} onClick={goBack}>
            Back
          </material_1.Button>
        </material_1.Grid>
      </material_1.Grid>
    </material_1.Paper>);
};
exports.ImportPubkey = ImportPubkey;
