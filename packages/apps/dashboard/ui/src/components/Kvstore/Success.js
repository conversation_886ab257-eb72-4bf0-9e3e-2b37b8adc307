"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Success = void 0;
const KVStore_json_1 = __importDefault(require("@human-protocol/core/abis/KVStore.json"));
const sdk_1 = require("@human-protocol/sdk");
const material_1 = require("@mui/material");
const file_saver_1 = require("file-saver");
const jszip_1 = __importDefault(require("jszip"));
const react_1 = require("react");
const wagmi_1 = require("wagmi");
const Alert_1 = require("../Alert");
const constants_1 = require("./constants");
async function downloadKey(publicKey, privateKey, setError) {
    try {
        const pubkey = new Blob([publicKey], { type: 'text/plain' });
        const privkey = new Blob([privateKey], { type: 'text/plain' });
        const zip = new jszip_1.default();
        zip.file('public_key.gpg', pubkey);
        zip.file('private_key', privkey);
        const ja = await zip.generateAsync({ type: 'blob' });
        (0, file_saver_1.saveAs)(ja, `public_and_private_key.zip`);
    }
    catch (e) {
        if (e instanceof Error) {
            setError(e.message);
        }
    }
}
async function saveToNFTStorage(publicKey, setCid, setError) {
    try {
        const someData = new Blob([publicKey]);
        const cid = await constants_1.NFT_STORAGE_CLIENT.storeBlob(someData);
        setCid(cid);
    }
    catch (e) {
        if (e instanceof Error) {
            setError(e.message);
        }
    }
}
const Success = ({ setStep, setPage, keys, what }) => {
    const { chain } = (0, wagmi_1.useAccount)();
    const [copy, setCopy] = (0, react_1.useState)(false);
    const [error, setError] = (0, react_1.useState)('');
    const [cid, setCid] = (0, react_1.useState)('');
    const [success, setSuccess] = (0, react_1.useState)(false);
    (0, react_1.useEffect)(() => {
        if (keys.publicKey.trim().length !== 0) {
            saveToNFTStorage(keys.publicKey, setCid, setError);
        }
    }, [keys.publicKey]);
    const { data: hash, writeContract } = (0, wagmi_1.useWriteContract)();
    const { isLoading: loadingTransaction, isSuccess: isTransactionSuccess, error: errorTransaction, } = (0, wagmi_1.useWaitForTransactionReceipt)({
        hash,
    });
    (0, react_1.useEffect)(() => {
        if (errorTransaction) {
            setError(errorTransaction.message);
        }
    }, [errorTransaction]);
    (0, react_1.useEffect)(() => {
        if (isTransactionSuccess) {
            setSuccess(true);
            setStep(2);
            setPage(3);
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [isTransactionSuccess]);
    function empowerHuman() {
        writeContract({
            address: sdk_1.NETWORKS[chain?.id]
                ?.kvstoreAddress,
            abi: KVStore_json_1.default,
            functionName: 'set',
            args: ['public_key', cid],
        }, {
            onError: (error) => {
                setError(error.message);
                setSuccess(false);
            },
        });
    }
    return (<material_1.Paper>
      <material_1.Snackbar anchorOrigin={{ vertical: 'top', horizontal: 'center' }} open={copy} autoHideDuration={3000} onClose={() => setCopy(false)}>
        <Alert_1.Alert onClose={() => setCopy(false)} severity="info" sx={{ width: '100%' }}>
          Public key copied
        </Alert_1.Alert>
      </material_1.Snackbar>
      <material_1.Snackbar anchorOrigin={{ vertical: 'top', horizontal: 'center' }} open={success} autoHideDuration={3000} onClose={() => setSuccess(false)}>
        <Alert_1.Alert onClose={() => setSuccess(false)} severity="success" sx={{ width: '100%' }}>
          Transaction success
        </Alert_1.Alert>
      </material_1.Snackbar>
      <material_1.Snackbar anchorOrigin={{ vertical: 'top', horizontal: 'center' }} open={error.length > 0} autoHideDuration={3000} onClose={() => setError('')}>
        <Alert_1.Alert onClose={() => setError('')} severity="error" sx={{ width: '100%' }}>
          {error}
        </Alert_1.Alert>
      </material_1.Snackbar>
      <material_1.Grid container direction="column">
        <material_1.Grid item container direction="column" alignItems="flex-start" justifyContent="center" sx={{ paddingLeft: 5, paddingTop: 5 }}>
          <material_1.Typography sx={{ marginTop: 3 }} variant="h6" color="primary">
            Success!
          </material_1.Typography>
          <material_1.Typography variant="body2" color="primary">
            Your public key has been {what}
          </material_1.Typography>
          <material_1.Grid container>
            <material_1.Paper className="pubkey" sx={{
            backgroundColor: '#f6f7fe',
            width: { lg: 600, xl: 600 },
            marginRight: { lg: 5, xl: 5 },
            height: 200,
            padding: 2,
            marginTop: 2,
            overflowY: 'scroll',
            inlineSize: { xs: 200, md: 400, lg: 600, xl: 600 },
            overflowWrap: 'break-word',
        }}>
              <material_1.Typography align="justify" variant="body2" color="primary">
                {keys.publicKey}
              </material_1.Typography>
            </material_1.Paper>
          </material_1.Grid>
          <material_1.Button size="small" variant="outlined" sx={{ mt: 2 }} onClick={() => {
            setCopy(true);
            navigator.clipboard.writeText(keys.publicKey);
        }}>
            Copy
          </material_1.Button>
        </material_1.Grid>

        <material_1.Grid item container direction="row" justifyContent="flex-end" alignItems="flex-end" sx={{
            marginTop: { xs: 1, sm: 1, md: 5, lg: 5 },
            paddingRight: { xs: 1, sm: 1, md: 5, lg: 5 },
            marginBottom: { xs: 1, sm: 1, md: 7, lg: 7 },
        }}>
          <material_1.Button disabled={cid.length === 0 || loadingTransaction} variant="outlined" sx={{ mr: 2 }} onClick={() => {
            if (what === 'imported') {
                setStep(1);
                setPage(2);
            }
            else {
                setStep(1);
                setPage(1);
            }
        }}>
            Back
          </material_1.Button>
          {what === 'generated' && (<material_1.Button disabled={cid.length === 0 || loadingTransaction} variant="outlined" sx={{ mr: 2 }} onClick={() => downloadKey(keys.publicKey, keys.privateKey, setError)}>
              Download
            </material_1.Button>)}
          <material_1.Button disabled={cid.length === 0 || loadingTransaction} onClick={() => empowerHuman()} variant="contained">
            {cid.length === 0 ? `Please wait...` : `Add to ETH - KV Store`}
          </material_1.Button>
        </material_1.Grid>
      </material_1.Grid>
    </material_1.Paper>);
};
exports.Success = Success;
