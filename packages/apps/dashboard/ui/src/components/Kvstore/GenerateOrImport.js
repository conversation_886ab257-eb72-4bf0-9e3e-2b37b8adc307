"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.GenerateOrImport = void 0;
const material_1 = require("@mui/material");
const key_svg_1 = __importDefault(require("src/assets/key.svg"));
const GenerateOrImport = ({ setStep, setPage, }) => (<material_1.Paper>
    <material_1.Grid container justifyContent="center" direction="column">
      <material_1.Grid item container direction="column" alignItems="center" sx={{
        marginTop: { xs: 1, sm: 1, md: 10, lg: 10 },
        marginBottom: { lg: 10 },
    }}>
        {' '}
        <img width="100" src={key_svg_1.default} alt="lbank"/>{' '}
        <material_1.Typography sx={{ marginTop: 3 }} variant="body2" color="primary">
          If you already have your public key import it, if not generate it
        </material_1.Typography>
      </material_1.Grid>
      <material_1.Grid item container direction="row" justifyContent="center" alignItems="flex-end" sx={{
        marginTop: { xs: 1, sm: 1, md: 10, lg: 10 },
        marginBottom: { xs: 1, sm: 1, md: 10, lg: 7 },
    }}>
        <material_1.Button onClick={() => {
        setStep(1);
        setPage(1);
    }} variant="contained" sx={{ marginRight: 2 }}>
          Generate
        </material_1.Button>
        <material_1.Button onClick={() => {
        setStep(1);
        setPage(2);
    }} variant="outlined">
          Import
        </material_1.Button>
      </material_1.Grid>
    </material_1.Grid>
  </material_1.Paper>);
exports.GenerateOrImport = GenerateOrImport;
