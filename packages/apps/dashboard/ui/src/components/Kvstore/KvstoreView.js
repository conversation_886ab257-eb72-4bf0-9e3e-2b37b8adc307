"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.KvstoreView = void 0;
const KVStore_json_1 = __importDefault(require("@human-protocol/core/abis/KVStore.json"));
const sdk_1 = require("@human-protocol/sdk");
const react_1 = require("react");
const wagmi_1 = require("wagmi");
const AfterConnect_1 = require("./AfterConnect");
const Dashboard_1 = require("./Dashboard");
const MainPage_1 = require("./MainPage");
const NotificationProvider_1 = require("src/providers/NotificationProvider");
const KvstoreView = () => {
    const { isConnected, address, chain } = (0, wagmi_1.useAccount)();
    const [publicKey, setPublicKey] = (0, react_1.useState)('');
    const [step, setStep] = (0, react_1.useState)(0);
    const [page, setPage] = (0, react_1.useState)(0);
    const [pubkeyExist, setPubkeyExist] = (0, react_1.useState)(false);
    const { data, refetch } = (0, wagmi_1.useReadContract)({
        address: sdk_1.NETWORKS[chain?.id]?.kvstoreAddress,
        abi: KVStore_json_1.default,
        functionName: 'get',
        args: [address, 'public_key'],
    });
    const { showMessage } = (0, NotificationProvider_1.useNotification)();
    (0, react_1.useEffect)(() => {
        if (publicKey?.trim().length === 0) {
            setStep(0);
            setPage(0);
        }
        setPublicKey(data);
        if (data) {
            setPubkeyExist(true);
        }
    }, [data, chain, publicKey]);
    (0, react_1.useEffect)(() => {
        if (isConnected && !chain) {
            showMessage('Unsupported network.', 'Please switch to the one of the supported networks.', 'error');
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [isConnected, chain]);
    return (<>
      {(!isConnected || !chain) && <MainPage_1.MainPage />}
      {isConnected && publicKey?.trim().length === 0 && (<AfterConnect_1.AfterConnect refetch={refetch} setPublicKey={setPublicKey} pubkeyExist={pubkeyExist} step={step} setStep={setStep} page={page} setPage={setPage}/>)}
      {isConnected && publicKey?.trim().length > 0 && (<Dashboard_1.Dashboard setStep={setStep} setPage={setPage} publicKey={publicKey} refetch={refetch} setPublicKey={setPublicKey}/>)}
    </>);
};
exports.KvstoreView = KvstoreView;
