"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.NFT_STORAGE_CLIENT = exports.PUBKEY_REGEX = exports.STEPS = void 0;
const nft_storage_1 = require("nft.storage");
exports.STEPS = ['Get Public Key', 'Add Public Key', 'Empower Human Scan'];
exports.PUBKEY_REGEX = /^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/;
exports.NFT_STORAGE_CLIENT = new nft_storage_1.NFTStorage({
    token: import.meta.env.VITE_APP_NFT_STORAGE_API,
});
