"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Decrypt = void 0;
const KVStore_json_1 = __importDefault(require("@human-protocol/core/abis/KVStore.json"));
const sdk_1 = require("@human-protocol/sdk");
const icons_material_1 = require("@mui/icons-material");
const material_1 = require("@mui/material");
const openpgp = __importStar(require("openpgp"));
const react_1 = require("react");
const wagmi_1 = require("wagmi");
const Alert_1 = require("../Alert");
const services_1 = require("src/services");
const Decrypt = () => {
    const { address, chain } = (0, wagmi_1.useAccount)();
    const [key, setKey] = (0, react_1.useState)('');
    const [passphrase, setPassphrase] = (0, react_1.useState)('');
    const [value, setValue] = (0, react_1.useState)('');
    const [error, setError] = (0, react_1.useState)('');
    const [privkey, setPrivkey] = (0, react_1.useState)('');
    const [decrypted, setDecrypted] = (0, react_1.useState)('');
    const [copy, setCopy] = (0, react_1.useState)(false);
    const [loading, setLoading] = (0, react_1.useState)(false);
    const [filename, setFilename] = (0, react_1.useState)('');
    const { refetch } = (0, wagmi_1.useReadContract)({
        address: sdk_1.NETWORKS[chain?.id]?.kvstoreAddress,
        abi: KVStore_json_1.default,
        functionName: 'get',
        args: [address, key],
    });
    async function getValue() {
        setLoading(true);
        try {
            const { data } = await refetch();
            setValue(await (0, services_1.showIPFS)(data.toString()));
            setLoading(false);
        }
        catch (e) {
            if (e instanceof Error) {
                setLoading(false);
                setError(e.message);
            }
        }
    }
    const handleFile = async (e) => {
        setLoading(true);
        setPrivkey('');
        try {
            const content = e.target.result;
            const a = await openpgp.readKey({ armoredKey: content });
            if (!a.isPrivate()) {
                setError('Error, file is not private key');
                setLoading(false);
                setFilename('');
                return;
            }
            setPrivkey(content);
            setLoading(false);
        }
        catch (e) {
            if (e instanceof Error) {
                setError(e.message);
                setLoading(false);
                setFilename('');
            }
        }
    };
    const handleChange = (ev) => {
        const reader = new FileReader();
        reader.addEventListener('load', handleFile);
        reader.readAsText(ev.target.files[0]);
        setFilename(ev.target.files[0].name);
    };
    async function decryptValue() {
        setLoading(true);
        try {
            const privateKey = await openpgp.decryptKey({
                privateKey: await openpgp.readPrivateKey({ armoredKey: privkey }),
                passphrase,
            });
            const message = await openpgp.readMessage({
                armoredMessage: value,
            });
            const { data: decryptedVal } = await openpgp.decrypt({
                message,
                decryptionKeys: privateKey,
            });
            setDecrypted(decryptedVal);
            setLoading(false);
        }
        catch (e) {
            if (e instanceof Error) {
                setDecrypted('');
                setError(e.message);
                setLoading(false);
            }
        }
    }
    return (<material_1.Grid item xs={12} sm={12} md={12} container direction="column" justifyContent="center" alignItems="center">
      <material_1.Snackbar anchorOrigin={{ vertical: 'top', horizontal: 'center' }} open={copy} autoHideDuration={3000} onClose={() => setCopy(false)}>
        <Alert_1.Alert onClose={() => setCopy(false)} severity="info" sx={{ width: '100%' }}>
          copied
        </Alert_1.Alert>
      </material_1.Snackbar>

      <material_1.Snackbar anchorOrigin={{ vertical: 'top', horizontal: 'center' }} open={error.length > 0} autoHideDuration={3000} onClose={() => setError('')}>
        <Alert_1.Alert onClose={() => setError('')} severity="error" sx={{ width: '100%' }}>
          {error}
        </Alert_1.Alert>
      </material_1.Snackbar>
      <material_1.Box sx={{ width: { xs: 1, md: '50%', lg: '50%', xl: '40%' } }}>
        <material_1.Paper>
          <material_1.Box sx={{ borderBottom: '1px solid #CBCFE6' }}>
            <material_1.Grid container direction="row" justifyContent={'space-between'} flexDirection={{ xs: 'column-reverse', md: 'row' }}>
              <material_1.Grid item>
                <material_1.Typography fontWeight={`500`} padding={`10px`}>
                  Decrypt
                </material_1.Typography>
              </material_1.Grid>
            </material_1.Grid>
          </material_1.Box>
          <material_1.Grid container direction="column" sx={{
            padding: {
                xs: '0px 80px 0px 80px',
                md: '0px 80px 0px 80px',
                lg: '0px 80px 0px 80px',
                xl: '0px 80px 0px 80px',
            },
        }}>
            <material_1.Grid item container direction="column" sx={{
            marginBottom: { lg: 10 },
        }}>
              <material_1.Box sx={{ mt: { md: 10, lg: 10, xl: 10 }, mb: 2 }}>
                <material_1.Typography>
                  Decrypt using your public key and private key
                </material_1.Typography>
              </material_1.Box>

              <material_1.TextField sx={{ my: 1 }} id="outlined-basic" label="Key" variant="outlined" value={key} onChange={(e) => setKey(e.target.value)} disabled={loading}/>
              <material_1.Box>
                <material_1.Button disabled={loading} onClick={getValue} sx={{ my: 1, mb: !value ? 5 : 2 }} variant="outlined">
                  Get
                </material_1.Button>
              </material_1.Box>
              {value && (<>
                  <material_1.Box>
                    <material_1.Typography fontWeight={500}>Value</material_1.Typography>
                  </material_1.Box>
                  <material_1.Box sx={{ width: { xs: 1 } }}>
                    <material_1.Box className="pubkey" sx={{
                backgroundColor: '#f6f7fe',
                maxHeight: 200,
                marginTop: 2,
                overflowY: 'scroll',
                overflowWrap: 'break-word',
                padding: 4,
                borderRadius: 3,
            }}>
                      {value}
                    </material_1.Box>
                  </material_1.Box>
                  <material_1.Box sx={{ marginBottom: { xs: 2, sm: 2, md: 2, lg: 0 } }}>
                    <material_1.Button onClick={() => {
                setCopy(true);
                navigator.clipboard.writeText(value);
            }} size="small">
                      Copy
                    </material_1.Button>
                  </material_1.Box>
                  <material_1.Divider sx={{ my: 2 }} light/>
                  <material_1.Box>
                    <material_1.Typography>Decrypt </material_1.Typography>
                  </material_1.Box>
                  <material_1.Box display={`flex`} flexDirection={'row'} alignContent={`center`}>
                    {privkey.length === 0 && (<material_1.Button sx={{ my: 2 }} disabled={loading} variant="outlined" size="small" component="label">
                        Upload Private Key
                        <input type="file" aria-label="add files" hidden onChange={handleChange}/>
                      </material_1.Button>)}
                    {privkey.length > 0 && (<>
                        <material_1.Typography sx={{ my: 2 }} variant={`body1`}>
                          {filename}
                        </material_1.Typography>
                        <icons_material_1.Close onClick={() => setPrivkey('')} sx={{ cursor: 'pointer', my: 2.3 }}/>
                      </>)}
                  </material_1.Box>
                  <material_1.TextField sx={{ my: 1 }} id="outlined-basic" label="Passphrase" variant="outlined" value={passphrase} type="password" disabled={loading} onChange={(e) => setPassphrase(e.target.value)}/>
                  <material_1.Box display={`flex`} width={`100%`} justifyContent={`flex-end`}>
                    <material_1.Button disabled={loading} sx={{ my: 1, mb: !decrypted ? 5 : 2 }} variant="contained" onClick={decryptValue}>
                      Decrypt
                    </material_1.Button>
                  </material_1.Box>
                  {decrypted && (<>
                      <material_1.Box>
                        <material_1.Typography fontWeight={`500`}>
                          Decrypted Value{' '}
                        </material_1.Typography>
                      </material_1.Box>
                      <material_1.Box className="pubkey" sx={{
                    backgroundColor: '#f6f7fe',
                    maxHeight: 200,
                    marginTop: 2,
                    overflowY: 'scroll',
                    overflowWrap: 'break-word',
                    padding: 4,
                    borderRadius: 3,
                }}>
                        {decrypted}
                      </material_1.Box>
                      <material_1.Box sx={{ marginBottom: { xs: 2, sm: 2, md: 2, lg: 0 } }}>
                        <material_1.Button onClick={() => {
                    setCopy(true);
                    navigator.clipboard.writeText(value);
                }} size="small">
                          Copy
                        </material_1.Button>
                      </material_1.Box>
                    </>)}
                </>)}
            </material_1.Grid>
          </material_1.Grid>
        </material_1.Paper>
      </material_1.Box>
    </material_1.Grid>);
};
exports.Decrypt = Decrypt;
