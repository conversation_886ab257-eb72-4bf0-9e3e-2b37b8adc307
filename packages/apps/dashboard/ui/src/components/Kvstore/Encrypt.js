"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Encrypt = void 0;
const KVStore_json_1 = __importDefault(require("@human-protocol/core/abis/KVStore.json"));
const sdk_1 = require("@human-protocol/sdk");
const material_1 = require("@mui/material");
const Snackbar_1 = __importDefault(require("@mui/material/Snackbar"));
const openpgp = __importStar(require("openpgp"));
const react_1 = require("react");
const wagmi_1 = require("wagmi");
const Alert_1 = require("../Alert");
const constants_1 = require("./constants");
const Encrypt = ({ publicKey }) => {
    const { chain } = (0, wagmi_1.useAccount)();
    const [key, setKey] = (0, react_1.useState)('');
    const [value, setValue] = (0, react_1.useState)('');
    const [loading, setLoading] = (0, react_1.useState)(false);
    const [success, setSuccess] = (0, react_1.useState)(false);
    const [error, setError] = (0, react_1.useState)('');
    const [copy, setCopy] = (0, react_1.useState)(false);
    const [encrypted, setEncrypted] = (0, react_1.useState)('');
    const { data: hash, writeContractAsync } = (0, wagmi_1.useWriteContract)();
    const { isSuccess: isTransactionSuccess, error: errorTransaction } = (0, wagmi_1.useWaitForTransactionReceipt)({
        hash,
    });
    (0, react_1.useEffect)(() => {
        if (errorTransaction) {
            setError(errorTransaction.message);
            setSuccess(false);
            setLoading(false);
        }
    }, [errorTransaction]);
    (0, react_1.useEffect)(() => {
        if (isTransactionSuccess) {
            setSuccess(true);
            setLoading(false);
        }
    }, [isTransactionSuccess]);
    async function storeKeyValue() {
        setLoading(true);
        setError('');
        setSuccess(false);
        setEncrypted('');
        if (key.trim().length === 0 || value.trim().length === 0) {
            setError('please fill key, value');
            setLoading(false);
            return;
        }
        try {
            const publicKeyArmored = await openpgp.readKey({ armoredKey: publicKey });
            const message = await openpgp.createMessage({ text: value });
            const encrypted1 = await openpgp.encrypt({
                message,
                encryptionKeys: publicKeyArmored,
                config: {
                    preferredCompressionAlgorithm: openpgp.enums.compression.zlib,
                },
            });
            const someData = new Blob([encrypted1]);
            const cid = await constants_1.NFT_STORAGE_CLIENT.storeBlob(someData);
            await writeContractAsync?.({
                address: sdk_1.NETWORKS[chain?.id]
                    ?.kvstoreAddress,
                abi: KVStore_json_1.default,
                chainId: chain?.id,
                functionName: 'set',
                args: [key, cid],
            }, {
                onError: () => {
                    setError('Error transaction');
                    setSuccess(false);
                    setLoading(false);
                },
            });
            setEncrypted(encrypted1);
        }
        catch (e) {
            if (e instanceof Error) {
                setLoading(false);
                setError(e.message);
            }
        }
    }
    return (<material_1.Grid item xs={12} sm={12} md={12} container direction="column" justifyContent="center" alignItems="center">
      <Snackbar_1.default anchorOrigin={{ vertical: 'top', horizontal: 'center' }} open={copy} autoHideDuration={3000} onClose={() => setCopy(false)}>
        <Alert_1.Alert onClose={() => setCopy(false)} severity="info" sx={{ width: '100%' }}>
          Encrypted message copied
        </Alert_1.Alert>
      </Snackbar_1.default>
      <Snackbar_1.default anchorOrigin={{ vertical: 'top', horizontal: 'center' }} open={success} autoHideDuration={3000} onClose={() => setSuccess(false)}>
        <Alert_1.Alert onClose={() => setSuccess(false)} severity="success" sx={{ width: '100%' }}>
          Transaction success
        </Alert_1.Alert>
      </Snackbar_1.default>
      <Snackbar_1.default anchorOrigin={{ vertical: 'top', horizontal: 'center' }} open={error.length > 0} autoHideDuration={3000} onClose={() => setError('')}>
        <Alert_1.Alert onClose={() => setError('')} severity="error" sx={{ width: '100%' }}>
          {error}
        </Alert_1.Alert>
      </Snackbar_1.default>
      <material_1.Box sx={{ width: { xs: 1, md: '50%', lg: '50%', xl: '40%' } }}>
        <material_1.Paper>
          <material_1.Box sx={{ borderBottom: '1px solid #CBCFE6' }}>
            <material_1.Grid container direction="row" justifyContent={'space-between'} flexDirection={{ xs: 'column-reverse', md: 'row' }}>
              <material_1.Grid item>
                <material_1.Typography fontWeight={`500`} padding={`10px`}>
                  Encrypt
                </material_1.Typography>
              </material_1.Grid>
            </material_1.Grid>
          </material_1.Box>
          <material_1.Grid container direction="column" sx={{
            padding: {
                xs: '0px 80px 0px 80px',
                md: '0px 80px 0px 80px',
                lg: '0px 80px 0px 80px',
                xl: '0px 80px 0px 80px',
            },
        }}>
            <material_1.Grid item container direction="column" sx={{
            marginBottom: { lg: 10 },
        }}>
              <material_1.Box sx={{ mt: { md: 10, lg: 10, xl: 10 }, mb: 2 }}>
                <material_1.Typography>Encrypt using your stored public key</material_1.Typography>
              </material_1.Box>
              <material_1.TextField disabled={loading} sx={{ my: 2 }} id="outlined-basic" label="Key" variant="outlined" value={key} onChange={(e) => setKey(e.target.value)}/>
              <material_1.TextField disabled={loading} sx={{ my: 1 }} id="outlined-basic" label="Value" variant="outlined" value={value} onChange={(e) => setValue(e.target.value)}/>
              <material_1.Box display={`flex`} width={`100%`} justifyContent={`flex-end`} marginBottom={2}>
                <material_1.Button disabled={loading} onClick={storeKeyValue} sx={{ my: 1 }} variant="contained">
                  Store
                </material_1.Button>
              </material_1.Box>

              {encrypted.length > 0 && !loading && (<>
                  <material_1.Box sx={{ width: { xs: 1 } }}>
                    <material_1.Box className="pubkey" sx={{
                backgroundColor: '#f6f7fe',
                maxHeight: 200,
                marginTop: 2,
                overflowY: 'scroll',
                overflowWrap: 'break-word',
                padding: 4,
                borderRadius: 3,
            }}>
                      {encrypted}
                    </material_1.Box>
                  </material_1.Box>
                  <material_1.Box sx={{
                marginTop: 2,
                marginBottom: { xs: 2, sm: 2, md: 4, lg: 0 },
            }}>
                    <material_1.Button onClick={() => {
                setCopy(true);
                navigator.clipboard.writeText(encrypted);
            }} size="small">
                      Copy
                    </material_1.Button>
                  </material_1.Box>
                </>)}
            </material_1.Grid>
          </material_1.Grid>
        </material_1.Paper>
      </material_1.Box>
    </material_1.Grid>);
};
exports.Encrypt = Encrypt;
