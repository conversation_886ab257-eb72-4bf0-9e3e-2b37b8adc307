"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Dashboard = void 0;
const material_1 = require("@mui/material");
const react_1 = require("react");
const ViewTitle_1 = require("../ViewTitle");
const Decrypt_1 = require("./Decrypt");
const Encrypt_1 = require("./Encrypt");
const StoredPubkey_1 = require("./StoredPubkey");
const small_key_svg_1 = __importDefault(require("src/assets/small_key.svg"));
const services_1 = require("src/services");
const Dashboard = ({ publicKey, refetch, setPublicKey, setStep, setPage, }) => {
    const [value, setValue] = (0, react_1.useState)(0);
    const [pubkey, setPubkey] = (0, react_1.useState)('');
    (0, react_1.useEffect)(() => {
        if (publicKey.trim().length !== 0) {
            (0, services_1.showIPFS)(publicKey).then((a) => setPubkey(a));
        }
    }, [publicKey]);
    const handleChange = (event, newValue) => {
        setValue(newValue);
    };
    return (<material_1.Grid container>
      <material_1.Grid item xs={12} sm={12} md={12} direction="row" justifyContent="center">
        <material_1.Box marginBottom="1em" display="flex" alignItems="center" flexWrap="wrap">
          <ViewTitle_1.ViewTitle title="ETH KV Store" iconUrl={small_key_svg_1.default}/>
        </material_1.Box>
        <material_1.Box sx={{ borderBottom: 1, borderColor: 'divider', marginBottom: 2 }}>
          {pubkey.trim().length > 0 && (<material_1.Tabs value={value} onChange={handleChange}>
              <material_1.Tab label="Public Key"/>
              <material_1.Tab label="Encrypt"/>
              <material_1.Tab label="Decrypt"/>
            </material_1.Tabs>)}
        </material_1.Box>
        {value === 0 && (<StoredPubkey_1.StoredPubkey refetch={refetch} publicKey={pubkey} setStep={setStep} setPage={setPage} setPublicKey={setPublicKey}/>)}
        {value === 1 && <Encrypt_1.Encrypt publicKey={pubkey}/>}
        {value === 2 && <Decrypt_1.Decrypt />}
      </material_1.Grid>
    </material_1.Grid>);
};
exports.Dashboard = Dashboard;
