"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.NetworkSelect = void 0;
const sdk_1 = require("@human-protocol/sdk");
const material_1 = require("@mui/material");
const chains_1 = require("../Icons/chains");
const constants_1 = require("src/constants");
const NetworkSelect = (props) => (<material_1.FormControl variant="standard" sx={{ m: { xs: 0, md: 1 }, minWidth: 220, width: props.width }}>
    <material_1.InputLabel id="newtork-select-label">Network</material_1.InputLabel>
    <material_1.Select labelId="network-select-label" id="network-select" label="Network" sx={{
        '.MuiSelect-select': {
            display: 'flex',
            alignItems: 'center',
            paddingTop: '8px',
            paddingBottom: '8px',
            minWidth: '300px',
            '.MuiListItemIcon-root': {
                minWidth: '36px',
            },
        },
    }} {...props}>
      {props.showAllNetwork && (<material_1.MenuItem value={sdk_1.ChainId.ALL}>
          <material_1.ListItemIcon sx={{ color: '#320a8d', fontSize: '0.8rem' }}>
            {chains_1.CHAIN_ICONS[sdk_1.ChainId.ALL]}
          </material_1.ListItemIcon>
          All Networks
        </material_1.MenuItem>)}
      {(props.supportedChainIds ?? constants_1.V2_SUPPORTED_CHAIN_IDS).map((chainId) => {
        const IconComponent = chains_1.CHAIN_ICONS[chainId];
        return (<material_1.MenuItem value={chainId} key={chainId}>
            {IconComponent && (<material_1.ListItemIcon sx={{ color: '#320a8d' }}>
                {IconComponent}
              </material_1.ListItemIcon>)}
            {sdk_1.NETWORKS[chainId]?.title}
          </material_1.MenuItem>);
    })}
    </material_1.Select>
  </material_1.FormControl>);
exports.NetworkSelect = NetworkSelect;
