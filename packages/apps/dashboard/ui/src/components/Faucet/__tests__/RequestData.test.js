"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const sdk_1 = require("@human-protocol/sdk");
const react_1 = require("@testing-library/react");
const test_utils_1 = require("react-dom/test-utils");
const react_router_dom_1 = require("react-router-dom");
const react_test_renderer_1 = require("react-test-renderer");
const RequestData_1 = require("../RequestData");
describe('when rendered AfterConnect component', () => {
    it('should render `text` prop', async () => {
        await (0, test_utils_1.act)(async () => {
            (0, react_1.render)(<RequestData_1.RequestData {...{}} network={sdk_1.NETWORKS[sdk_1.ChainId.POLYGON_AMOY]}/>, { wrapper: react_router_dom_1.MemoryRouter });
        });
        expect(react_1.screen.getByText(/Token address:/)).toBeInTheDocument();
    });
});
it('AfterConnect component renders correctly, corresponds to the snapshot', () => {
    const tree = (0, react_test_renderer_1.create)(<react_router_dom_1.MemoryRouter>
      <RequestData_1.RequestData {...{}} network={sdk_1.NETWORKS[sdk_1.ChainId.POLYGON_AMOY]}/>
    </react_router_dom_1.MemoryRouter>).toJSON();
    expect(tree).toMatchSnapshot();
});
