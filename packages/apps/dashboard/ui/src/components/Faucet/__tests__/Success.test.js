"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const react_1 = require("@testing-library/react");
const test_utils_1 = require("react-dom/test-utils");
const react_router_dom_1 = require("react-router-dom");
const react_test_renderer_1 = require("react-test-renderer");
const Success_1 = require("../Success");
describe('when rendered Success component', () => {
    it('should render `text` prop', async () => {
        await (0, test_utils_1.act)(async () => {
            (0, react_1.render)(<Success_1.Success {...{}}/>, { wrapper: react_router_dom_1.MemoryRouter });
        });
        expect(react_1.screen.getByText(/Request Complete!/)).toBeInTheDocument();
        expect(react_1.screen.getByText(/Congratulations, 10 testnet HMT was sent to your account/)).toBeInTheDocument();
    });
});
it('Success component renders correctly, corresponds to the snapshot', () => {
    const tree = (0, react_test_renderer_1.create)(<react_router_dom_1.MemoryRouter>
      <Success_1.Success {...{}}/>
    </react_router_dom_1.MemoryRouter>).toJSON();
    expect(tree).toMatchSnapshot();
});
