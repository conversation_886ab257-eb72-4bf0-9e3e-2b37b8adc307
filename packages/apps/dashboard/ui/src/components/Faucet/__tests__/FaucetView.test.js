"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const react_1 = require("@testing-library/react");
const test_utils_1 = require("react-dom/test-utils");
const react_router_dom_1 = require("react-router-dom");
const react_test_renderer_1 = require("react-test-renderer");
const FaucetView_1 = require("../FaucetView");
describe('when rendered AfterConnect component', () => {
    it('should render `text` prop', async () => {
        await (0, test_utils_1.act)(async () => {
            (0, react_1.render)(<FaucetView_1.FaucetView />, { wrapper: react_router_dom_1.MemoryRouter });
        });
        expect(react_1.screen.getByText(/HUMAN Faucet for testnet/)).toBeInTheDocument();
    });
});
it('AfterConnect component renders correctly, corresponds to the snapshot', () => {
    const tree = (0, react_test_renderer_1.create)(<react_router_dom_1.MemoryRouter>
      <FaucetView_1.FaucetView />
    </react_router_dom_1.MemoryRouter>).toJSON();
    expect(tree).toMatchSnapshot();
});
