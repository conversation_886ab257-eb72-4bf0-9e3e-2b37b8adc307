"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.RequestData = void 0;
const sdk_1 = require("@human-protocol/sdk");
const material_1 = require("@mui/material");
const react_1 = require("react");
const react_router_dom_1 = require("react-router-dom");
const Alert_1 = require("../Alert");
const constants_1 = require("src/constants");
const RequestData = ({ step, setStep, setTxHash, network, setNetwork, }) => {
    const [address, setAddress] = (0, react_1.useState)('');
    const [error, setError] = (0, react_1.useState)('');
    const handleSubmit = async () => {
        setStep(1);
        const payload = { address: address, chainId: network.chainId };
        const response = await fetch(`${import.meta.env.VITE_APP_FAUCET_SERVER_URL}/faucet`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                Accept: 'application/json',
            },
            body: JSON.stringify(payload),
        });
        const result = await response.json();
        if (!result.status) {
            setError(result.message);
            setStep(0);
        }
        else {
            setTxHash(result.txHash);
            setStep(2);
        }
    };
    return (<material_1.Paper>
      <material_1.Snackbar anchorOrigin={{ vertical: 'top', horizontal: 'center' }} open={error.length > 0} autoHideDuration={3000} onClose={() => setError('')}>
        <Alert_1.Alert onClose={() => setError('')} severity="error" sx={{ width: '100%' }}>
          {error}
        </Alert_1.Alert>
      </material_1.Snackbar>
      <material_1.Grid container justifyContent="center" direction="column" sx={{
            padding: { xs: 2, sm: 2, md: 10, lg: 10 },
            minWidth: 600,
        }}>
        <material_1.Grid item container direction="row" alignItems="center">
          <material_1.FormControl fullWidth disabled={step === 1}>
            <material_1.InputLabel>Network</material_1.InputLabel>
            <material_1.Select label="Network" variant="outlined" value={network.chainId} onChange={(e) => setNetwork(sdk_1.NETWORKS[Number(e.target.value)])}>
              {constants_1.FAUCET_CHAIN_IDS.map((chainId) => (<material_1.MenuItem key={chainId} value={chainId}>
                  {sdk_1.NETWORKS[chainId]?.title}
                </material_1.MenuItem>))}
            </material_1.Select>
          </material_1.FormControl>
        </material_1.Grid>

        <material_1.Grid item container direction="row" alignItems="center" sx={{
            marginTop: { xs: 1, sm: 1, md: 5, lg: 5 },
        }}>
          <material_1.TextField fullWidth id="outlined-basic" label="Address" variant="outlined" value={address} onChange={(e) => setAddress(e.target.value)} disabled={step === 1}/>
        </material_1.Grid>
        <material_1.Grid item container direction="row" justifyContent="center" alignItems="flex-end" sx={{
            marginTop: { xs: 1, sm: 1, md: 5, lg: 5 },
        }}>
          <material_1.Button fullWidth onClick={handleSubmit} variant="contained" disabled={step === 1} sx={{
            '&.Mui-disabled': {
                background: '#2F0087',
                color: '#FFFFFF',
            },
        }}>
            {step === 1 ? 'Sending tokens' : 'Send me'}
          </material_1.Button>
        </material_1.Grid>
        <material_1.Grid item container direction="row" justifyContent="center" alignItems="flex-end" sx={{
            marginTop: { xs: 1, sm: 1, md: 5, lg: 5 },
        }}>
          <material_1.Typography variant="body2" sx={step === 1
            ? {
                fontWeight: 'bold',
                color: (theme) => theme.palette.text.disabled,
            }
            : {
                fontWeight: 'bold',
            }}>
            Token address:
          </material_1.Typography>
        </material_1.Grid>
        <material_1.Grid item container direction="row" justifyContent="center" alignItems="flex-end">
          <material_1.Typography variant="body2" sx={step === 1
            ? {
                mt: 1,
                color: (theme) => theme.palette.text.disabled,
            }
            : {
                mt: 1,
            }}>
            <react_router_dom_1.Link to={network?.scanUrl + '/address/' + network?.hmtAddress} style={{ textDecoration: 'none', color: 'inherit' }}>
              {network?.hmtAddress}
            </react_router_dom_1.Link>
          </material_1.Typography>
        </material_1.Grid>
      </material_1.Grid>
    </material_1.Paper>);
};
exports.RequestData = RequestData;
