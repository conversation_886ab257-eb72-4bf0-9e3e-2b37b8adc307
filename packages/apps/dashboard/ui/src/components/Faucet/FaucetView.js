"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.FaucetView = void 0;
const sdk_1 = require("@human-protocol/sdk");
const material_1 = require("@mui/material");
const react_1 = require("react");
const RequestData_1 = require("./RequestData");
const Success_1 = require("./Success");
const STEPS = ['Wallet Address', 'Send Test tokens', 'Confirmation'];
const FaucetView = () => {
    const [step, setStep] = (0, react_1.useState)(0);
    const [txHash, setTxHash] = (0, react_1.useState)('');
    const [network, setNetwork] = (0, react_1.useState)(sdk_1.NETWORKS[sdk_1.ChainId.POLYGON_AMOY]);
    return (<material_1.Grid container>
      <material_1.Grid item xs={12} sm={12} md={12} container direction="row" justifyContent="center">
        <material_1.Grid item xs={12} sm={12} md={12} container direction="column" justifyContent="center" alignItems="center">
          <material_1.Box>
            <material_1.Typography variant="h4" sx={{ marginBottom: 2 }} color="primary">
              HUMAN Faucet for testnet
            </material_1.Typography>
            <material_1.Paper sx={{ padding: { md: 2 }, marginBottom: 2 }}>
              <material_1.Box sx={{ width: '100%' }}>
                <material_1.Stepper activeStep={step}>
                  {STEPS.map((step) => (<material_1.Step key={step}>
                      <material_1.StepLabel>{step}</material_1.StepLabel>
                    </material_1.Step>))}
                </material_1.Stepper>
              </material_1.Box>
            </material_1.Paper>

            {step < 2 && (<RequestData_1.RequestData step={step} setStep={setStep} setTxHash={setTxHash} network={network} setNetwork={setNetwork}/>)}
            {step === 2 && <Success_1.Success txHash={txHash} network={network}/>}
          </material_1.Box>
        </material_1.Grid>
      </material_1.Grid>
    </material_1.Grid>);
};
exports.FaucetView = FaucetView;
