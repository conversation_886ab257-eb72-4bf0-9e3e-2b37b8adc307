"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Success = void 0;
const material_1 = require("@mui/material");
const react_router_dom_1 = require("react-router-dom");
const Success = ({ txHash, network }) => {
    return (<material_1.Paper>
      <material_1.Grid container direction="column" sx={{
            padding: { xs: 2, sm: 2, md: 10, lg: 10 },
            minWidth: 600,
        }} justifyContent="center">
        <material_1.Grid item container direction="row" justifyContent="center">
          <h2>&#x2713;</h2>
        </material_1.Grid>
        <material_1.Grid item container direction="row" alignItems="center" sx={{ marginTop: { xs: 1, sm: 1, md: 5, lg: 5 } }}>
          <material_1.Typography variant="h6" color="primary" align="center" width={'100%'}>
            Request Complete!
          </material_1.Typography>
        </material_1.Grid>

        <material_1.Grid item container direction="row" alignItems="center" sx={{ marginTop: { xs: 1, sm: 1, md: 3, lg: 3 } }}>
          <material_1.Typography variant="body2" color="primary" align="center" width={'100%'}>
            Congratulations, 10 testnet HMT was sent to your account
          </material_1.Typography>
        </material_1.Grid>

        <material_1.Grid item container direction="row" alignItems="center" sx={{ pb: 3 }}>
          <material_1.Paper sx={{
            backgroundColor: '#FAFAFA',
            padding: 2,
            marginTop: 2,
            overflowY: 'scroll',
            overflowWrap: 'break-word',
        }}>
            <material_1.Typography align="justify" variant="body2" color="primary">
              <react_router_dom_1.Link to={network?.scanUrl + '/tx/' + txHash} style={{ textDecoration: 'none', color: 'inherit' }}>
                {txHash}
              </react_router_dom_1.Link>
            </material_1.Typography>
          </material_1.Paper>
        </material_1.Grid>
        <material_1.Button size="medium" variant="contained" sx={{ marginTop: { xs: 1, sm: 1, md: 3, lg: 3 } }} onClick={() => {
            window.location.href = '/';
        }}>
          Home
        </material_1.Button>
      </material_1.Grid>
    </material_1.Paper>);
};
exports.Success = Success;
