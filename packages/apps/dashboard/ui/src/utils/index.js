"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.formatAmount = exports.shortenAddress = exports.isAddress = void 0;
const address_1 = require("@ethersproject/address");
const bignumber_1 = require("@ethersproject/bignumber");
const units_1 = require("@ethersproject/units");
const constants_1 = require("src/constants");
// returns the checksummed address if the address is valid, otherwise returns false
function isAddress(value) {
    try {
        return (0, address_1.getAddress)(value);
    }
    catch {
        return false;
    }
}
exports.isAddress = isAddress;
// shorten the checksummed version of the input address to have 0x + 4 characters at start and end
function shortenAddress(address, chars = 4) {
    const parsed = isAddress(address);
    if (!parsed) {
        throw Error(`Invalid 'address' parameter '${address}'.`);
    }
    return `${parsed.substring(0, chars + 2)}...${parsed.substring(42 - chars)}`;
}
exports.shortenAddress = shortenAddress;
function formatAmount(amount) {
    return Number((0, units_1.formatUnits)(bignumber_1.BigNumber.from(amount), constants_1.HM_TOKEN_DECIMALS));
}
exports.formatAmount = formatAmount;
