"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.MyHMT = void 0;
const material_1 = require("@mui/material");
const Box_1 = __importDefault(require("@mui/material/Box"));
const human_token_svg_1 = __importDefault(require("src/assets/human-token.svg"));
const components_1 = require("src/components");
const MyHMT_1 = require("src/components/MyHMT");
const ViewTitle_1 = require("src/components/ViewTitle");
const MyHMT = () => (<components_1.PageWrapper>
    <Box_1.default sx={{
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between',
        mb: 4,
    }}>
      <ViewTitle_1.ViewTitle title="My HMT" iconUrl={human_token_svg_1.default}/>
      <material_1.Button color="primary" variant="contained" href="/configure-oracle">
        Configure your Oracle
      </material_1.Button>
    </Box_1.default>
    <material_1.Grid container spacing={4}>
      <material_1.Grid item xs={12} lg={6}>
        <Box_1.default sx={{ display: 'flex', flexDirection: 'column', height: '100%' }}>
          <MyHMT_1.HMTStatusTable />
        </Box_1.default>
      </material_1.Grid>
      <material_1.Grid item xs={12} lg={6}>
        <Box_1.default sx={{ display: 'flex', flexDirection: 'column', height: '100%' }}>
          <MyHMT_1.HMTStatusChart />
        </Box_1.default>
      </material_1.Grid>
    </material_1.Grid>
    <material_1.Grid container spacing={4} sx={{ mt: 8 }}>
      <material_1.Grid item xs={12} md={6}>
        <Box_1.default sx={{ display: 'flex', flexDirection: 'column', height: '100%' }}>
          <material_1.Typography color="primary" variant="h4" fontWeight={600} mb={4}>
            Stake
          </material_1.Typography>
          <MyHMT_1.Stake />
        </Box_1.default>
      </material_1.Grid>
      <material_1.Grid item xs={12} md={6}>
        <Box_1.default sx={{ display: 'flex', flexDirection: 'column', height: '100%' }}>
          <material_1.Typography color="primary" variant="h4" fontWeight={600} mb={4}>
            Rewards
          </material_1.Typography>
          <MyHMT_1.Rewards />
        </Box_1.default>
      </material_1.Grid>
    </material_1.Grid>
    <Box_1.default sx={{ mt: 8 }}>
      <material_1.Typography color="primary" variant="h4" fontWeight={600} mb={4}>
        Rewards History
      </material_1.Typography>
      <MyHMT_1.RewardsHistory />
    </Box_1.default>
    <material_1.Grid container spacing={4} sx={{ mt: 4 }}>
      <material_1.Grid item xs={12} lg={6}>
        <Box_1.default sx={{ display: 'flex', flexDirection: 'column', height: '100%' }}>
          <material_1.Typography color="primary" variant="h4" fontWeight={600} mb={4}>
            Allocated HMT
          </material_1.Typography>
          <MyHMT_1.HMTTable />
        </Box_1.default>
      </material_1.Grid>
      <material_1.Grid item xs={12} lg={6}>
        <Box_1.default sx={{ display: 'flex', flexDirection: 'column', height: '100%' }}>
          <material_1.Typography color="primary" variant="h4" fontWeight={600} mb={4}>
            Slashed HMT
          </material_1.Typography>
          <MyHMT_1.HMTTable />
        </Box_1.default>
      </material_1.Grid>
    </material_1.Grid>
  </components_1.PageWrapper>);
exports.MyHMT = MyHMT;
