"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.HowToHuman = void 0;
const KeyboardArrowDown_1 = __importDefault(require("@mui/icons-material/KeyboardArrowDown"));
const material_1 = require("@mui/material");
const react_1 = require("react");
const how_to_human_svg_1 = __importDefault(require("src/assets/how-to-human.svg"));
const next_arrow_svg_1 = __importDefault(require("src/assets/next-arrow.svg"));
const prev_arrow_svg_1 = __importDefault(require("src/assets/prev-arrow.svg"));
const components_1 = require("src/components");
const how_to_human_1 = require("src/constants/how-to-human");
const HowToHuman = () => {
    const [value, setValue] = (0, react_1.useState)(0);
    const config = how_to_human_1.HOW_TO_HUMAN_DATA[value];
    const theme = (0, material_1.useTheme)();
    const isMobile = (0, material_1.useMediaQuery)(theme.breakpoints.down('sm'));
    const isUpMd = (0, material_1.useMediaQuery)(theme.breakpoints.up(944));
    const [anchorEl, setAnchorEl] = (0, react_1.useState)(null);
    const open = Boolean(anchorEl);
    const handleClick = (event) => {
        setAnchorEl(event.currentTarget);
    };
    const handleClose = () => {
        setAnchorEl(null);
    };
    const renderButtonGroup = () => {
        if (isMobile)
            return (<>
          <material_1.Button color="primary" size="large" variant="contained" endIcon={<KeyboardArrowDown_1.default />} fullWidth sx={{ my: 3 }} onClick={handleClick}>
            {config?.label}
          </material_1.Button>
          <material_1.Menu anchorEl={anchorEl} open={open} onClose={handleClose} anchorOrigin={{
                    vertical: 'bottom',
                    horizontal: 'left',
                }} MenuListProps={{
                    sx: { width: anchorEl && anchorEl.offsetWidth },
                }}>
            {how_to_human_1.HOW_TO_HUMAN_DATA.map((item, index) => (<material_1.MenuItem key={item.key} onClick={() => {
                        setValue(index);
                        handleClose();
                    }}>
                {item.label}
              </material_1.MenuItem>))}
          </material_1.Menu>
        </>);
        return (<material_1.ToggleButtonGroup exclusive value={value} onChange={(e, newValue) => {
                if (newValue !== null)
                    setValue(newValue);
            }} sx={{ my: 3 }} fullWidth={!isUpMd}>
        {how_to_human_1.HOW_TO_HUMAN_DATA.map((item, index) => (<material_1.ToggleButton key={item.key} value={index}>
            {item.label}
          </material_1.ToggleButton>))}
      </material_1.ToggleButtonGroup>);
    };
    return (<components_1.PageWrapper>
      <material_1.Box sx={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            flexWrap: 'wrap',
        }}>
        <components_1.ViewTitle title="How To HUMAN" iconUrl={how_to_human_svg_1.default}/>
        {renderButtonGroup()}
      </material_1.Box>
      <material_1.Box sx={{ py: { xs: 3, md: 10 } }}>
        <material_1.Typography color="primary" variant="h3" fontWeight={600} textAlign="center" mb={{ xs: 4, md: 8 }}>
          {config?.title}
        </material_1.Typography>
        <material_1.Box sx={{
            background: '#fff',
            borderRadius: '20px',
            maxWidth: '980px',
            width: '90%',
            mx: 'auto',
            p: 3,
        }}>
          <iframe src={`https://www.youtube.com/embed/${config?.youtubeId}?rel=0&amp;controls=1&amp;autoplay=0&amp;mute=0&amp;start=0`} frameBorder={0} allow="autoplay; encrypted-media" allowFullScreen={false} title="Human Ops journey" style={{ width: '100%', height: '500px', borderRadius: '9px' }}></iframe>
        </material_1.Box>
        {!isMobile && (<material_1.Box sx={{
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                mt: 6,
            }}>
            <material_1.Box sx={{ cursor: 'pointer' }} onClick={() => setValue(Math.max(0, value - 1))}>
              <img src={prev_arrow_svg_1.default} alt="prev"/>
            </material_1.Box>
            <material_1.Box sx={{ cursor: 'pointer' }} onClick={() => setValue(Math.min(how_to_human_1.HOW_TO_HUMAN_DATA.length - 1, value + 1))}>
              <img src={next_arrow_svg_1.default} alt="next"/>
            </material_1.Box>
          </material_1.Box>)}
      </material_1.Box>
    </components_1.PageWrapper>);
};
exports.HowToHuman = HowToHuman;
