"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Profile = void 0;
const material_1 = require("@mui/material");
const react_redux_1 = require("react-redux");
const wagmi_1 = require("wagmi");
const address_svg_1 = __importDefault(require("src/assets/address.svg"));
const components_1 = require("src/components");
const hooks_1 = require("src/state/leader/hooks");
const DATA = [
    {
        escrow: '******************************************',
        stake: 30000,
        payouts: 1000,
        status: 'Launched',
    },
    {
        escrow: '******************************************',
        stake: 30000,
        payouts: 1000,
        status: 'Launched',
    },
    {
        escrow: '******************************************',
        stake: 30000,
        payouts: 1000,
        status: 'Launched',
    },
    {
        escrow: '******************************************',
        stake: 30000,
        payouts: 1000,
        status: 'Launched',
    },
    {
        escrow: '******************************************',
        stake: 30000,
        payouts: 1000,
        status: 'Launched',
    },
];
const Profile = () => {
    const theme = (0, material_1.useTheme)();
    const isMobile = (0, material_1.useMediaQuery)(theme.breakpoints.down('lg'));
    const { address } = (0, wagmi_1.useAccount)();
    const chainId = (0, wagmi_1.useChainId)();
    (0, hooks_1.useFetchLeaderData)(chainId.toString(), address);
    const { currentLeader, currentLeaderLoaded } = (0, react_redux_1.useSelector)((state) => state.leader);
    return (<components_1.PageWrapper>
      {!currentLeaderLoaded ? (<material_1.Box display="flex" justifyContent="center" alignItems="center">
          <material_1.CircularProgress />
        </material_1.Box>) : (<>
          <material_1.Box display="flex" alignItems="center" flexWrap="wrap">
            <components_1.ViewTitle title="Address" iconUrl={address_svg_1.default}/>
            {!isMobile && <components_1.CopyAddressButton address={address} ml={6}/>}
            <material_1.Box ml="auto">
              <components_1.NetworkSelect value={chainId} disabled/>
            </material_1.Box>
          </material_1.Box>
          {isMobile && (<material_1.Box mt={{ xs: 4, md: 6 }}>
              <components_1.CopyAddressButton address={address}/>
            </material_1.Box>)}
          <material_1.Grid container spacing={4} mt={{ xs: 0, md: 4 }}>
            <material_1.Grid item xs={12} md={6}>
              <components_1.CardContainer densed>
                <material_1.Typography variant="body2" color="primary" fontWeight={600} sx={{ mb: 2 }}>
                  Escrow details
                </material_1.Typography>
                <material_1.Stack spacing={2}>
                  <components_1.CardTextRow label="Manifest URL" value="https://job-laucher.ai"/>
                  <components_1.CardTextRow label="Manifest Hash" value="0xe22647d4ae522f7545e7b4dda8c967"/>
                  <components_1.CardTextRow label="Balance of" value="2,000 HMT"/>
                  <components_1.CardTextRow label="Paid Out HMT" value="390 HMT"/>
                  <components_1.CardTextRow label="Amount of Jobs" value="200"/>
                  <components_1.CardTextRow label="Workers assigned" value="10"/>
                </material_1.Stack>
              </components_1.CardContainer>
            </material_1.Grid>
            <material_1.Grid item xs={12} md={6}>
              <material_1.Stack spacing={4}>
                <components_1.CardContainer densed>
                  <material_1.Typography variant="body2" color="primary" fontWeight={600} sx={{ mb: 2 }}>
                    Overview
                  </material_1.Typography>
                  <material_1.Stack spacing={2}>
                    <components_1.CardTextRow label="Role" value={currentLeader?.role || '-'}/>
                    <components_1.CardTextRow label="World Rank" value="#1003"/>
                    <components_1.CardTextRow label="Reputation" value={currentLeader?.reputation.toLocaleString() || '-'}/>
                    <components_1.CardTextRow label="Jobs Launched" value={currentLeader?.amountJobsLaunched.toLocaleString() ||
                '-'}/>
                  </material_1.Stack>
                </components_1.CardContainer>
                <components_1.CardContainer densed>
                  <material_1.Typography variant="body2" color="primary" fontWeight={600} sx={{ mb: 2 }}>
                    Stake info
                  </material_1.Typography>
                  <material_1.Stack spacing={2}>
                    <components_1.CardTextRow label="Tokens locked for withdrawal" value={currentLeader?.amountLocked
                ? `${currentLeader?.amountLocked.toLocaleString()} HMT`
                : '-'}/>
                    <components_1.CardTextRow label="Tokens locked until" value={currentLeader?.lockedUntilTimestamp
                ? new Date(currentLeader?.lockedUntilTimestamp).toDateString()
                : 'N/A'}/>
                  </material_1.Stack>
                </components_1.CardContainer>
              </material_1.Stack>
            </material_1.Grid>
          </material_1.Grid>
          <material_1.Box mt={4}>
            <material_1.Typography mb={4} variant="h6" color="primary">
              Stakes
            </material_1.Typography>
            <material_1.TableContainer component={material_1.Paper} sx={{
                borderRadius: '16px',
                boxShadow: '0px 3px 1px -2px #E9EBFA, 0px 2px 2px rgba(233, 235, 250, 0.5), 0px 1px 5px rgba(233, 235, 250, 0.2);',
            }}>
              <material_1.Table sx={{ minWidth: 650 }} aria-label="simple table">
                <material_1.TableHead>
                  <material_1.TableRow>
                    <material_1.TableCell>Escrow</material_1.TableCell>
                    <material_1.TableCell align="left">Stake</material_1.TableCell>
                    <material_1.TableCell align="left">Payouts</material_1.TableCell>
                    <material_1.TableCell align="left">Status</material_1.TableCell>
                  </material_1.TableRow>
                </material_1.TableHead>
                <material_1.TableBody>
                  {DATA.map((item) => (<material_1.TableRow key={item.escrow} sx={{ '&:last-child td, &:last-child th': { border: 0 } }}>
                      <material_1.TableCell align="left">{item.escrow}</material_1.TableCell>
                      <material_1.TableCell align="left">{item.stake} HMT</material_1.TableCell>
                      <material_1.TableCell align="left">{item.payouts} HMT</material_1.TableCell>
                      <material_1.TableCell align="left">{item.status}</material_1.TableCell>
                    </material_1.TableRow>))}
                </material_1.TableBody>
              </material_1.Table>
            </material_1.TableContainer>
          </material_1.Box>
        </>)}
    </components_1.PageWrapper>);
};
exports.Profile = Profile;
