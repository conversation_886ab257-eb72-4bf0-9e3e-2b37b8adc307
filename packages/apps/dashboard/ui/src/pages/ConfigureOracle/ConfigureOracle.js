"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ConfigureOracle = void 0;
const material_1 = require("@mui/material");
const react_1 = require("react");
const components_1 = require("src/components");
const Container_1 = require("src/components/Cards/Container");
const ConfigureOracle = () => {
    const [isSuccess, setIsSuccess] = (0, react_1.useState)(false);
    return (<components_1.PageWrapper>
      <Container_1.Container maxWidth="md" mx="auto">
        {!isSuccess ? (<>
            <material_1.Typography variant="h4" color="text.primary" textAlign="center" sx={{ mb: 9 }}>
              Configure Your Oracle
            </material_1.Typography>
            <material_1.Grid container spacing={4}>
              <material_1.Grid item xs={12} sm={6}>
                <material_1.FormControl fullWidth>
                  <material_1.InputLabel variant="standard" htmlFor="oracle-type">
                    Type Of Oracle
                  </material_1.InputLabel>
                  <material_1.NativeSelect defaultValue={0} inputProps={{
                name: 'oracleType',
                id: 'oracle-type',
            }}>
                    <option value={0}>Exchange</option>
                    <option value={1}>Reputation</option>
                  </material_1.NativeSelect>
                </material_1.FormControl>
              </material_1.Grid>
              <material_1.Grid item xs={12} sm={6}>
                <material_1.TextField variant="outlined" fullWidth placeholder="Percentage of Fees"/>
              </material_1.Grid>
              <material_1.Grid item xs={12}>
                <material_1.TextField variant="outlined" fullWidth placeholder="Webhook URL"/>
              </material_1.Grid>
              <material_1.Grid item xs={12}>
                <material_1.TextField variant="outlined" fullWidth placeholder="Oracle URL"/>
              </material_1.Grid>
              <material_1.Grid item xs={12}>
                <material_1.TextField variant="outlined" fullWidth placeholder="Refuse Notification URL"/>
              </material_1.Grid>
            </material_1.Grid>
            <material_1.Box mt={4}>
              <material_1.Button variant="contained" fullWidth onClick={() => setIsSuccess(true)}>
                Confirm
              </material_1.Button>
              <material_1.Button variant="text" fullWidth sx={{ mt: '12px' }} href="/staking">
                Back
              </material_1.Button>
            </material_1.Box>
          </>) : (<material_1.Box sx={{
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                justifyContent: 'center',
                py: 10,
            }}>
            <material_1.Typography color="primary" variant="h6" fontWeight={500} gutterBottom>
              Success!
            </material_1.Typography>
            <material_1.Typography color="primary" variant="body2" textAlign="center" mt={2}>
              Congratulations, you have successfully configured your HUMAN
              Oracle.
            </material_1.Typography>
            <material_1.Typography color="primary" variant="body2" fontWeight={600} mt={4}>
              CTA lorem ipsum{' '}
              <material_1.Link href="#" target="_blank">
                here
              </material_1.Link>
            </material_1.Typography>
          </material_1.Box>)}
      </Container_1.Container>
    </components_1.PageWrapper>);
};
exports.ConfigureOracle = ConfigureOracle;
