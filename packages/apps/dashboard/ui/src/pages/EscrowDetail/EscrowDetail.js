"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.EscrowDetail = void 0;
const material_1 = require("@mui/material");
const react_router_dom_1 = require("react-router-dom");
const address_svg_1 = __importDefault(require("src/assets/address.svg"));
const components_1 = require("src/components");
const EscrowDetail = () => {
    const theme = (0, material_1.useTheme)();
    const isMobile = (0, material_1.useMediaQuery)(theme.breakpoints.down('lg'));
    const { chainId, address } = (0, react_router_dom_1.useParams)();
    return (<components_1.PageWrapper>
      <material_1.Box display="flex" alignItems="center" flexWrap="wrap">
        <components_1.ViewTitle title="Address" iconUrl={address_svg_1.default}/>
        {!isMobile && <components_1.CopyAddressButton address={address || ''} ml={6}/>}
        <material_1.Box ml="auto">
          <components_1.NetworkSelect value={chainId} disabled/>
        </material_1.Box>
      </material_1.Box>
      {isMobile && (<material_1.Box mt={{ xs: 4, md: 6 }}>
          <components_1.CopyAddressButton address={address || ''}/>
        </material_1.Box>)}
      <material_1.Grid container spacing={4} mt={{ xs: 0, md: 4 }}>
        <material_1.Grid item xs={12} md={6}>
          <components_1.CardContainer densed>
            <material_1.Typography variant="body2" color="primary" fontWeight={600} sx={{ mb: 2 }}>
              Escrow details
            </material_1.Typography>
            <material_1.Stack spacing={2}>
              <components_1.CardTextRow label="Manifest URL" value="https://job-laucher.ai"/>
              <components_1.CardTextRow label="Manifest Hash" value="0xe22647d4ae522f7545e7b4dda8c967"/>
              <components_1.CardTextRow label="Balance of" value="2,000 HMT"/>
              <components_1.CardTextRow label="Paid Out HMT" value="390 HMT"/>
              <components_1.CardTextRow label="Amount of Jobs" value="200"/>
              <components_1.CardTextRow label="Workers assigned" value="10"/>
            </material_1.Stack>
          </components_1.CardContainer>
        </material_1.Grid>
        <material_1.Grid item xs={12} md={6}>
          <components_1.CardContainer densed>
            <material_1.Typography variant="body2" color="primary" fontWeight={600} sx={{ mb: 2 }}>
              Stake details
            </material_1.Typography>
            <material_1.Stack spacing={2}>
              <components_1.CardTextRow label="Staker" value="0xe22647d4ae522f7545e7b4dda8c967"/>
              <components_1.CardTextRow label="Staked HMT" value="2,000 HMT"/>
              <components_1.CardTextRow label="Slashed HMT" value="0 HMT"/>
            </material_1.Stack>
          </components_1.CardContainer>
        </material_1.Grid>
      </material_1.Grid>
    </components_1.PageWrapper>);
};
exports.EscrowDetail = EscrowDetail;
