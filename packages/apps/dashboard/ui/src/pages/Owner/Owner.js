"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Owner = void 0;
const material_1 = require("@mui/material");
const address_svg_1 = __importDefault(require("src/assets/address.svg"));
const components_1 = require("src/components");
const Owner = () => {
    const theme = (0, material_1.useTheme)();
    const isMobile = (0, material_1.useMediaQuery)(theme.breakpoints.down('lg'));
    return (<components_1.PageWrapper>
      <material_1.Box display="flex" alignItems="center" flexWrap="wrap">
        <components_1.ViewTitle title="Address" iconUrl={address_svg_1.default}/>
        {!isMobile && (<components_1.CopyAddressButton address="******************************************" ml={6}/>)}
        <material_1.Box ml="auto">
          <components_1.NetworkSelect />
        </material_1.Box>
      </material_1.Box>
      {isMobile && (<material_1.Box mt={{ xs: 4, md: 6 }}>
          <components_1.CopyAddressButton address="******************************************"/>
        </material_1.Box>)}
      <material_1.Grid container spacing={4} mt={{ xs: 0, md: 4 }}>
        <material_1.Grid item xs={12} sm={6}>
          <components_1.CardContainer densed>
            <material_1.Typography variant="body2" color="primary" fontWeight={600} sx={{ mb: 2 }}>
              Overview
            </material_1.Typography>
            <material_1.Stack spacing={2}>
              <components_1.CardTextRow label="Role" value="Owner"/>
              <components_1.CardTextRow label="Jobs Launched" value="15,064"/>
            </material_1.Stack>
          </components_1.CardContainer>
        </material_1.Grid>
        <material_1.Grid item xs={12} sm={6}>
          <components_1.CardContainer densed>
            <material_1.Typography variant="body2" color="primary" fontWeight={600} sx={{ mb: 2 }}>
              Stake info
            </material_1.Typography>
            <material_1.Stack spacing={2}>
              <components_1.CardTextRow label="Toekns staked" value="89,000 HMT"/>
            </material_1.Stack>
          </components_1.CardContainer>
        </material_1.Grid>
      </material_1.Grid>
    </components_1.PageWrapper>);
};
exports.Owner = Owner;
