"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.LeaderDetail = void 0;
const sdk_1 = require("@human-protocol/sdk");
const material_1 = require("@mui/material");
const react_redux_1 = require("react-redux");
const react_router_dom_1 = require("react-router-dom");
const address_svg_1 = __importDefault(require("src/assets/address.svg"));
const components_1 = require("src/components");
const hooks_1 = require("src/state/leader/hooks");
const LeaderDetail = () => {
    const theme = (0, material_1.useTheme)();
    const isMobile = (0, material_1.useMediaQuery)(theme.breakpoints.down('lg'));
    const { chainId, address } = (0, react_router_dom_1.useParams)();
    (0, hooks_1.useFetchLeaderData)(chainId, address);
    const { currentLeader, currentLeaderLoaded, leaderEscrowsLoaded, leaderEscrows, } = (0, react_redux_1.useSelector)((state) => state.leader);
    return (<components_1.PageWrapper>
      {!currentLeaderLoaded ? (<material_1.Box display="flex" justifyContent="center" alignItems="center">
          <material_1.CircularProgress />
        </material_1.Box>) : !currentLeader ? (<material_1.Box>Not Found</material_1.Box>) : (<>
          <material_1.Box display="flex" alignItems="center" flexWrap="wrap" gap={{ xs: 3, sm: 4 }}>
            <components_1.ViewTitle title="Address" iconUrl={address_svg_1.default}/>
            {!isMobile && <components_1.CopyAddressButton address={currentLeader.address}/>}
            {!isMobile ? (<material_1.Box display="flex" alignItems="center" justifyContent="space-between" flex={1}>
                <components_1.NetworkSelect value={currentLeader.chainId} disabled/>
                {currentLeader.url && (<material_1.Button href={currentLeader.url} target="_blank" variant="outlined" sx={{ minWidth: 240 }}>
                    Exchange
                  </material_1.Button>)}
              </material_1.Box>) : (<material_1.Box ml="auto">
                <components_1.NetworkSelect value={currentLeader.chainId} disabled/>
              </material_1.Box>)}
          </material_1.Box>
          {isMobile && (<material_1.Box mt={{ xs: 4, md: 6 }}>
              <components_1.CopyAddressButton address={currentLeader.address}/>
              {currentLeader.url && (<material_1.Button href={currentLeader.url} target="_blank" variant="outlined" sx={{ mt: 3 }} fullWidth>
                  Exchange
                </material_1.Button>)}
            </material_1.Box>)}
          <material_1.Grid container spacing={4} mt={{ xs: 0, md: 4 }}>
            <material_1.Grid item xs={12} sm={6}>
              <components_1.CardContainer densed>
                <material_1.Typography variant="body2" color="primary" fontWeight={600} sx={{ mb: 2 }}>
                  Overview
                </material_1.Typography>
                <material_1.Stack spacing={2}>
                  <components_1.CardTextRow label="Role" value={currentLeader?.role}/>
                  <components_1.CardTextRow label="Network" value={sdk_1.NETWORKS[currentLeader.chainId]?.title}/>
                  <components_1.CardTextRow label="World Rank" value="#1003"/>
                  <components_1.CardTextRow label="Reputation" value={currentLeader.reputation.toLocaleString()}/>
                  <components_1.CardTextRow label="Jobs Launched" value={currentLeader.amountJobsLaunched.toLocaleString()}/>
                </material_1.Stack>
              </components_1.CardContainer>
            </material_1.Grid>
            <material_1.Grid item xs={12} sm={6}>
              <components_1.CardContainer densed>
                <material_1.Typography variant="body2" color="primary" fontWeight={600} sx={{ mb: 2 }}>
                  Stake info
                </material_1.Typography>
                <material_1.Stack spacing={2}>
                  <components_1.CardTextRow label="Tokens staked" value={`${currentLeader.amountStaked.toLocaleString()} HMT`}/>
                  <components_1.CardTextRow label="Tokens allocated" value={`${currentLeader.amountAllocated.toLocaleString()} HMT`}/>
                  <components_1.CardTextRow label="Tokens locked" value={`${currentLeader.amountLocked.toLocaleString()} HMT`}/>
                  <components_1.CardTextRow label="Tokens locked until" value={currentLeader.lockedUntilTimestamp
                ? new Date(currentLeader.lockedUntilTimestamp).toDateString()
                : 'N/A'}/>
                </material_1.Stack>
              </components_1.CardContainer>
            </material_1.Grid>
          </material_1.Grid>
          <material_1.Box mt={4}>
            <material_1.Typography mb={4} variant="h6" color="primary">
              Escrows
            </material_1.Typography>
            {!leaderEscrowsLoaded ? (<material_1.Box display="flex" justifyContent="center" alignItems="center">
                <material_1.CircularProgress />
              </material_1.Box>) : (<material_1.TableContainer component={material_1.Paper} sx={{
                    borderRadius: '16px',
                    boxShadow: '0px 3px 1px -2px #E9EBFA, 0px 2px 2px rgba(233, 235, 250, 0.5), 0px 1px 5px rgba(233, 235, 250, 0.2);',
                }}>
                {!leaderEscrows?.length ? (<material_1.Box padding={2} display="flex" justifyContent="center">
                    No escrows launched yet
                  </material_1.Box>) : (<material_1.Table sx={{ minWidth: 650 }} aria-label="simple table">
                    <material_1.TableHead>
                      <material_1.TableRow>
                        <material_1.TableCell>Escrow</material_1.TableCell>
                        <material_1.TableCell align="left">Allocated</material_1.TableCell>
                        <material_1.TableCell align="left">Payouts</material_1.TableCell>
                        <material_1.TableCell align="left">Status</material_1.TableCell>
                      </material_1.TableRow>
                    </material_1.TableHead>
                    <material_1.TableBody>
                      {leaderEscrows.map((escrow) => (<material_1.TableRow key={escrow.address} sx={{
                            '&:last-child td, &:last-child th': { border: 0 },
                        }}>
                          <material_1.TableCell align="left">{escrow.address}</material_1.TableCell>
                          <material_1.TableCell align="left">
                            {escrow.amountAllocated} HMT
                          </material_1.TableCell>
                          <material_1.TableCell align="left">
                            {escrow.amountPayout} HMT
                          </material_1.TableCell>
                          <material_1.TableCell align="left">{escrow.status}</material_1.TableCell>
                        </material_1.TableRow>))}
                    </material_1.TableBody>
                  </material_1.Table>)}
              </material_1.TableContainer>)}
          </material_1.Box>
        </>)}
    </components_1.PageWrapper>);
};
exports.LeaderDetail = LeaderDetail;
