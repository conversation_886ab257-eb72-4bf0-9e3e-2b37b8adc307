<svg width="103" height="106" viewBox="0 0 103 106" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_d_1046_12291)">
<circle cx="39.0453" cy="25.7982" r="16.7273" transform="rotate(-16.5457 39.0453 25.7982)" fill="url(#paint0_radial_1046_12291)"/>
<circle cx="39.0453" cy="25.7982" r="13.7273" transform="rotate(-16.5457 39.0453 25.7982)" stroke="url(#paint1_linear_1046_12291)" stroke-width="6"/>
</g>
<path d="M30.2204 30.5003C28.5017 24.7151 31.7983 18.6319 37.5836 16.9133V16.9133C43.3688 15.1946 49.4519 18.4912 51.1706 24.2764V24.2764C52.8893 30.0616 49.5927 36.1448 43.8075 37.8635V37.8635C38.0222 39.5822 31.9391 36.2856 30.2204 30.5003V30.5003Z" fill="url(#paint2_linear_1046_12291)"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M41.4653 35.7147C46.7581 34.1423 49.7501 28.4961 48.148 23.1036C46.546 17.711 40.9567 14.6141 35.6639 16.1865C30.3711 17.7589 27.3792 23.4051 28.9812 28.7977C30.5832 34.1902 36.1726 37.2871 41.4653 35.7147ZM49.0608 22.8324C50.8154 28.7386 47.5385 34.9225 41.7416 36.6446C35.9448 38.3668 29.8231 34.975 28.0685 29.0688C26.3139 23.1627 29.5908 16.9788 35.3876 15.2566C41.1845 13.5345 47.3062 16.9263 49.0608 22.8324Z" fill="#320A8D"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M40.4985 32.46C44.027 31.4117 46.0216 27.6476 44.9536 24.0526C43.8856 20.4575 40.1594 18.393 36.6308 19.4412C33.1023 20.4895 31.1077 24.2536 32.1757 27.8486C33.2437 31.4437 36.97 33.5083 40.4985 32.46ZM45.8663 23.7814C47.0869 27.89 44.8073 32.1919 40.7747 33.3899C36.7421 34.5879 32.4836 32.2284 31.263 28.1198C30.0424 24.0112 32.322 19.7093 36.3546 18.5113C40.3872 17.3133 44.6457 19.6728 45.8663 23.7814Z" fill="#320A8D"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M39.303 21.4659C39.1109 21.4593 38.9188 21.4527 38.7315 21.4447C38.4948 21.4302 38.3512 21.7116 38.4464 21.9963C38.628 22.5365 38.8081 23.0718 38.9897 23.612C39.0482 23.7909 39.01 23.9827 38.8944 24.0913C38.6617 24.3036 38.4227 24.5126 38.1836 24.7215C37.8707 24.6819 37.555 24.6324 37.2441 24.5816C37.083 24.5552 36.9513 24.4139 36.9026 24.2321C36.7598 23.6803 36.6184 23.1335 36.4756 22.5818C36.3985 22.2863 36.126 22.1339 35.9356 22.2753C35.7782 22.3857 35.6272 22.4996 35.4684 22.6051C35.3421 22.6957 35.2817 22.8834 35.3236 23.0778C35.4662 23.7358 35.604 24.3951 35.7451 25.0481C35.7841 25.2328 35.9159 25.374 36.0735 25.4068C36.4271 25.4768 36.5544 26.0119 36.2557 26.2333C36.1212 26.3316 36.0544 26.5159 36.0934 26.7006C36.3069 27.7035 36.5217 28.7113 36.7351 29.7142C36.777 29.9087 36.9311 30.0539 37.1262 30.0702C37.3595 30.091 37.5977 30.1105 37.8359 30.1299C38.1333 30.153 38.3603 29.868 38.2831 29.5726C38.0501 28.6817 37.8219 27.7892 37.5875 26.8933C37.5387 26.7116 37.5993 26.5238 37.7323 26.4206C37.9937 26.2157 38.2502 26.0121 38.5037 25.7989C38.8311 25.8343 39.1586 25.8696 39.4895 25.8986C39.6573 25.9124 39.8105 26.0367 39.8689 26.2156C40.1602 27.0893 40.4562 27.9615 40.749 28.8401C40.8455 29.1297 41.1898 29.2396 41.4279 29.0628C41.6168 28.9165 41.8058 28.7701 41.9899 28.6253C42.1395 28.5066 42.1942 28.2993 42.1231 28.1136C41.7544 27.1568 41.3841 26.1951 41.0154 25.2383C40.9473 25.0623 40.7956 24.943 40.6243 24.9354C40.2546 24.918 40.0676 24.3953 40.3256 24.1435C40.4447 24.0286 40.4717 23.8349 40.405 23.6637C40.1653 23.0347 39.9207 22.4071 39.6824 21.7829C39.6114 21.5971 39.4568 21.468 39.303 21.4659" fill="#320A8D"/>
<g filter="url(#filter1_d_1046_12291)">
<circle cx="60.4998" cy="36.7073" r="16.7273" transform="rotate(-16.5457 60.4998 36.7073)" fill="url(#paint3_radial_1046_12291)"/>
<circle cx="60.4998" cy="36.7073" r="13.7273" transform="rotate(-16.5457 60.4998 36.7073)" stroke="url(#paint4_linear_1046_12291)" stroke-width="6"/>
</g>
<path d="M51.6749 41.4094C49.9562 35.6241 53.2528 29.541 59.038 27.8223V27.8223C64.8233 26.1036 70.9064 29.4002 72.6251 35.1855V35.1855C74.3438 40.9707 71.0472 47.0538 65.2619 48.7725V48.7725C59.4767 50.4912 53.3936 47.1946 51.6749 41.4094V41.4094Z" fill="url(#paint5_linear_1046_12291)"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M62.9198 46.6238C68.2126 45.0514 71.2045 39.4052 69.6025 34.0126C68.0005 28.6201 62.4111 25.5232 57.1183 27.0956C51.8256 28.668 48.8336 34.3142 50.4356 39.7067C52.0377 45.0993 57.627 48.1962 62.9198 46.6238ZM70.5152 33.7415C72.2698 39.6476 68.9929 45.8316 63.196 47.5537C57.3992 49.2758 51.2775 45.884 49.5229 39.9779C47.7683 34.0717 51.0452 27.8878 56.8421 26.1657C62.6389 24.4435 68.7606 27.8353 70.5152 33.7415Z" fill="#320A8D"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M61.953 43.3691C65.4815 42.3208 67.4761 38.5567 66.4081 34.9616C65.3401 31.3666 61.6138 29.302 58.0853 30.3503C54.5568 31.3985 52.5622 35.1627 53.6302 38.7577C54.6982 42.3527 58.4244 44.4173 61.953 43.3691ZM67.3208 34.6905C68.5414 38.7991 66.2618 43.101 62.2292 44.299C58.1966 45.497 53.9381 43.1375 52.7175 39.0288C51.4969 34.9202 53.7765 30.6184 57.8091 29.4204C61.8417 28.2223 66.1002 30.5819 67.3208 34.6905Z" fill="#320A8D"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M60.7576 32.375C60.5655 32.3684 60.3734 32.3618 60.1861 32.3538C59.9493 32.3392 59.8058 32.6206 59.901 32.9053C60.0826 33.4456 60.2627 33.9809 60.4443 34.5211C60.5028 34.7 60.4646 34.8917 60.349 35.0004C60.1163 35.2127 59.8772 35.4216 59.6382 35.6306C59.3253 35.591 59.0095 35.5415 58.6986 35.4906C58.5375 35.4642 58.4058 35.3229 58.3571 35.1411C58.2143 34.5894 58.073 34.0426 57.9302 33.4908C57.853 33.1954 57.5806 33.0429 57.3902 33.1844C57.2328 33.2948 57.0817 33.4087 56.923 33.5142C56.7966 33.6048 56.7362 33.7925 56.7781 33.9869C56.9207 34.6448 57.0585 35.3041 57.1997 35.9572C57.2387 36.1418 57.3705 36.2831 57.5281 36.3158C57.8817 36.3858 58.009 36.921 57.7102 37.1423C57.5758 37.2407 57.5089 37.425 57.5479 37.6096C57.7614 38.6125 57.9763 39.6204 58.1897 40.6233C58.2316 40.8177 58.3857 40.9629 58.5807 40.9793C58.814 41.0001 59.0523 41.0195 59.2905 41.039C59.5879 41.062 59.8148 40.777 59.7376 40.4817C59.5046 39.5907 59.2765 38.6983 59.0421 37.8024C58.9933 37.6206 59.0538 37.4329 59.1869 37.3297C59.4482 37.1247 59.7047 36.9212 59.9582 36.7079C60.2857 36.7433 60.6132 36.7786 60.944 36.8077C61.1118 36.8215 61.2651 36.9457 61.3235 37.1246C61.6148 37.9984 61.9108 38.8706 62.2035 39.7491C62.3001 40.0387 62.6444 40.1487 62.8824 39.9719C63.0714 39.8255 63.2604 39.6792 63.4444 39.5344C63.5941 39.4156 63.6487 39.2084 63.5777 39.0226C63.2089 38.0658 62.8387 37.1042 62.47 36.1474C62.4018 35.9714 62.2502 35.852 62.0789 35.8445C61.7092 35.8271 61.5222 35.3043 61.7802 35.0526C61.8992 34.9377 61.9263 34.744 61.8595 34.5728C61.6198 33.9438 61.3753 33.3161 61.137 32.6919C61.0659 32.5062 60.9114 32.377 60.7576 32.375" fill="#320A8D"/>
<g filter="url(#filter2_bi_1046_12291)">
<rect x="11" y="42.1976" width="85.7708" height="62.9091" rx="10" fill="url(#paint6_radial_1046_12291)" fill-opacity="0.99"/>
</g>
<g filter="url(#filter3_i_1046_12291)">
<rect x="91.0854" y="99.8837" width="74.8624" height="52.2296" rx="5" transform="rotate(180 91.0854 99.8837)" fill="url(#paint7_radial_1046_12291)" fill-opacity="0.1"/>
</g>
<rect x="16.723" y="48.1542" width="73.8624" height="51.2296" rx="4.5" stroke="url(#paint8_linear_1046_12291)" stroke-dasharray="1 1"/>
<g filter="url(#filter4_bdi_1046_12291)">
<path d="M62.2727 71.1067C62.2727 65.5838 66.7498 61.1067 72.2727 61.1067H91C95.4182 61.1067 99 64.6884 99 69.1067V76.3794C99 80.7977 95.4182 84.3794 91 84.3794H72.2727C66.7498 84.3794 62.2727 79.9023 62.2727 74.3794V71.1067Z" fill="url(#paint9_radial_1046_12291)" fill-opacity="0.99" shape-rendering="crispEdges"/>
</g>
<path d="M81.7202 71.0869C81.8065 71.4354 81.8656 71.7943 81.8951 72.1613L81.5462 72.1894C81.5599 72.3598 81.5669 72.5321 81.5669 72.7061C81.5669 72.8801 81.5599 73.0524 81.5462 73.2229L81.8951 73.2509C81.8656 73.6179 81.8065 73.9768 81.7202 74.3253L81.3805 74.2412C81.297 74.5781 81.1867 74.9048 81.0519 75.2191L81.3735 75.3571C81.2297 75.6924 81.0593 76.0141 80.8651 76.3198L80.5697 76.1321C80.3846 76.4233 80.1766 76.6994 79.9481 76.9577L80.2103 77.1896C79.9699 77.4613 79.7082 77.7144 79.4275 77.9463L79.2045 77.6765C78.9394 77.8956 78.6565 78.0948 78.3583 78.2719L78.5369 78.5728C78.2265 78.7571 77.9002 78.9185 77.5606 79.0546L77.4304 78.7297C77.1129 78.857 76.783 78.961 76.4431 79.0396L76.5219 79.3806C76.1725 79.4614 75.813 79.5166 75.4456 79.5442L75.4194 79.1952C75.2483 79.2081 75.0753 79.2146 74.9007 79.2146C74.7261 79.2146 74.5531 79.2081 74.382 79.1952L74.3558 79.5442C73.9884 79.5166 73.6289 79.4614 73.2795 79.3806L73.3583 79.0396C73.0183 78.961 72.6885 78.857 72.371 78.7297L72.2408 79.0546C71.9012 78.9185 71.5749 78.7571 71.2645 78.5728L71.4431 78.2719C71.1449 78.0948 70.862 77.8956 70.5968 77.6765L70.3739 77.9463C70.0932 77.7144 69.8314 77.4613 69.5911 77.1896L69.8533 76.9577C69.6248 76.6994 69.4168 76.4233 69.2317 76.1321L68.9363 76.3198C68.7421 76.0141 68.5717 75.6924 68.4279 75.3571L68.7495 75.2191C68.6146 74.9048 68.5043 74.5781 68.4209 74.2412L68.0812 74.3253C67.9949 73.9768 67.9358 73.6179 67.9063 73.2509L68.2552 73.2229C68.2415 73.0524 68.2345 72.8801 68.2345 72.7061C68.2345 72.5321 68.2415 72.3598 68.2552 72.1894L67.9063 72.1613C67.9358 71.7943 67.9949 71.4354 68.0812 71.0869L68.4209 71.171C68.5043 70.8341 68.6146 70.5074 68.7495 70.1931L68.4279 70.0551C68.5717 69.7198 68.7421 69.3981 68.9363 69.0924L69.2317 69.2801C69.4168 68.9889 69.6248 68.7128 69.8533 68.4545L69.5911 68.2226C69.8314 67.9509 70.0932 67.6978 70.3739 67.4659L70.5968 67.7357C70.862 67.5166 71.1449 67.3174 71.4431 67.1404L71.2645 66.8394C71.5749 66.6551 71.9012 66.4937 72.2408 66.3576L72.371 66.6825C72.6885 66.5552 73.0183 66.4512 73.3583 66.3726L73.2795 66.0316C73.6289 65.9508 73.9884 65.8956 74.3558 65.868L74.382 66.217C74.5531 66.2041 74.7261 66.1976 74.9007 66.1976C75.0753 66.1976 75.2483 66.2041 75.4194 66.217L75.4456 65.868C75.813 65.8956 76.1725 65.9508 76.5219 66.0316L76.4431 66.3726C76.783 66.4512 77.1129 66.5552 77.4304 66.6825L77.5606 66.3576C77.9002 66.4937 78.2265 66.6551 78.5369 66.8394L78.3583 67.1404C78.6565 67.3174 78.9394 67.5166 79.2045 67.7357L79.4275 67.4659C79.7082 67.6978 79.9699 67.9509 80.2103 68.2226L79.9481 68.4545C80.1766 68.7128 80.3846 68.9889 80.5697 69.2801L80.8651 69.0924C81.0593 69.3981 81.2297 69.7198 81.3735 70.0551L81.0519 70.1931C81.1867 70.5074 81.297 70.8341 81.3805 71.171L81.7202 71.0869Z" stroke="url(#paint10_linear_1046_12291)" stroke-width="0.7" stroke-dasharray="1 1"/>
<defs>
<filter id="filter0_d_1046_12291" x="0.313652" y="0.0665894" width="77.4633" height="77.4633" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="13"/>
<feGaussianBlur stdDeviation="11"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.519896 0 0 0 0 0.55789 0 0 0 0 0.775 0 0 0 0.5 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1046_12291"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1046_12291" result="shape"/>
</filter>
<filter id="filter1_d_1046_12291" x="24.7682" y="7.97565" width="71.4633" height="71.4633" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="7"/>
<feGaussianBlur stdDeviation="9.5"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.519896 0 0 0 0 0.55789 0 0 0 0 0.775 0 0 0 0.5 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1046_12291"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1046_12291" result="shape"/>
</filter>
<filter id="filter2_bi_1046_12291" x="-22" y="9.19757" width="151.771" height="128.909" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feGaussianBlur in="BackgroundImageFix" stdDeviation="16.5"/>
<feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_1046_12291"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_backgroundBlur_1046_12291" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-3"/>
<feGaussianBlur stdDeviation="3.5"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.57 0"/>
<feBlend mode="normal" in2="shape" result="effect2_innerShadow_1046_12291"/>
</filter>
<filter id="filter3_i_1046_12291" x="16.223" y="44.6542" width="74.8624" height="55.2296" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-3"/>
<feGaussianBlur stdDeviation="3.5"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.28 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_1046_12291"/>
</filter>
<filter id="filter4_bdi_1046_12291" x="50.2727" y="51.1067" width="58.7273" height="51.2727" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feGaussianBlur in="BackgroundImageFix" stdDeviation="5"/>
<feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_1046_12291"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-4" dy="10"/>
<feGaussianBlur stdDeviation="4"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.0448 0 0 0 0 0.04864 0 0 0 0 0.2752 0 0 0 0.16 0"/>
<feBlend mode="normal" in2="effect1_backgroundBlur_1046_12291" result="effect2_dropShadow_1046_12291"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect2_dropShadow_1046_12291" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="2" dy="-3"/>
<feGaussianBlur stdDeviation="3.5"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.14 0"/>
<feBlend mode="normal" in2="shape" result="effect3_innerShadow_1046_12291"/>
</filter>
<radialGradient id="paint0_radial_1046_12291" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(39.0453 14.4893) rotate(90) scale(28.0362)">
<stop stop-color="#F0F0FF"/>
<stop stop-color="#F1F1FD"/>
<stop offset="0.703125" stop-color="white"/>
</radialGradient>
<linearGradient id="paint1_linear_1046_12291" x1="59.0806" y1="42.5255" x2="61.5992" y2="31.4298" gradientUnits="userSpaceOnUse">
<stop stop-color="#F7F8FD"/>
<stop offset="1" stop-color="white"/>
</linearGradient>
<linearGradient id="paint2_linear_1046_12291" x1="28.3262" y1="24.1242" x2="49.045" y2="36.3075" gradientUnits="userSpaceOnUse">
<stop stop-color="#244CB3" stop-opacity="0.34"/>
<stop offset="1" stop-color="#B4C2E5" stop-opacity="0.07"/>
</linearGradient>
<radialGradient id="paint3_radial_1046_12291" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(60.4998 25.3984) rotate(90) scale(28.0362)">
<stop stop-color="#F0F0FF"/>
<stop stop-color="#F1F1FD"/>
<stop offset="0.703125" stop-color="white"/>
</radialGradient>
<linearGradient id="paint4_linear_1046_12291" x1="80.5351" y1="53.4345" x2="83.0537" y2="42.3388" gradientUnits="userSpaceOnUse">
<stop stop-color="#F7F8FD"/>
<stop offset="1" stop-color="white"/>
</linearGradient>
<linearGradient id="paint5_linear_1046_12291" x1="49.7806" y1="35.0332" x2="70.4995" y2="47.2165" gradientUnits="userSpaceOnUse">
<stop stop-color="#244CB3" stop-opacity="0.34"/>
<stop offset="1" stop-color="#B4C2E5" stop-opacity="0.07"/>
</linearGradient>
<radialGradient id="paint6_radial_1046_12291" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(69.7395 47.1474) rotate(143.89) scale(86.6398 71.6844)">
<stop stop-color="#171985" stop-opacity="0.58"/>
<stop offset="0.688159" stop-color="#14167D"/>
</radialGradient>
<radialGradient id="paint7_radial_1046_12291" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(92.6097 153.591) rotate(-35.4575) scale(90.0355 72.2057)">
<stop stop-color="#14167D" stop-opacity="0"/>
<stop offset="1" stop-color="#14167D"/>
</radialGradient>
<linearGradient id="paint8_linear_1046_12291" x1="5.80658" y1="109.164" x2="90.8781" y2="37.6187" gradientUnits="userSpaceOnUse">
<stop stop-color="white" stop-opacity="0.65"/>
<stop offset="1" stop-color="white" stop-opacity="0"/>
</linearGradient>
<radialGradient id="paint9_radial_1046_12291" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(68.5432 62.7533) rotate(66.8566) scale(33.4277 46.6447)">
<stop stop-color="#14167D" stop-opacity="0"/>
<stop offset="1" stop-color="#14167D"/>
</radialGradient>
<linearGradient id="paint10_linear_1046_12291" x1="68.5423" y1="65.8476" x2="79.2925" y2="80.6559" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="white" stop-opacity="0"/>
</linearGradient>
</defs>
</svg>
