<svg width="149" height="149" viewBox="0 0 149 149" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_d_1392_14938)">
<path d="M116.96 50.48C116.96 73.9411 97.9411 92.96 74.48 92.96C51.0189 92.96 32 73.9411 32 50.48C32 27.0189 51.0189 8 74.48 8C97.9411 8 116.96 27.0189 116.96 50.48Z" fill="url(#paint0_radial_1392_14938)"/>
</g>
<g filter="url(#filter1_d_1392_14938)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M74.48 87.7213C95.0478 87.7213 111.721 71.0478 111.721 50.48C111.721 29.9122 95.0478 13.2387 74.48 13.2387C53.9122 13.2387 37.2387 29.9122 37.2387 50.48C37.2387 71.0478 53.9122 87.7213 74.48 87.7213ZM74.48 92.96C97.9411 92.96 116.96 73.9411 116.96 50.48C116.96 27.0189 97.9411 8 74.48 8C51.0189 8 32 27.0189 32 50.48C32 73.9411 51.0189 92.96 74.48 92.96Z" fill="url(#paint1_linear_1392_14938)"/>
</g>
<path fill-rule="evenodd" clip-rule="evenodd" d="M60.3828 45.0851V61.6385L77.201 70.919L94.0193 61.6385V44.4739L77.7876 35.7719L60.3828 45.0851Z" fill="url(#paint2_linear_1392_14938)"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M73.4068 29L89.855 37.818V55.6491L72.9275 64.99L56 55.6491V38.3143L73.4068 29Z" stroke="#320A8D" stroke-width="1.6"/>
<path d="M56.3438 38.6203L72.9448 47.3161L90.1107 38.1121" stroke="#320A8D" stroke-width="1.6"/>
<path d="M72.9512 64.9898V47.2029" stroke="#320A8D" stroke-width="1.6"/>
<defs>
<filter id="filter0_d_1392_14938" x="0" y="0" width="148.96" height="148.96" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="24"/>
<feGaussianBlur stdDeviation="16"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.0486111 0 0 0 0 0.127083 0 0 0 0 0.833333 0 0 0 0.06 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1392_14938"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1392_14938" result="shape"/>
</filter>
<filter id="filter1_d_1392_14938" x="0" y="0" width="148.96" height="148.96" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="24"/>
<feGaussianBlur stdDeviation="16"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.0486111 0 0 0 0 0.127083 0 0 0 0 0.833333 0 0 0 0.06 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1392_14938"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1392_14938" result="shape"/>
</filter>
<radialGradient id="paint0_radial_1392_14938" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(74.48 21.7604) rotate(90) scale(71.1997)">
<stop stop-color="#F0F0FF"/>
<stop stop-color="#F1F1FD"/>
<stop offset="0.703125" stop-color="white"/>
</radialGradient>
<linearGradient id="paint1_linear_1392_14938" x1="125.361" y1="92.96" x2="131.757" y2="64.7817" gradientUnits="userSpaceOnUse">
<stop stop-color="#F7F8FD"/>
<stop offset="1" stop-color="white"/>
</linearGradient>
<linearGradient id="paint2_linear_1392_14938" x1="77.2011" y1="31.2752" x2="77.2011" y2="75.4464" gradientUnits="userSpaceOnUse">
<stop offset="0.015625" stop-color="#F9FAFF"/>
<stop offset="0.692708" stop-color="#C9D4F4"/>
</linearGradient>
</defs>
</svg>
