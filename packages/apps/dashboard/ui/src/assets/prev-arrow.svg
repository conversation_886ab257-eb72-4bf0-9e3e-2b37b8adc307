<svg width="112" height="112" viewBox="0 0 112 112" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="arrow">
<g id="Oval" filter="url(#filter0_bd_4316_65704)">
<circle cx="40" cy="40" r="40" transform="matrix(-1 0 0 1 96 4)" fill="url(#paint0_linear_4316_65704)"/>
<circle cx="40" cy="40" r="36" transform="matrix(-1 0 0 1 96 4)" stroke="url(#paint1_linear_4316_65704)" stroke-width="8"/>
</g>
<g id="Group 3">
<path id="Path 2" d="M52 36L44 44L52 52" stroke="#320A8D"/>
<path id="Path 3" d="M44 44H68" stroke="#320A8D"/>
</g>
</g>
<defs>
<filter id="filter0_bd_4316_65704" x="0" y="-4.15485" width="112" height="116.155" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feGaussianBlur in="BackgroundImageFix" stdDeviation="4.07742"/>
<feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_4316_65704"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="12"/>
<feGaussianBlur stdDeviation="8"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.0486111 0 0 0 0 0.127083 0 0 0 0 0.833333 0 0 0 0.07 0"/>
<feBlend mode="normal" in2="effect1_backgroundBlur_4316_65704" result="effect2_dropShadow_4316_65704"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect2_dropShadow_4316_65704" result="shape"/>
</filter>
<linearGradient id="paint0_linear_4316_65704" x1="-3.11594" y1="-3.68932" x2="39.0031" y2="56.9185" gradientUnits="userSpaceOnUse">
<stop stop-color="#F0F2FC"/>
<stop offset="1" stop-color="white"/>
</linearGradient>
<linearGradient id="paint1_linear_4316_65704" x1="87.9104" y1="80" x2="93.9333" y2="53.4668" gradientUnits="userSpaceOnUse">
<stop stop-color="#F7F8FD"/>
<stop offset="1" stop-color="white"/>
</linearGradient>
</defs>
</svg>
