<svg width="149" height="149" viewBox="0 0 149 149" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_d_1392_14914)">
<path d="M116.96 50.48C116.96 73.9411 97.9411 92.96 74.48 92.96C51.0189 92.96 32 73.9411 32 50.48C32 27.0189 51.0189 8 74.48 8C97.9411 8 116.96 27.0189 116.96 50.48Z" fill="url(#paint0_radial_1392_14914)"/>
</g>
<g filter="url(#filter1_d_1392_14914)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M74.48 87.7213C95.0478 87.7213 111.721 71.0478 111.721 50.48C111.721 29.9122 95.0478 13.2387 74.48 13.2387C53.9122 13.2387 37.2387 29.9122 37.2387 50.48C37.2387 71.0478 53.9122 87.7213 74.48 87.7213ZM74.48 92.96C97.9411 92.96 116.96 73.9411 116.96 50.48C116.96 27.0189 97.9411 8 74.48 8C51.0189 8 32 27.0189 32 50.48C32 73.9411 51.0189 92.96 74.48 92.96Z" fill="url(#paint1_linear_1392_14914)"/>
</g>
<path d="M59.9521 46.6648C59.9521 45.0386 61.2705 43.7203 62.8967 43.7203H93.8147C95.4409 43.7203 96.7593 45.0386 96.7593 46.6648V63.5961C96.7593 65.2224 95.4409 66.5407 93.8147 66.5407H62.8967C61.2705 66.5407 59.9521 65.2224 59.9521 63.5961V46.6648Z" fill="url(#paint2_linear_1392_14914)"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M69.375 45.6335C69.375 45.2269 69.7046 44.8973 70.1111 44.8973H72.7613C73.1678 44.8973 73.4974 45.2269 73.4974 45.6335C73.4974 46.04 73.1678 46.3696 72.7613 46.3696H70.1111C69.7046 46.3696 69.375 46.04 69.375 45.6335Z" fill="#320A8D"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M55.5358 44.0142L55.5358 56.9703C55.5358 59.4097 57.5133 61.3872 59.9526 61.3872L82.9203 61.3871C85.3596 61.3871 87.3371 59.4097 87.3371 56.9703L87.3371 44.0142C87.3371 41.5748 85.3596 39.5973 82.9203 39.5973L59.9526 39.5973C57.5133 39.5973 55.5358 41.5748 55.5358 44.0142ZM54.0635 56.9703C54.0635 60.2228 56.7001 62.8594 59.9526 62.8594L82.9203 62.8594C86.1727 62.8594 88.8094 60.2228 88.8094 56.9703L88.8094 44.0142C88.8094 40.7617 86.1727 38.1251 82.9203 38.1251L59.9526 38.1251C56.7001 38.1251 54.0635 40.7617 54.0635 44.0142L54.0635 56.9703Z" fill="#320A8D"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M55.5358 44.0142L55.5358 56.9703C55.5358 59.4097 57.5133 61.3872 59.9526 61.3872L87.0427 61.3871C89.482 61.3871 91.4595 59.4097 91.4595 56.9703L91.4595 44.0142C91.4595 41.5748 89.482 39.5973 87.0427 39.5973L59.9526 39.5973C57.5133 39.5973 55.5358 41.5748 55.5358 44.0142ZM54.0635 56.9703C54.0635 60.2228 56.7001 62.8594 59.9526 62.8594L87.0427 62.8594C90.2951 62.8594 92.9318 60.2228 92.9318 56.9703L92.9318 44.0142C92.9318 40.7617 90.2951 38.1251 87.0427 38.1251L59.9526 38.1251C56.7001 38.1251 54.0635 40.7617 54.0635 44.0142L54.0635 56.9703Z" fill="#320A8D"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M68.4055 32.6792H76.4149C76.9247 32.6668 77.4321 32.7508 77.9083 32.9264C78.3882 33.1033 78.827 33.3697 79.1996 33.7102C79.5721 34.0508 79.8711 34.4588 80.0793 34.911C80.2876 35.3633 80.4008 35.8506 80.4127 36.3448L80.413 36.3534V38.3615H78.8833V36.3711C78.875 36.0732 78.8063 35.7799 78.6809 35.5076C78.5544 35.2328 78.3726 34.9848 78.1462 34.7778C77.9197 34.5707 77.6529 34.4088 77.3613 34.3013C77.0696 34.1938 76.7588 34.1428 76.4466 34.1512L76.4359 34.1515H68.3845L68.3738 34.1512C68.0616 34.1428 67.7508 34.1938 67.4591 34.3013C67.1675 34.4088 66.9007 34.5707 66.6742 34.7778C66.4478 34.9848 66.266 35.2328 66.1395 35.5076C66.014 35.7801 65.9452 36.0735 65.9369 36.3712V38.2773H64.4072V36.3534L64.4074 36.3448C64.4194 35.8503 64.5329 35.3631 64.7411 34.911C64.9493 34.4588 65.2483 34.0508 65.6208 33.7102C65.9934 33.3697 66.4322 33.1033 66.912 32.9264C67.3883 32.7508 67.8957 32.6668 68.4055 32.6792Z" fill="#320A8D"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M54.5567 43.1557C54.7062 42.7776 55.1339 42.5924 55.5119 42.7419L68.374 47.8282C70.461 48.6536 72.784 48.6536 74.8711 47.8282L87.3608 42.8891C87.7389 42.7396 88.1666 42.9249 88.3161 43.3029C88.4656 43.681 88.2803 44.1087 87.9022 44.2582L75.4125 49.1973C72.9776 50.1602 70.2675 50.1602 67.8325 49.1973L54.9705 44.111C54.5924 43.9615 54.4072 43.5338 54.5567 43.1557Z" fill="#320A8D"/>
<defs>
<filter id="filter0_d_1392_14914" x="0" y="0" width="148.96" height="148.96" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="24"/>
<feGaussianBlur stdDeviation="16"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.0486111 0 0 0 0 0.127083 0 0 0 0 0.833333 0 0 0 0.06 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1392_14914"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1392_14914" result="shape"/>
</filter>
<filter id="filter1_d_1392_14914" x="0" y="0" width="148.96" height="148.96" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="24"/>
<feGaussianBlur stdDeviation="16"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.0486111 0 0 0 0 0.127083 0 0 0 0 0.833333 0 0 0 0.06 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1392_14914"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1392_14914" result="shape"/>
</filter>
<radialGradient id="paint0_radial_1392_14914" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(74.48 21.7604) rotate(90) scale(71.1997)">
<stop stop-color="#F0F0FF"/>
<stop stop-color="#F1F1FD"/>
<stop offset="0.703125" stop-color="white"/>
</radialGradient>
<linearGradient id="paint1_linear_1392_14914" x1="125.361" y1="92.96" x2="131.757" y2="64.7817" gradientUnits="userSpaceOnUse">
<stop stop-color="#F7F8FD"/>
<stop offset="1" stop-color="white"/>
</linearGradient>
<linearGradient id="paint2_linear_1392_14914" x1="66.4701" y1="43.7203" x2="93.2838" y2="72.539" gradientUnits="userSpaceOnUse">
<stop stop-color="#244CB3" stop-opacity="0.2"/>
<stop offset="1" stop-color="#B4C2E5" stop-opacity="0.07"/>
</linearGradient>
</defs>
</svg>
