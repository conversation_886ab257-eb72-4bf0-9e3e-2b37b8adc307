{"extends": "../../../../tsconfig.json", "compilerOptions": {"target": "es6", "lib": ["dom", "dom.iterable", "esnext", "es2015.promise"], "allowJs": true, "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "forceConsistentCasingInFileNames": true, "noFallthroughCasesInSwitch": true, "module": "esnext", "moduleResolution": "node", "isolatedModules": true, "noEmit": true, "jsx": "react-jsx", "resolveJsonModule": true, "downlevelIteration": true, "baseUrl": "./", "types": ["node", "jest", "@testing-library/jest-dom"]}, "include": ["src", "tests"]}