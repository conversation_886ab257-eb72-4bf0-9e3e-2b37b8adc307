{"private": "true", "name": "@human-protocol/dashboard-ui", "version": "1.0.0", "description": "Human Protocol Dashboard", "license": "MIT", "dependencies": {"@emotion/react": "^11.10.5", "@emotion/styled": "^11.10.5", "@human-protocol/sdk": "*", "@mui/icons-material": "^6.1.1", "@mui/material": "^5.16.7", "@reduxjs/toolkit": "^2.2.7", "@tanstack/query-sync-storage-persister": "^5.38.0", "@tanstack/react-query": "^5.49.2", "@tanstack/react-query-persist-client": "^5.51.11", "axios": "^1.2.3", "bignumber.js": "^9.1.0", "copy-to-clipboard": "^3.3.3", "dayjs": "^1.11.12", "ethers": "^6.12.1", "fast-json-stable-stringify": "^2.1.0", "file-saver": "^2.0.5", "graphql": "^16.6.0", "jszip": "^3.10.1", "nft.storage": "^7.2.0", "numeral": "^2.0.6", "openpgp": "^5.11.2", "react": "^18.2.0", "react-dom": "^18.2.0", "react-gtm-module": "^2.0.11", "react-loading-skeleton": "^3.3.1", "react-redux": "^9.1.0", "react-router-dom": "^6.4.3", "react-test-renderer": "^18.2.0", "recharts": "2.9.0", "serve": "^14.1.1", "swr": "^2.2.4", "viem": "2.x", "wagmi": "^2.12.8", "web-vitals": "^4.2.2"}, "devDependencies": {"@testing-library/jest-dom": "^6.5.0", "@testing-library/react": "^15.0.7", "@types/crypto-js": "^4.1.2", "@types/file-saver": "^2.0.5", "@types/glob": "^8.1.0", "@types/numeral": "^2.0.2", "@types/react": "^18.2.43", "@types/react-dom": "^18.2.25", "@types/react-gtm-module": "^2.0.3", "@types/react-test-renderer": "^18.0.0", "@vitejs/plugin-react": "^4.2.1", "dotenv": "^16.3.2", "eslint-config-react-app": "^7.0.1", "eslint-import-resolver-typescript": "^3.5.2", "eslint-plugin-import": "^2.29.0", "eslint-plugin-react": "^7.34.3", "eslint-plugin-react-hooks": "^4.6.0", "happy-dom": "^12.9.1", "identity-obj-proxy": "^3.0.0", "jsdom": "^25.0.1", "resize-observer-polyfill": "^1.5.1", "vite": "^5.4.7", "vite-plugin-node-polyfills": "^0.22.0", "vitest": "^1.6.0"}, "scripts": {"lint": "eslint '**/*.{ts,tsx}'", "start": "vite", "build": "vite build", "preview": "vite preview", "start-prod": "serve -s dist", "test": "vitest -u", "format:prettier": "prettier --write '**/*.{ts,tsx}'", "format:lint": "eslint --fix '**/*.{ts,tsx}'", "format": "yarn format:prettier && yarn format:lint", "vercel-build": "yarn workspace @human-protocol/sdk build && yarn build"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "lint-staged": {"*.{ts,tsx}": ["prettier --write", "eslint --fix"]}, "resolutions": {"gluegun": "^5.0.0", "mocha": "^10.0.0", "node-fetch": "^2.6.7", "node-forge": "^1.0.0", "qrcode": "^1.5.0", "semver": "^7.5.2"}}