{"extends": ["plugin:import/recommended", "plugin:import/typescript", "plugin:prettier/recommended", "react-app"], "settings": {"import/resolver": {"typescript": {}, "node": {"extensions": [".js", ".jsx", ".ts", ".tsx", ".d.ts"]}}, "import/parsers": {"@typescript-eslint/parser": [".ts", ".tsx"]}}, "parserOptions": {"ecmaVersion": 2020, "sourceType": "module"}, "rules": {"no-console": "warn", "no-unused-vars": "off", "import/extensions": ["error", "ignorePackages", {"js": "never", "jsx": "never", "ts": "never", "tsx": "never"}], "import/order": ["error", {"pathGroups": [{"pattern": "~/**", "group": "external"}], "alphabetize": {"order": "asc", "caseInsensitive": true}}], "@typescript-eslint/no-unused-vars": ["error"], "prettier/prettier": ["error"]}, "overrides": [{"files": ["*.ts", "*.tsx"], "rules": {"no-undef": "off"}}], "env": {"browser": true, "jest": true, "node": true}}