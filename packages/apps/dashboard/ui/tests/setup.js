"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
const matchers = __importStar(require("@testing-library/jest-dom/matchers"));
const react_1 = require("@testing-library/react");
const vitest_1 = require("vitest");
global.ResizeObserver = require('resize-observer-polyfill');
// extends Vitest's expect method with methods from react-testing-library
vitest_1.expect.extend(matchers);
// runs a cleanup after each test case (e.g. clearing jsdom)
(0, vitest_1.afterEach)(() => {
    (0, react_1.cleanup)();
});
