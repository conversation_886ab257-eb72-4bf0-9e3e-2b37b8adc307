"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Providers = exports.wagmiConfig = exports.testChains = void 0;
const react_query_1 = require("@tanstack/react-query");
const wagmi_1 = require("wagmi");
const chains_1 = require("wagmi/chains");
const connectors_1 = require("wagmi/connectors");
const foundryMainnet = {
    ...chains_1.mainnet,
    rpcUrls: chains_1.foundry.rpcUrls,
};
exports.testChains = [foundryMainnet, chains_1.mainnet, chains_1.goerli, chains_1.optimism, chains_1.polygon];
exports.wagmiConfig = (0, wagmi_1.createConfig)({
    chains: [chains_1.mainnet, chains_1.sepolia],
    connectors: [
        (0, connectors_1.mock)({
            accounts: [
                '0xf39Fd6e51aad88F6F4ce6aB8827279cffFb92266',
                '0x70997970c51812dc3a010c7d01b50e0d17dc79c8',
                '0x3C44CdDdB6a900fa2b585dd299e03d12FA4293BC',
            ],
        }),
    ],
    transports: {
        [chains_1.mainnet.id]: (0, wagmi_1.http)(),
        [chains_1.sepolia.id]: (0, wagmi_1.http)(),
    },
});
const queryClient = new react_query_1.QueryClient();
const Providers = ({ children }) => (<wagmi_1.WagmiProvider config={exports.wagmiConfig}>
    <react_query_1.QueryClientProvider client={queryClient}>{children}</react_query_1.QueryClientProvider>
  </wagmi_1.WagmiProvider>);
exports.Providers = Providers;
