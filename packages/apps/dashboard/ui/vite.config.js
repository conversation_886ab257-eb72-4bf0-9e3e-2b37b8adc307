"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
/// <reference types="vitest" />
/// <reference types="vite/client" />
const path_1 = __importDefault(require("path"));
const plugin_react_1 = __importDefault(require("@vitejs/plugin-react"));
const dotenv = __importStar(require("dotenv"));
const vite_1 = require("vite");
const vite_plugin_node_polyfills_1 = require("vite-plugin-node-polyfills");
dotenv.config();
// https://vitejs.dev/config/
exports.default = (0, vite_1.defineConfig)(({ mode }) => {
    return {
        plugins: [
            (0, plugin_react_1.default)(),
            (0, vite_plugin_node_polyfills_1.nodePolyfills)({
                // Whether to polyfill `node:` protocol imports.
                protocolImports: true,
            }),
        ],
        worker: {
            plugins: () => (0, plugin_react_1.default)(),
        },
        resolve: {
            alias: [
                { find: 'src', replacement: path_1.default.resolve(__dirname, 'src') },
                { find: 'tests', replacement: path_1.default.resolve(__dirname, 'tests') },
            ],
        },
        test: {
            globals: true,
            environment: 'happy-dom',
            setupFiles: './tests/setup.ts',
            coverage: {
                reporter: ['text', 'json', 'html'],
            },
        },
        optimizeDeps: {
            include: ['@human-protocol/sdk'],
        },
        build: {
            commonjsOptions: {
                include: [/human-protocol-sdk/, /node_modules/],
            },
        },
        server: {
            port: 3002,
        },
    };
});
