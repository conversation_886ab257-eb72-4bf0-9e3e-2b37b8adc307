# human_protocol_sdk.kvstore package

This module allows the contract interaction with the key value storage on chain.

## Submodules

* [human_protocol_sdk.kvstore.kvstore_client module](human_protocol_sdk.kvstore.kvstore_client.md)
  * [Code Example](human_protocol_sdk.kvstore.kvstore_client.md#code-example)
  * [Module](human_protocol_sdk.kvstore.kvstore_client.md#module)
  * [`KVStoreClient`](human_protocol_sdk.kvstore.kvstore_client.md#human_protocol_sdk.kvstore.kvstore_client.KVStoreClient)
    * [`KVStoreClient.__init__()`](human_protocol_sdk.kvstore.kvstore_client.md#human_protocol_sdk.kvstore.kvstore_client.KVStoreClient.__init__)
    * [`KVStoreClient.set()`](human_protocol_sdk.kvstore.kvstore_client.md#human_protocol_sdk.kvstore.kvstore_client.KVStoreClient.set)
    * [`KVStoreClient.set_bulk()`](human_protocol_sdk.kvstore.kvstore_client.md#human_protocol_sdk.kvstore.kvstore_client.KVStoreClient.set_bulk)
    * [`KVStoreClient.set_file_url_and_hash()`](human_protocol_sdk.kvstore.kvstore_client.md#human_protocol_sdk.kvstore.kvstore_client.KVStoreClient.set_file_url_and_hash)
  * [`KVStoreClientError`](human_protocol_sdk.kvstore.kvstore_client.md#human_protocol_sdk.kvstore.kvstore_client.KVStoreClientError)
