# human_protocol_sdk package

## Subpackages

* [human_protocol_sdk.agreement package](human_protocol_sdk.agreement.md)
  * [Getting Started](human_protocol_sdk.agreement.md#getting-started)
    * [A simple example](human_protocol_sdk.agreement.md#a-simple-example)
  * [Submodules](human_protocol_sdk.agreement.md#submodules)
    * [human_protocol_sdk.agreement.bootstrap module](human_protocol_sdk.agreement.bootstrap.md)
      * [`confidence_intervals()`](human_protocol_sdk.agreement.bootstrap.md#human_protocol_sdk.agreement.bootstrap.confidence_intervals)
    * [human_protocol_sdk.agreement.measures module](human_protocol_sdk.agreement.measures.md)
      * [`agreement()`](human_protocol_sdk.agreement.measures.md#human_protocol_sdk.agreement.measures.agreement)
      * [`cohens_kappa()`](human_protocol_sdk.agreement.measures.md#human_protocol_sdk.agreement.measures.cohens_kappa)
      * [`fleiss_kappa()`](human_protocol_sdk.agreement.measures.md#human_protocol_sdk.agreement.measures.fleiss_kappa)
      * [`krippendorffs_alpha()`](human_protocol_sdk.agreement.measures.md#human_protocol_sdk.agreement.measures.krippendorffs_alpha)
      * [`percentage()`](human_protocol_sdk.agreement.measures.md#human_protocol_sdk.agreement.measures.percentage)
      * [`sigma()`](human_protocol_sdk.agreement.measures.md#human_protocol_sdk.agreement.measures.sigma)
    * [human_protocol_sdk.agreement.utils module](human_protocol_sdk.agreement.utils.md)
      * [`NormalDistribution`](human_protocol_sdk.agreement.utils.md#human_protocol_sdk.agreement.utils.NormalDistribution)
      * [`confusion_matrix()`](human_protocol_sdk.agreement.utils.md#human_protocol_sdk.agreement.utils.confusion_matrix)
      * [`label_counts()`](human_protocol_sdk.agreement.utils.md#human_protocol_sdk.agreement.utils.label_counts)
      * [`observed_and_expected_differences()`](human_protocol_sdk.agreement.utils.md#human_protocol_sdk.agreement.utils.observed_and_expected_differences)
      * [`records_from_annotations()`](human_protocol_sdk.agreement.utils.md#human_protocol_sdk.agreement.utils.records_from_annotations)
* [human_protocol_sdk.encryption package](human_protocol_sdk.encryption.md)
  * [Submodules](human_protocol_sdk.encryption.md#submodules)
    * [human_protocol_sdk.encryption.encryption module](human_protocol_sdk.encryption.encryption.md)
      * [Code Example](human_protocol_sdk.encryption.encryption.md#code-example)
      * [Module](human_protocol_sdk.encryption.encryption.md#module)
      * [`Encryption`](human_protocol_sdk.encryption.encryption.md#human_protocol_sdk.encryption.encryption.Encryption)
    * [human_protocol_sdk.encryption.encryption_utils module](human_protocol_sdk.encryption.encryption_utils.md)
      * [Code Example](human_protocol_sdk.encryption.encryption_utils.md#code-example)
      * [Module](human_protocol_sdk.encryption.encryption_utils.md#module)
      * [`EncryptionUtils`](human_protocol_sdk.encryption.encryption_utils.md#human_protocol_sdk.encryption.encryption_utils.EncryptionUtils)
* [human_protocol_sdk.escrow package](human_protocol_sdk.escrow.md)
  * [Submodules](human_protocol_sdk.escrow.md#submodules)
    * [human_protocol_sdk.escrow.escrow_client module](human_protocol_sdk.escrow.escrow_client.md)
      * [Code Example](human_protocol_sdk.escrow.escrow_client.md#code-example)
      * [Module](human_protocol_sdk.escrow.escrow_client.md#module)
      * [`EscrowCancel`](human_protocol_sdk.escrow.escrow_client.md#human_protocol_sdk.escrow.escrow_client.EscrowCancel)
      * [`EscrowClient`](human_protocol_sdk.escrow.escrow_client.md#human_protocol_sdk.escrow.escrow_client.EscrowClient)
      * [`EscrowClientError`](human_protocol_sdk.escrow.escrow_client.md#human_protocol_sdk.escrow.escrow_client.EscrowClientError)
      * [`EscrowConfig`](human_protocol_sdk.escrow.escrow_client.md#human_protocol_sdk.escrow.escrow_client.EscrowConfig)
    * [human_protocol_sdk.escrow.escrow_utils module](human_protocol_sdk.escrow.escrow_utils.md)
      * [Code Example](human_protocol_sdk.escrow.escrow_utils.md#code-example)
      * [Module](human_protocol_sdk.escrow.escrow_utils.md#module)
      * [`EscrowData`](human_protocol_sdk.escrow.escrow_utils.md#human_protocol_sdk.escrow.escrow_utils.EscrowData)
      * [`EscrowUtils`](human_protocol_sdk.escrow.escrow_utils.md#human_protocol_sdk.escrow.escrow_utils.EscrowUtils)
      * [`StatusEvent`](human_protocol_sdk.escrow.escrow_utils.md#human_protocol_sdk.escrow.escrow_utils.StatusEvent)
* [human_protocol_sdk.kvstore package](human_protocol_sdk.kvstore.md)
  * [Submodules](human_protocol_sdk.kvstore.md#submodules)
    * [human_protocol_sdk.kvstore.kvstore_client module](human_protocol_sdk.kvstore.kvstore_client.md)
      * [Code Example](human_protocol_sdk.kvstore.kvstore_client.md#code-example)
      * [Module](human_protocol_sdk.kvstore.kvstore_client.md#module)
      * [`KVStoreClient`](human_protocol_sdk.kvstore.kvstore_client.md#human_protocol_sdk.kvstore.kvstore_client.KVStoreClient)
      * [`KVStoreClientError`](human_protocol_sdk.kvstore.kvstore_client.md#human_protocol_sdk.kvstore.kvstore_client.KVStoreClientError)
* [human_protocol_sdk.operator package](human_protocol_sdk.operator.md)
  * [Submodules](human_protocol_sdk.operator.md#submodules)
    * [human_protocol_sdk.operator.operator_utils module](human_protocol_sdk.operator.operator_utils.md)
      * [Code Example](human_protocol_sdk.operator.operator_utils.md#code-example)
      * [Module](human_protocol_sdk.operator.operator_utils.md#module)
      * [`LeaderData`](human_protocol_sdk.operator.operator_utils.md#human_protocol_sdk.operator.operator_utils.LeaderData)
      * [`LeaderFilter`](human_protocol_sdk.operator.operator_utils.md#human_protocol_sdk.operator.operator_utils.LeaderFilter)
      * [`Operator`](human_protocol_sdk.operator.operator_utils.md#human_protocol_sdk.operator.operator_utils.Operator)
      * [`OperatorUtils`](human_protocol_sdk.operator.operator_utils.md#human_protocol_sdk.operator.operator_utils.OperatorUtils)
      * [`OperatorUtilsError`](human_protocol_sdk.operator.operator_utils.md#human_protocol_sdk.operator.operator_utils.OperatorUtilsError)
      * [`RewardData`](human_protocol_sdk.operator.operator_utils.md#human_protocol_sdk.operator.operator_utils.RewardData)
* [human_protocol_sdk.staking package](human_protocol_sdk.staking.md)
  * [Submodules](human_protocol_sdk.staking.md#submodules)
    * [human_protocol_sdk.staking.staking_client module](human_protocol_sdk.staking.staking_client.md)
      * [Code Example](human_protocol_sdk.staking.staking_client.md#code-example)
      * [Module](human_protocol_sdk.staking.staking_client.md#module)
      * [`AllocationData`](human_protocol_sdk.staking.staking_client.md#human_protocol_sdk.staking.staking_client.AllocationData)
      * [`StakingClient`](human_protocol_sdk.staking.staking_client.md#human_protocol_sdk.staking.staking_client.StakingClient)
      * [`StakingClientError`](human_protocol_sdk.staking.staking_client.md#human_protocol_sdk.staking.staking_client.StakingClientError)
* [human_protocol_sdk.statistics package](human_protocol_sdk.statistics.md)
  * [Submodules](human_protocol_sdk.statistics.md#submodules)
    * [human_protocol_sdk.statistics.statistics_client module](human_protocol_sdk.statistics.statistics_client.md)
      * [Code Example](human_protocol_sdk.statistics.statistics_client.md#code-example)
      * [Module](human_protocol_sdk.statistics.statistics_client.md#module)
      * [`DailyEscrowData`](human_protocol_sdk.statistics.statistics_client.md#human_protocol_sdk.statistics.statistics_client.DailyEscrowData)
      * [`DailyHMTData`](human_protocol_sdk.statistics.statistics_client.md#human_protocol_sdk.statistics.statistics_client.DailyHMTData)
      * [`DailyPaymentData`](human_protocol_sdk.statistics.statistics_client.md#human_protocol_sdk.statistics.statistics_client.DailyPaymentData)
      * [`DailyWorkerData`](human_protocol_sdk.statistics.statistics_client.md#human_protocol_sdk.statistics.statistics_client.DailyWorkerData)
      * [`EscrowStatistics`](human_protocol_sdk.statistics.statistics_client.md#human_protocol_sdk.statistics.statistics_client.EscrowStatistics)
      * [`HMTHolder`](human_protocol_sdk.statistics.statistics_client.md#human_protocol_sdk.statistics.statistics_client.HMTHolder)
      * [`HMTHoldersParam`](human_protocol_sdk.statistics.statistics_client.md#human_protocol_sdk.statistics.statistics_client.HMTHoldersParam)
      * [`HMTStatistics`](human_protocol_sdk.statistics.statistics_client.md#human_protocol_sdk.statistics.statistics_client.HMTStatistics)
      * [`PaymentStatistics`](human_protocol_sdk.statistics.statistics_client.md#human_protocol_sdk.statistics.statistics_client.PaymentStatistics)
      * [`StatisticsClient`](human_protocol_sdk.statistics.statistics_client.md#human_protocol_sdk.statistics.statistics_client.StatisticsClient)
      * [`StatisticsClientError`](human_protocol_sdk.statistics.statistics_client.md#human_protocol_sdk.statistics.statistics_client.StatisticsClientError)
      * [`WorkerStatistics`](human_protocol_sdk.statistics.statistics_client.md#human_protocol_sdk.statistics.statistics_client.WorkerStatistics)
* [human_protocol_sdk.storage package](human_protocol_sdk.storage.md)
  * [Submodules](human_protocol_sdk.storage.md#submodules)
    * [human_protocol_sdk.storage.storage_client module](human_protocol_sdk.storage.storage_client.md)
      * [Code Example](human_protocol_sdk.storage.storage_client.md#code-example)
      * [Module](human_protocol_sdk.storage.storage_client.md#module)
      * [`Credentials`](human_protocol_sdk.storage.storage_client.md#human_protocol_sdk.storage.storage_client.Credentials)
      * [`StorageClient`](human_protocol_sdk.storage.storage_client.md#human_protocol_sdk.storage.storage_client.StorageClient)
      * [`StorageClientError`](human_protocol_sdk.storage.storage_client.md#human_protocol_sdk.storage.storage_client.StorageClientError)
      * [`StorageFileNotFoundError`](human_protocol_sdk.storage.storage_client.md#human_protocol_sdk.storage.storage_client.StorageFileNotFoundError)
    * [human_protocol_sdk.storage.storage_utils module](human_protocol_sdk.storage.storage_utils.md)
      * [`StorageUtils`](human_protocol_sdk.storage.storage_utils.md#human_protocol_sdk.storage.storage_utils.StorageUtils)

## Submodules

* [human_protocol_sdk.constants module](human_protocol_sdk.constants.md)
  * [`ChainId`](human_protocol_sdk.constants.md#human_protocol_sdk.constants.ChainId)
    * [`ChainId.AVALANCHE`](human_protocol_sdk.constants.md#human_protocol_sdk.constants.ChainId.AVALANCHE)
    * [`ChainId.AVALANCHE_TESTNET`](human_protocol_sdk.constants.md#human_protocol_sdk.constants.ChainId.AVALANCHE_TESTNET)
    * [`ChainId.BSC_MAINNET`](human_protocol_sdk.constants.md#human_protocol_sdk.constants.ChainId.BSC_MAINNET)
    * [`ChainId.BSC_TESTNET`](human_protocol_sdk.constants.md#human_protocol_sdk.constants.ChainId.BSC_TESTNET)
    * [`ChainId.CELO`](human_protocol_sdk.constants.md#human_protocol_sdk.constants.ChainId.CELO)
    * [`ChainId.CELO_ALFAJORES`](human_protocol_sdk.constants.md#human_protocol_sdk.constants.ChainId.CELO_ALFAJORES)
    * [`ChainId.GOERLI`](human_protocol_sdk.constants.md#human_protocol_sdk.constants.ChainId.GOERLI)
    * [`ChainId.LOCALHOST`](human_protocol_sdk.constants.md#human_protocol_sdk.constants.ChainId.LOCALHOST)
    * [`ChainId.MAINNET`](human_protocol_sdk.constants.md#human_protocol_sdk.constants.ChainId.MAINNET)
    * [`ChainId.MOONBASE_ALPHA`](human_protocol_sdk.constants.md#human_protocol_sdk.constants.ChainId.MOONBASE_ALPHA)
    * [`ChainId.MOONBEAM`](human_protocol_sdk.constants.md#human_protocol_sdk.constants.ChainId.MOONBEAM)
    * [`ChainId.POLYGON`](human_protocol_sdk.constants.md#human_protocol_sdk.constants.ChainId.POLYGON)
    * [`ChainId.POLYGON_AMOY`](human_protocol_sdk.constants.md#human_protocol_sdk.constants.ChainId.POLYGON_AMOY)
    * [`ChainId.POLYGON_MUMBAI`](human_protocol_sdk.constants.md#human_protocol_sdk.constants.ChainId.POLYGON_MUMBAI)
    * [`ChainId.RINKEBY`](human_protocol_sdk.constants.md#human_protocol_sdk.constants.ChainId.RINKEBY)
    * [`ChainId.SEPOLIA`](human_protocol_sdk.constants.md#human_protocol_sdk.constants.ChainId.SEPOLIA)
    * [`ChainId.XLAYER`](human_protocol_sdk.constants.md#human_protocol_sdk.constants.ChainId.XLAYER)
    * [`ChainId.XLAYER_TESTNET`](human_protocol_sdk.constants.md#human_protocol_sdk.constants.ChainId.XLAYER_TESTNET)
  * [`KVStoreKeys`](human_protocol_sdk.constants.md#human_protocol_sdk.constants.KVStoreKeys)
    * [`KVStoreKeys.fee`](human_protocol_sdk.constants.md#human_protocol_sdk.constants.KVStoreKeys.fee)
    * [`KVStoreKeys.job_types`](human_protocol_sdk.constants.md#human_protocol_sdk.constants.KVStoreKeys.job_types)
    * [`KVStoreKeys.public_key`](human_protocol_sdk.constants.md#human_protocol_sdk.constants.KVStoreKeys.public_key)
    * [`KVStoreKeys.registration_needed`](human_protocol_sdk.constants.md#human_protocol_sdk.constants.KVStoreKeys.registration_needed)
    * [`KVStoreKeys.role`](human_protocol_sdk.constants.md#human_protocol_sdk.constants.KVStoreKeys.role)
    * [`KVStoreKeys.url`](human_protocol_sdk.constants.md#human_protocol_sdk.constants.KVStoreKeys.url)
    * [`KVStoreKeys.webhook_url`](human_protocol_sdk.constants.md#human_protocol_sdk.constants.KVStoreKeys.webhook_url)
  * [`OrderDirection`](human_protocol_sdk.constants.md#human_protocol_sdk.constants.OrderDirection)
    * [`OrderDirection.ASC`](human_protocol_sdk.constants.md#human_protocol_sdk.constants.OrderDirection.ASC)
    * [`OrderDirection.DESC`](human_protocol_sdk.constants.md#human_protocol_sdk.constants.OrderDirection.DESC)
  * [`Role`](human_protocol_sdk.constants.md#human_protocol_sdk.constants.Role)
    * [`Role.exchange_oracle`](human_protocol_sdk.constants.md#human_protocol_sdk.constants.Role.exchange_oracle)
    * [`Role.job_launcher`](human_protocol_sdk.constants.md#human_protocol_sdk.constants.Role.job_launcher)
    * [`Role.recording_oracle`](human_protocol_sdk.constants.md#human_protocol_sdk.constants.Role.recording_oracle)
    * [`Role.reputation_oracle`](human_protocol_sdk.constants.md#human_protocol_sdk.constants.Role.reputation_oracle)
    * [`Role.validator`](human_protocol_sdk.constants.md#human_protocol_sdk.constants.Role.validator)
  * [`Status`](human_protocol_sdk.constants.md#human_protocol_sdk.constants.Status)
    * [`Status.Cancelled`](human_protocol_sdk.constants.md#human_protocol_sdk.constants.Status.Cancelled)
    * [`Status.Complete`](human_protocol_sdk.constants.md#human_protocol_sdk.constants.Status.Complete)
    * [`Status.Launched`](human_protocol_sdk.constants.md#human_protocol_sdk.constants.Status.Launched)
    * [`Status.Paid`](human_protocol_sdk.constants.md#human_protocol_sdk.constants.Status.Paid)
    * [`Status.Partial`](human_protocol_sdk.constants.md#human_protocol_sdk.constants.Status.Partial)
    * [`Status.Pending`](human_protocol_sdk.constants.md#human_protocol_sdk.constants.Status.Pending)
* [human_protocol_sdk.filter module](human_protocol_sdk.filter.md)
  * [`EscrowFilter`](human_protocol_sdk.filter.md#human_protocol_sdk.filter.EscrowFilter)
    * [`EscrowFilter.__init__()`](human_protocol_sdk.filter.md#human_protocol_sdk.filter.EscrowFilter.__init__)
  * [`FilterError`](human_protocol_sdk.filter.md#human_protocol_sdk.filter.FilterError)
  * [`PayoutFilter`](human_protocol_sdk.filter.md#human_protocol_sdk.filter.PayoutFilter)
    * [`PayoutFilter.__init__()`](human_protocol_sdk.filter.md#human_protocol_sdk.filter.PayoutFilter.__init__)
  * [`StatisticsFilter`](human_protocol_sdk.filter.md#human_protocol_sdk.filter.StatisticsFilter)
    * [`StatisticsFilter.__init__()`](human_protocol_sdk.filter.md#human_protocol_sdk.filter.StatisticsFilter.__init__)
  * [`TransactionFilter`](human_protocol_sdk.filter.md#human_protocol_sdk.filter.TransactionFilter)
    * [`TransactionFilter.__init__()`](human_protocol_sdk.filter.md#human_protocol_sdk.filter.TransactionFilter.__init__)
* [human_protocol_sdk.legacy_encryption module](human_protocol_sdk.legacy_encryption.md)
  * [`DecryptionError`](human_protocol_sdk.legacy_encryption.md#human_protocol_sdk.legacy_encryption.DecryptionError)
  * [`Encryption`](human_protocol_sdk.legacy_encryption.md#human_protocol_sdk.legacy_encryption.Encryption)
    * [`Encryption.CIPHER`](human_protocol_sdk.legacy_encryption.md#human_protocol_sdk.legacy_encryption.Encryption.CIPHER)
    * [`Encryption.ELLIPTIC_CURVE`](human_protocol_sdk.legacy_encryption.md#human_protocol_sdk.legacy_encryption.Encryption.ELLIPTIC_CURVE)
    * [`Encryption.KEY_LEN`](human_protocol_sdk.legacy_encryption.md#human_protocol_sdk.legacy_encryption.Encryption.KEY_LEN)
    * [`Encryption.MODE`](human_protocol_sdk.legacy_encryption.md#human_protocol_sdk.legacy_encryption.Encryption.MODE)
    * [`Encryption.PUBLIC_KEY_LEN`](human_protocol_sdk.legacy_encryption.md#human_protocol_sdk.legacy_encryption.Encryption.PUBLIC_KEY_LEN)
    * [`Encryption.decrypt()`](human_protocol_sdk.legacy_encryption.md#human_protocol_sdk.legacy_encryption.Encryption.decrypt)
    * [`Encryption.encrypt()`](human_protocol_sdk.legacy_encryption.md#human_protocol_sdk.legacy_encryption.Encryption.encrypt)
    * [`Encryption.generate_private_key()`](human_protocol_sdk.legacy_encryption.md#human_protocol_sdk.legacy_encryption.Encryption.generate_private_key)
    * [`Encryption.generate_public_key()`](human_protocol_sdk.legacy_encryption.md#human_protocol_sdk.legacy_encryption.Encryption.generate_public_key)
    * [`Encryption.is_encrypted()`](human_protocol_sdk.legacy_encryption.md#human_protocol_sdk.legacy_encryption.Encryption.is_encrypted)
  * [`InvalidPublicKey`](human_protocol_sdk.legacy_encryption.md#human_protocol_sdk.legacy_encryption.InvalidPublicKey)
* [human_protocol_sdk.utils module](human_protocol_sdk.utils.md)
  * [`get_contract_interface()`](human_protocol_sdk.utils.md#human_protocol_sdk.utils.get_contract_interface)
  * [`get_data_from_subgraph()`](human_protocol_sdk.utils.md#human_protocol_sdk.utils.get_data_from_subgraph)
  * [`get_erc20_interface()`](human_protocol_sdk.utils.md#human_protocol_sdk.utils.get_erc20_interface)
  * [`get_escrow_interface()`](human_protocol_sdk.utils.md#human_protocol_sdk.utils.get_escrow_interface)
  * [`get_factory_interface()`](human_protocol_sdk.utils.md#human_protocol_sdk.utils.get_factory_interface)
  * [`get_hmt_balance()`](human_protocol_sdk.utils.md#human_protocol_sdk.utils.get_hmt_balance)
  * [`get_kvstore_interface()`](human_protocol_sdk.utils.md#human_protocol_sdk.utils.get_kvstore_interface)
  * [`get_reward_pool_interface()`](human_protocol_sdk.utils.md#human_protocol_sdk.utils.get_reward_pool_interface)
  * [`get_staking_interface()`](human_protocol_sdk.utils.md#human_protocol_sdk.utils.get_staking_interface)
  * [`handle_transaction()`](human_protocol_sdk.utils.md#human_protocol_sdk.utils.handle_transaction)
  * [`parse_transfer_transaction()`](human_protocol_sdk.utils.md#human_protocol_sdk.utils.parse_transfer_transaction)
  * [`validate_url()`](human_protocol_sdk.utils.md#human_protocol_sdk.utils.validate_url)
  * [`with_retry()`](human_protocol_sdk.utils.md#human_protocol_sdk.utils.with_retry)
