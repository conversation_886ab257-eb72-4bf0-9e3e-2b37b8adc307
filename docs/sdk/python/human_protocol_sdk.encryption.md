# human_protocol_sdk.encryption package

Encryption utility to encrypt/decrypt and sign/verify messages and secure protocol.

## Submodules

* [human_protocol_sdk.encryption.encryption module](human_protocol_sdk.encryption.encryption.md)
  * [Code Example](human_protocol_sdk.encryption.encryption.md#code-example)
  * [Module](human_protocol_sdk.encryption.encryption.md#module)
  * [`Encryption`](human_protocol_sdk.encryption.encryption.md#human_protocol_sdk.encryption.encryption.Encryption)
    * [`Encryption.__init__()`](human_protocol_sdk.encryption.encryption.md#human_protocol_sdk.encryption.encryption.Encryption.__init__)
    * [`Encryption.decrypt()`](human_protocol_sdk.encryption.encryption.md#human_protocol_sdk.encryption.encryption.Encryption.decrypt)
    * [`Encryption.sign()`](human_protocol_sdk.encryption.encryption.md#human_protocol_sdk.encryption.encryption.Encryption.sign)
    * [`Encryption.sign_and_encrypt()`](human_protocol_sdk.encryption.encryption.md#human_protocol_sdk.encryption.encryption.Encryption.sign_and_encrypt)
* [human_protocol_sdk.encryption.encryption_utils module](human_protocol_sdk.encryption.encryption_utils.md)
  * [Code Example](human_protocol_sdk.encryption.encryption_utils.md#code-example)
  * [Module](human_protocol_sdk.encryption.encryption_utils.md#module)
  * [`EncryptionUtils`](human_protocol_sdk.encryption.encryption_utils.md#human_protocol_sdk.encryption.encryption_utils.EncryptionUtils)
    * [`EncryptionUtils.encrypt()`](human_protocol_sdk.encryption.encryption_utils.md#human_protocol_sdk.encryption.encryption_utils.EncryptionUtils.encrypt)
    * [`EncryptionUtils.get_signed_data()`](human_protocol_sdk.encryption.encryption_utils.md#human_protocol_sdk.encryption.encryption_utils.EncryptionUtils.get_signed_data)
    * [`EncryptionUtils.is_encrypted()`](human_protocol_sdk.encryption.encryption_utils.md#human_protocol_sdk.encryption.encryption_utils.EncryptionUtils.is_encrypted)
    * [`EncryptionUtils.verify()`](human_protocol_sdk.encryption.encryption_utils.md#human_protocol_sdk.encryption.encryption_utils.EncryptionUtils.verify)
