# human_protocol_sdk.operator package

This module enables to perform actions on staking contracts and
obtain staking information from both the contracts and subgraph.

## Submodules

* [human_protocol_sdk.operator.operator_utils module](human_protocol_sdk.operator.operator_utils.md)
  * [Code Example](human_protocol_sdk.operator.operator_utils.md#code-example)
  * [Module](human_protocol_sdk.operator.operator_utils.md#module)
  * [`LeaderData`](human_protocol_sdk.operator.operator_utils.md#human_protocol_sdk.operator.operator_utils.LeaderData)
    * [`LeaderData.__init__()`](human_protocol_sdk.operator.operator_utils.md#human_protocol_sdk.operator.operator_utils.LeaderData.__init__)
  * [`LeaderFilter`](human_protocol_sdk.operator.operator_utils.md#human_protocol_sdk.operator.operator_utils.LeaderFilter)
    * [`LeaderFilter.__init__()`](human_protocol_sdk.operator.operator_utils.md#human_protocol_sdk.operator.operator_utils.LeaderFilter.__init__)
  * [`Operator`](human_protocol_sdk.operator.operator_utils.md#human_protocol_sdk.operator.operator_utils.Operator)
    * [`Operator.__init__()`](human_protocol_sdk.operator.operator_utils.md#human_protocol_sdk.operator.operator_utils.Operator.__init__)
  * [`OperatorUtils`](human_protocol_sdk.operator.operator_utils.md#human_protocol_sdk.operator.operator_utils.OperatorUtils)
    * [`OperatorUtils.get_leader()`](human_protocol_sdk.operator.operator_utils.md#human_protocol_sdk.operator.operator_utils.OperatorUtils.get_leader)
    * [`OperatorUtils.get_leaders()`](human_protocol_sdk.operator.operator_utils.md#human_protocol_sdk.operator.operator_utils.OperatorUtils.get_leaders)
    * [`OperatorUtils.get_reputation_network_operators()`](human_protocol_sdk.operator.operator_utils.md#human_protocol_sdk.operator.operator_utils.OperatorUtils.get_reputation_network_operators)
    * [`OperatorUtils.get_rewards_info()`](human_protocol_sdk.operator.operator_utils.md#human_protocol_sdk.operator.operator_utils.OperatorUtils.get_rewards_info)
  * [`OperatorUtilsError`](human_protocol_sdk.operator.operator_utils.md#human_protocol_sdk.operator.operator_utils.OperatorUtilsError)
  * [`RewardData`](human_protocol_sdk.operator.operator_utils.md#human_protocol_sdk.operator.operator_utils.RewardData)
    * [`RewardData.__init__()`](human_protocol_sdk.operator.operator_utils.md#human_protocol_sdk.operator.operator_utils.RewardData.__init__)
