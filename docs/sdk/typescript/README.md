**@human-protocol/sdk** • [**Docs**](modules.md)

***

<p align="center">
  <a href="https://www.humanprotocol.org/" target="blank"><img src="https://s2.coinmarketcap.com/static/img/coins/64x64/10347.png" width="100" alt="Human Protocol" /></a>
</p>

[circleci-image]: https://img.shields.io/circleci/build/github/nestjs/nest/master?token=abc123def456
[circleci-url]: https://circleci.com/gh/nestjs/nest

<h1 align="center">Human Protocol Node.js SDK</h1>
<p align="center">Node.js SDK to launch/manage escrows on <a href="https://www.humanprotocol.org/">Human Protocol</a>
</p>

<p align="center">
  <a href="https://github.com/humanprotocol/human-protocol/actions/workflows/ci-test-node-sdk.yaml">
    <img src="https://github.com/humanprotocol/human-protocol/actions/workflows/ci-test-node-sdk.yaml/badge.svg?branch=main" alt="Node SDK Check">
  </a>
  <a href="https://github.com/humanprotocol/human-protocol/actions/workflows/cd-node-sdk.yaml">
    <img src="https://github.com/humanprotocol/human-protocol/actions/workflows/cd-node-sdk.yaml/badge.svg?event=release" alt="Node SDK deployment">
  </a>
</p>

## Installation

This SDK is available on [NPM](https://www.npmjs.com/package/@human-protocol/sdk).

    yarn add @human-protocol/sdk

## Documentation

For detailed information about core, please refer to the [Human Protocol Docs](https://sdk.humanprotocol.org/).

## License

This project is licensed under the MIT License. See the [LICENSE](https://github.com/humanprotocol/human-protocol/blob/main/LICENSE) file for details.
